# -*- coding: utf-8 -*-
from nt import startfile
import os
import shutil
from time import sleep
import time

from airtest_selenium.proxy import WebChrome
from selenium import webdriver
from selenium.webdriver.common.by import By
import wx
import xlrd


class LoginFrame(wx.Frame):
    
    def __init__(self):
        self.path = os.path.abspath(os.path.join(os.path.dirname(__file__),"..\.."))
        current_path = os.path.dirname(__file__)
        doc_current_path = os.path.join(current_path,"doc")
        # 判断login.xlsx是否存在
        workbook = None
        try:
            file_path = os.path.join(current_path, 'login.xls')
            doc_file_path = os.path.join(doc_current_path, 'login.xls')
            if os.path.isfile(file_path):
                self.excel_path = "同一目录"
                workbook = xlrd.open_workbook(file_path)
            elif os.path.isfile(doc_file_path):
                self.excel_path = "doc下"
                workbook = xlrd.open_workbook(doc_file_path)
            else:
                pass
        except:
            pass
        if workbook is not None:
            self.fkw_sheet = workbook.sheets()[0]
            self.oem_sheet = workbook.sheets()[1]
            self.other_sheet = workbook.sheets()[2]
            self.emlment_sheet = workbook.sheets()[3]

        wx.Frame.__init__(self, None, title=u"凡科登录",size = (760,430),pos = (600,400))
        panel = wx.Panel(self)
        self.icon = wx.Icon(u'img\\all.png', wx.BITMAP_TYPE_PNG)
        self.SetIcon(self.icon)  
        self.Label1 = wx.StaticText(panel, -1, u"环境",pos=(247,13))
        self.Label2 = wx.StaticText(panel, -1, u"直销/OEM",pos=(138,13))
        self.Label3 = wx.StaticText(panel, -1, u"A/B",pos=(348,13))
        self.Label4 = wx.StaticText(panel, -1, u"帐号",pos=(457,13))
        self.Label5 = wx.StaticText(panel, -1, u"员工帐号",pos=(568,13))
        self.Label5 = wx.StaticText(panel, -1, u"PC/商家助手",pos=(28,13))
        
        '''-------------------网站登录/商家助手登录选择-----------------------------------------'''
        self.sampleList_is_mallm = [u'PC后台登录',u"商家助手登录"]
        self.select_is_mallm=wx.Choice(panel, -1, size=(90, -1), choices=self.sampleList_is_mallm,pos=(20,40))
        self.Bind(wx.EVT_CHOICE, self.svr , self.select_is_mallm)
        self.select_is_mallm.SetSelection(0)
        
        #直销OEM选择
        self.sampleListx = [u'直销帐号',u"OEM帐号"]
        self.selectx=wx.Choice(panel, -1, size=(75, -1), choices=self.sampleListx,pos=(130,40))
        self.Bind(wx.EVT_CHOICE, self.svr, self.selectx)
        self.selectx.SetSelection(0)
        
        #直销环境选择
        self.sampleList1=[u'本地环境', u'独立环境', u'dep3环境', u'线上环境']
        self.select1=wx.Choice(panel, -1, size=(78, -1), choices=self.sampleList1,pos=(226,40))
        self.Bind(wx.EVT_CHOICE, self.svr, self.select1)
        self.select1.SetSelection(2) 
        
        #直销类型选择      
        self.sampleList2 = [u'-',u'A类帐号',u"B类帐号"]
        self.select2=wx.Choice(panel, -1, size=(75, -1), choices=self.sampleList2,pos=(323,40))
        self.Bind(wx.EVT_CHOICE, self.svr, self.select2)
        self.select2.SetSelection(0)

        #直销帐号选择
        self.sampleList3 = self.moren_name()
        self.select3=wx.Choice(panel, -1, size=(120, -1), choices=self.sampleList3,pos=(418,40))
        self.Bind(wx.EVT_CHOICE, self.Tip, self.select3)
        #下拉框默认值
        self.select3.SetSelection(0)
        #员工帐号选择
        self.stafftList = self.moren_staff()
        self.select4=wx.Choice(panel, -1, size=(90, -1), choices=self.stafftList,pos=(556,40))
        self.Bind(wx.EVT_CHOICE, self.Tip, self.select4)
#         self.select4.SetSelection(0)
        #登录按钮
        self.login1= wx.Button(panel, -1, u"登录",pos=(664,40), size = (60,26))
        self.Bind(wx.EVT_BUTTON, self.jz, self.login1)
        '''------------------------------------------------------------------'''
        
        #查看Excel
        bitmap4 = wx.Image(u"img\\look.png",wx.BITMAP_TYPE_PNG).ConvertToBitmap()
        self.jpg = wx.BitmapButton(panel,-1, bitmap4,pos=(568,97),size =(30,30))
        self.Bind(wx.EVT_BUTTON, self.openf , self.jpg)
        self.jpg.SetToolTip(u"打开Excel")
        
        '''----------------------------开发环境登录--------------------------------------'''
        #开发环境
        self.labe5 = wx.StaticText(panel, -1, u"开发环境:",pos=(30,100))
        dev_acct = self.other_sheet.col(1)[20].value
        self.dev = wx.TextCtrl(panel, value= dev_acct, pos = (100,100), size = (100,-1))
        #账号类型选择      
        self.accountTypeList = [u'-',u'A类帐号',u"B类帐号"]
        self.accountTypeSelect=wx.Choice(panel, -1, size=(75, -1), choices=self.accountTypeList,pos=(220,100))
        self.Bind(wx.EVT_CHOICE, self.devSvr , self.accountTypeSelect)
        self.accountTypeSelect.SetSelection(0)
        #直销帐号选择
        self.accountList = self.dev_moren_name()
        self.accountSelect=wx.Choice(panel, -1, size=(130, -1), choices=self.accountList,pos=(320,100))
        self.Bind(wx.EVT_CHOICE, self.Tip, self.accountSelect)
        #下拉框默认值
        self.accountSelect.SetSelection(0)   
        #登录按钮
        self.devLogin= wx.Button(panel, -1, u"登录",pos=(475,98))
        self.Bind(wx.EVT_BUTTON, self.devjz , self.devLogin)
        
        
        '''----------------------输入帐号密码登录--------------------------------------'''
        Accountbox = wx.StaticBox(panel, -1,u"帐号密码登录",pos = (20,220),size = (710,75)) 
        #直销环境选择
        self.sampleList1=[u'本地环境', u'独立环境', u'dep3环境', u'线上环境']
        #self.sampleList1 = [u'本地环境', u'dep3环境', u'线上环境']
        self.envir_select=wx.Choice(panel, -1, size=(80, -1), choices=self.sampleList1,pos=(30,250))
        self.Bind(wx.EVT_CHOICE, self.svr , self.envir_select)
         
        self.envir_select.SetSelection(2) 
      
        #直销OEM选择
        self.sampleListx = [u'直销帐号',u"OEM帐号"]
        self.type_select=wx.Choice(panel, -1, size=(80, -1), choices=self.sampleListx,pos=(130,250))
        self.Bind(wx.EVT_CHOICE, self.svr , self.type_select)
        self.type_select.SetSelection(0)
        
        self.labeaccount = wx.StaticText(panel, -1, u"帐号:",pos=(220,252))
        self.accountInput = wx.TextCtrl(panel, pos = (250,250))
        #self.Bind(wx.EVT_TEXT, self.getValue , self.accountInput)
        self.labestaff = wx.StaticText(panel, -1, u"员工:",pos=(365,252))
        self.staffInput = wx.TextCtrl(panel, pos = (395,250))
        #self.Bind(wx.EVT_TEXT, self.getValue , self.staffInput)
        self.labepwd = wx.StaticText(panel, -1, u"密码:",pos=(510,252))
        self.pwdInput = wx.TextCtrl(panel, pos = (540,250))
        #self.Bind(wx.EVT_TEXT, self.getValue , self.pwdInput)
        self.loginInput= wx.Button(panel, -1, u"登录",pos=(665,250),size = (50,26))
        self.loginInput.SetToolTip(u"帐号密码登录")
        self.Bind(wx.EVT_BUTTON, self.login_input, self.loginInput)
        
        #浏览器选择               
        lblList = [u'Chrome', u'Firefox', u'Ie']           
        self.rbox = wx.RadioBox(panel, label = u'浏览器选择', pos = (20,150), choices = lblList,
                                majorDimension = 1, style = wx.RA_SPECIFY_ROWS)
        #克隆帐号
        clonebox = wx.StaticBox(panel, -1,u"克隆帐号",pos = (260,150),size = (190,58)) 
        self.clone = wx.TextCtrl(panel, pos = (270,175))
        self.login2= wx.Button(panel, -1, u"GO",pos=(390,175),size = (50,26))
        self.login2.SetToolTip(u"登录克隆帐号")
        self.Bind(wx.EVT_BUTTON, self.login_clone , self.login2)
        #帐号备注
        self.tip = wx.TextCtrl(panel, pos = (475,150),size = (140,58),style =wx.TE_READONLY | wx.TE_MULTILINE,value = "备注:")
        self.moren_tip()
        
        '''----------------------代理平台登录--------------------------------------'''
        agency_box = wx.StaticBox(panel, -1,u"代理商平台登录",pos = (20,300),size = (710,70)) 
        #环境选择
        self.sampleList_agency=[u'本地环境', u'dep3环境', u'线上环境']
        self.select_agency=wx.Choice(panel, -1, size=(80, -1), choices=self.sampleList_agency,pos=(30,330))
        self.Bind(wx.EVT_CHOICE, self.get_agency_input , self.select_agency)  
        self.select_agency.SetSelection(2) 
        
        self.labe_agency_account = wx.StaticText(panel, -1, u"代理商帐号:",pos=(128,332))
        self.agency_account_input = wx.TextCtrl(panel,pos = (205,330))

        self.labe_agency_pwd = wx.StaticText(panel, -1, u"密码:",pos=(333,332))
        self.agency_pwd_input = wx.TextCtrl(panel, pos = (368,330))
        
        self.agency_login_btn= wx.Button(panel, -1, u"登录",pos=(498,330),size = (60,26))
        self.agency_login_btn.SetToolTip(u"代理商登录")
        self.Bind(wx.EVT_BUTTON, self.agency_login , self.agency_login_btn)
        self.moren_agency_input()
         
        self.Bind(wx.EVT_CLOSE, self.OnClose)  
        panel.Fit() 
        self.Show(True)
        

    '''------------------------------------------------------'''
    def get_element(self):
        '''获取登录页面元素'''
        #获取直销旧版登录页面元素
        fkw_account = [self.emlment_sheet.row(1)[1].value, self.emlment_sheet.row(1)[2].value]
        fkw_pwd = [self.emlment_sheet.row(3)[1].value, self.emlment_sheet.row(3)[2].value]
        fkw_login = [self.emlment_sheet.row(5)[1].value, self.emlment_sheet.row(5)[2].value]
        
        #获取直销新版登录页面元素    
        fkw_account_new = [self.emlment_sheet.row(2)[1].value, self.emlment_sheet.row(2)[2].value]
        fkw_pwd_new = [self.emlment_sheet.row(4)[1].value, self.emlment_sheet.row(4)[2].value]
        fkw_login_new = [self.emlment_sheet.row(6)[1].value, self.emlment_sheet.row(6)[2].value]
        fkw_staff = [self.emlment_sheet.row(7)[1].value, self.emlment_sheet.row(7)[2].value]
        fkw_staff_check = [self.emlment_sheet.row(8)[1].value, self.emlment_sheet.row(8)[2].value]
        #获取OEM登录页面元素
        oem_account = [self.emlment_sheet.row(11)[1].value, self.emlment_sheet.row(11)[2].value]
        oem_staff = [self.emlment_sheet.row(12)[1].value, self.emlment_sheet.row(12)[2].value]
        oem_pwd = [self.emlment_sheet.row(13)[1].value, self.emlment_sheet.row(13)[2].value]
        oem_staff_check = [self.emlment_sheet.row(14)[1].value, self.emlment_sheet.row(14)[2].value]
        oem_login = [self.emlment_sheet.row(15)[1].value,self.emlment_sheet.row(15)[2].value]
        
        agency_login_type = [self.emlment_sheet.row(18)[1].value,self.emlment_sheet.row(18)[2].value]
        agency_account = [self.emlment_sheet.row(19)[1].value,self.emlment_sheet.row(19)[2].value]
        agency_pwd = [self.emlment_sheet.row(20)[1].value,self.emlment_sheet.row(20)[2].value]
        agency_login = [self.emlment_sheet.row(21)[1].value,self.emlment_sheet.row(21)[2].value]
        
        fkw_mallm_account = [self.emlment_sheet.row(24)[1].value, self.emlment_sheet.row(24)[2].value]
        fkw_mallm_staff = [self.emlment_sheet.row(25)[1].value, self.emlment_sheet.row(25)[2].value]
        fkw_mallm_pwd = [self.emlment_sheet.row(26)[1].value, self.emlment_sheet.row(26)[2].value]
        fkw_mallm_staff_check = [self.emlment_sheet.row(27)[1].value, self.emlment_sheet.row(27)[2].value]
        fkw_mallm_login = [self.emlment_sheet.row(28)[1].value, self.emlment_sheet.row(28)[2].value]
        
        oem_mallm_account = [self.emlment_sheet.row(31)[1].value, self.emlment_sheet.row(31)[2].value]
        oem_mallm_staff = [self.emlment_sheet.row(32)[1].value, self.emlment_sheet.row(32)[2].value]
        oem_mallm_pwd = [self.emlment_sheet.row(33)[1].value, self.emlment_sheet.row(33)[2].value]
        oem_mallm_login = [self.emlment_sheet.row(34)[1].value, self.emlment_sheet.row(34)[2].value]
        
        #返回各元素
        return fkw_account,fkw_pwd,fkw_login,fkw_account_new,fkw_pwd_new,fkw_login_new,oem_account,oem_staff,oem_pwd,oem_login,fkw_staff,fkw_staff_check,oem_staff_check,agency_login_type,agency_account,agency_pwd,agency_login, fkw_mallm_account, fkw_mallm_staff, fkw_mallm_pwd, fkw_mallm_staff_check, fkw_mallm_login, oem_mallm_account, oem_mallm_staff, oem_mallm_pwd, oem_mallm_login


    def is_loginPage_new(self):
        '''判断直销新旧版登录页面'''
        flag=True   
        #调用函数获取页面元素
        element = self.get_element()
        try:
            self.driver.find_element(*element[3])
            return flag        
        except:
            flag=False
            return flag
    
    def task_kill(self):
        #结束驱动进程
        if self.rbox.GetStringSelection()=="Chrome":   
            os.system('@taskkill /IM chrome.exe /F')
        if self.rbox.GetStringSelection()=="Firefox":                 
            os.system('@taskkill>nul 2>nul /IM geckodriver.exe /F')
        if self.rbox.GetStringSelection()=="Ie": 
            os.system('@taskkill>nul 2>nul /IM IEDriverServer.exe /F')

   
    def fkw_login(self, account, pwd, staff="",staffPwd=""):
        '''直销登录 '''
        flag = self.is_loginPage_new()
        #调用函数获取页面元素
        element = self.get_element()
        #新版登录页面
        if flag:
            self.driver.find_element(*element[3]).clear()
            self.driver.find_element(*element[3]).send_keys(account)
            if staff =="" or staff == "boss":
                self.driver.find_element(*element[4]).clear()
                self.driver.find_element(*element[4]).send_keys(pwd)
            else:
                self.driver.find_element(*element[11]).click() 
                self.driver.find_element(*element[10]).clear()
                self.driver.find_element(*element[10]).send_keys(staff)             
                self.driver.find_element(*element[4]).clear()
                self.driver.find_element(*element[4]).send_keys(staffPwd)
            self.driver.find_element(*element[5]).click()
        #旧版登录页面
        else:
            self.driver.find_element(*element[0]).clear()
            self.driver.find_element(*element[0]).send_keys(account)
            self.driver.find_element(*element[1]).clear()
            self.driver.find_element(*element[1]).send_keys(pwd)
            self.driver.find_element(*element[2]).click() 
        sleep(2)  
        
        
    def oem_login(self, oem_account, oem_pwd,staff="",staffPwd=""):
        '''oem输入账号密码并登录'''
        #调用函数获取页面元素
        element = self.get_element()
        self.driver.find_element(*element[6]).clear()
        self.driver.find_element(*element[6]).send_keys(oem_account)
        if staff =="" or staff == "boss":
            self.driver.find_element(*element[8]).clear()
            self.driver.find_element(*element[8]).send_keys(oem_pwd)
        else:
            self.driver.find_element(*element[12]).click() 
            self.driver.find_element(*element[7]).clear()
            self.driver.find_element(*element[7]).send_keys(staff)             
            self.driver.find_element(*element[8]).clear()
            self.driver.find_element(*element[8]).send_keys(staffPwd)  
        self.driver.find_element(*element[9]).click()
        sleep(2) 
        
    def close_pop(self):
        '''关闭登录后的弹窗   '''
        try:
            #定位弹窗的关闭按钮
            bbb = self.driver.find_elements(By.CLASS_NAME,"close")
            ccc = self.driver.find_elements(By.CLASS_NAME,"btnClose") 
            #如果登录后弹窗则点击关闭弹窗
            if len(bbb)!=0:
                self.driver.find_element(By.CLASS_NAME,"close").click()
            if len(ccc)!=0:
                self.driver.find_element(By.CLASS_NAME,"btnClose").click()
        except:
            pass
            
    
    def fkw_mallm_login(self,account, pwd, staff="", staffPwd=""):
        '''直销商家助手登录'''
        #调用函数获取页面元素
        element = self.get_element()
        self.driver.find_element(*element[17]).clear()
        self.driver.find_element(*element[17]).send_keys(account)
        if staff =="" or staff == "boss":
            self.driver.find_element(*element[19]).clear()
            self.driver.find_element(*element[19]).send_keys(pwd)
        else:
            self.driver.find_element(*element[20]).click() 
            self.driver.find_element(*element[18]).clear()
            self.driver.find_element(*element[18]).send_keys(staff)             
            self.driver.find_element(*element[19]).clear()
            self.driver.find_element(*element[19]).send_keys(staffPwd)  
        self.driver.find_element(*element[21]).click()
        sleep(2) 
        
    def oem_mallm_login(self,oem_account, oem_pwd, staff="", staffPwd="",oem_staff_name=None):
        '''oem商家助手登录'''
        #调用函数获取页面元素
        element = self.get_element()
        self.driver.find_element(*element[22]).clear()
        self.driver.find_element(*element[22]).send_keys(oem_account)
        self.driver.find_element(*element[23]).clear()
        self.driver.find_element(*element[23]).send_keys(staff)   

        if staff =="" or staff == "boss":
            self.driver.find_element(*element[24]).clear()
            self.driver.find_element(*element[24]).send_keys(oem_pwd)
        else:   
            self.driver.find_element(*element[24]).clear()
            self.driver.find_element(*element[24]).send_keys(staffPwd)  
        self.driver.find_element(*element[25]).click()
        sleep(2) 


    def login_clone(self,evt):
        '''登录克隆账号'''
        leixing = self.select1.GetStringSelection()
        clone_name = self.clone.GetValue()
        if clone_name!="":
            if leixing =="本地环境" or leixing =="独立环境":
                dlg = wx.MessageDialog(self, '克隆的帐号不支持在本地环境登录','出错了',style = wx.ICON_ERROR)
                dlg.ShowModal()
                dlg.Destroy()
            else: 
                if leixing =="线上环境":
                    url =self.other_sheet.col(1)[1].value
                else:
                    url =self.other_sheet.col(1)[2].value
                self.browser()
                self.driver.get(url)
                #调用函数传参并登录
                self.fkw_login(clone_name,"faisco1234")
                #调用关闭弹窗函数关闭登录后的弹窗
                self.close_pop() 
   
    def login_input(self,evt):
        '''输入账号密码'''
        zhifen = self.type_select.GetStringSelection()
        leixing = self.envir_select.GetStringSelection()
        account = self.accountInput.GetValue()
        staff = self.staffInput.GetValue()
        pwd = self.pwdInput.GetValue()
        self.browser()
        if zhifen =="直销帐号":      
            if leixing =="线上环境":
                url =self.other_sheet.col(1)[1].value
            elif leixing=="dep3环境":
                url =self.other_sheet.col(1)[2].value
            elif leixing=="本地环境":
                url =self.other_sheet.col(1)[3].value
            else:
                url =self.other_sheet.col(1)[16].value
            self.driver.get(url)
            #调用函数传参并登录
            self.fkw_login(account,pwd,staff,pwd)
 
        if zhifen =="OEM帐号":
            if leixing =="线上环境":
                url =self.other_sheet.col(1)[5].value
            elif leixing=="dep3环境":
                url =self.other_sheet.col(1)[6].value
            else:
                url =self.other_sheet.col(1)[7].value
            self.driver.get(url)
            self.oem_login(account, pwd,staff,pwd)
            
        #调用关闭弹窗函数关闭登录后的弹窗
        self.close_pop()           
        
    def agency_login(self,evt):
        '''代理商平台登录'''
        #调用函数获取页面元素
        element = self.get_element()
        
        leixing = self.select_agency.GetStringSelection()
        if leixing =="线上环境":
            url =self.other_sheet.col(1)[22].value
        elif leixing=="dep3环境":
            url =self.other_sheet.col(1)[23].value
        else:
            # 本地环境
            url =self.other_sheet.col(1)[24].value
               
        account = self.agency_account_input.GetValue()
        pwd = self.agency_pwd_input.GetValue()
        
        self.browser()
        self.driver.get(url)
        self.driver.find_element(*element[13]).click()
        time.sleep(0.2)
        self.driver.find_element(*element[14]).clear()
        self.driver.find_element(*element[14]).send_keys(account)
        self.driver.find_element(*element[15]).clear()
        self.driver.find_element(*element[15]).send_keys(pwd)
        self.driver.find_element(*element[16]).click()
         
#浏览器选择       
    def browser(self):#谷歌浏览器
        try:
            if self.rbox.GetStringSelection()=="Chrome":
                chrome_options = webdriver.ChromeOptions()
                chrome_options.add_experimental_option('excludeSwitches', ['enable-automation']) 
                prefs = {"profile.cookie_controls_mode": 1,"credentials_enable_service": False, "profile.password_manager_enabled": False}
                chrome_options.add_experimental_option("prefs", prefs)
                self.driver = WebChrome(chrome_options=chrome_options)
                self.driver.maximize_window()
            #火狐浏览器
            elif self.rbox.GetStringSelection()=="Firefox":
                self.driver = webdriver.Firefox()
            #ie浏览器
            else:
                self.driver = webdriver.Ie()
        except:
            dlg = wx.MessageDialog(self, '%s浏览器出现问题'%self.rbox.GetStringSelection(),'出错了',style = wx.ICON_ERROR)
            dlg.ShowModal()
            dlg.Destroy()                        
            
#帐号备注
    def Tip(self,evt):
        leixing = self.select1.GetStringSelection()
        zhifen = self.selectx.GetStringSelection()
        name = self.select3.GetStringSelection()
        if name =="":
            self.tip.SetValue("无该类型帐号")
        else:
            if zhifen =="直销帐号":

                for i in range(self.fkw_sheet.nrows-1):
                    if leixing != "本地环境" or leixing != "独立环境":
                        if self.fkw_sheet.col(1)[i+1].value=="线上" and self.fkw_sheet.col(3)[i+1].value==name:
                            if self.fkw_sheet.col(5)[i+1].value !="":
                                self.tip.SetValue(self.fkw_sheet.col(5)[i+1].value)
                            else:
                                self.tip.SetValue("该帐号无备注")
                    else:
                        if self.fkw_sheet.col(1)[i+1].value=="本地" and self.fkw_sheet.col(3)[i+1].value==name:
                            if self.fkw_sheet.col(5)[i+1].value:
                                print(self.fkw_sheet.col(5)[i+1].value)
                                self.tip.SetValue(self.fkw_sheet.col(5)[i+1].value)
                            else:
                                self.tip.SetValue("该帐号无备注") 
            else:
                # 分销帐号
                if leixing != "本地环境" or leixing!="独立环境":
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(1)[i+1].value=="线上" and self.oem_sheet.col(3)[i+1].value==name:
                            if self.oem_sheet.col(6)[i+1].value != "":
                                self.tip.SetValue(self.oem_sheet.col(6)[i+1].value)
                            else:
                                self.tip.SetValue("该帐号无备注")
                else:
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(1)[i+1].value=="本地" and self.oem_sheet.col(3)[i+1].value==name:
                            if self.oem_sheet.col(6)[i+1].value != "":
                                self.tip.SetValue(self.oem_sheet.col(6)[i+1].value)
                            else:
                                self.tip.SetValue("该帐号无备注")       
#------------------------------------------------------------------------------------------------------
#直销控制环境        
    def svr(self, evt):
        leixing = self.select1.GetStringSelection()
        ab = self.select2.GetStringSelection()
        zhifen = self.selectx.GetStringSelection()
        a=[]
        staff = []
        if zhifen =="直销帐号":
            if leixing !="本地环境" and leixing !="独立环境":
                if ab == "-":
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                elif ab == "A类帐号":
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(2)[i+1].value=="A" and self.fkw_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                else:
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(2)[i+1].value=="B" and self.fkw_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                self.select3.Set(a)
                self.select3.SetSelection(0)
                # 员工帐号
                for i in range(self.fkw_sheet.nrows-1):
                    if self.fkw_sheet.col(1)[i+1].value=="线上" :
                            cell_staff = self.fkw_sheet.col(6)[i+1].value
                            if cell_staff!="":
                                staff.append(cell_staff)
                            i+=1
                self.select4.Set(staff)
                self.select4.SetSelection(0)
            #本地/独立环境    
            else:
                if ab == "-":
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                elif ab == "A类帐号":
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(2)[i+1].value=="A" and self.fkw_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                else:
                    for i in range(self.fkw_sheet.nrows-1):
                        if self.fkw_sheet.col(2)[i+1].value=="B" and self.fkw_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.fkw_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                self.select3.Set(a)
                self.select3.SetSelection(0)
            #获取员工帐号    
                for i in range(self.fkw_sheet.nrows-1):
                    if self.fkw_sheet.col(1)[i+1].value=="本地" :
                            cell_staff = self.fkw_sheet.col(6)[i+1].value
                            if cell_staff!="":
                                staff.append(cell_staff)
                            i+=1
                self.select4.Set(staff)
                self.select4.SetSelection(0)
        else:
            #"OEM帐号"
            # 线上帐号
            if leixing !="本地环境" and leixing !="独立环境":
                if ab == "-":
                    # 全部帐号
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value  
                            a.append(cell_1)
                            i+=1
                elif ab =="A类帐号":
                    # A类帐号
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(2)[i+1].value=="A" and self.oem_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value  
                            a.append(cell_1)
                            i+=1
                else:
                    # B类帐号
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(2)[i+1].value=="B" and self.oem_sheet.col(1)[i+1].value=="线上" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value 
                            a.append(cell_1)
                            i+=1
                self.select3.Set(a)
                self.select3.SetSelection(0)
                #员工帐号
                for i in range(self.oem_sheet.nrows-1):
                    if self.oem_sheet.col(1)[i+1].value=="线上" :
                        cell_staff = self.oem_sheet.col(7)[i+1].value 
                        if cell_staff!="":
                                staff.append(cell_staff)
                        i+=1
                self.select4.Set(staff)
                self.select4.SetSelection(0)
            # 本地帐号
            else:
                if ab =="-":
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                
                elif ab =="A类帐号":
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(2)[i+1].value=="A" and self.oem_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                else:
                    for i in range(self.oem_sheet.nrows-1):
                        if self.oem_sheet.col(2)[i+1].value=="B" and self.oem_sheet.col(1)[i+1].value=="本地" :
                            cell_1 = self.oem_sheet.col(3)[i+1].value
                            a.append(cell_1)
                            i+=1
                self.select3.Set(a)
                self.select3.SetSelection(0)
            #获取员工帐号    
                for i in range(self.oem_sheet.nrows-1):
                    if self.oem_sheet.col(1)[i+1].value=="本地" :
                        cell_staff = self.oem_sheet.col(7)[i+1].value 
                        if cell_staff!="":
                                staff.append(cell_staff)
                        i+=1
                self.select4.Set(staff)
                self.select4.SetSelection(0)
        self.Tip(evt)

        
    #开发环境选择账号
    def devSvr(self, evt):
            ab = self.accountTypeSelect.GetStringSelection()
            a=[]
            if ab == "-":
                for i in range(self.fkw_sheet.nrows-1):
                    if self.fkw_sheet.col(1)[i+1].value=="本地" :
                        cell_1 = self.fkw_sheet.col(3)[i+1].value
                        a.append(cell_1)
                        i+=1         
            elif ab == "A类帐号":
                for i in range(self.fkw_sheet.nrows-1):
                    if self.fkw_sheet.col(2)[i+1].value=="A" and self.fkw_sheet.col(1)[i+1].value=="本地" :
                        cell_1 = self.fkw_sheet.col(3)[i+1].value
                        a.append(cell_1)
                        i+=1
            else:
                for i in range(self.fkw_sheet.nrows-1):
                    if self.fkw_sheet.col(2)[i+1].value=="B" and self.fkw_sheet.col(1)[i+1].value=="本地" :
                        cell_1 = self.fkw_sheet.col(3)[i+1].value
                        a.append(cell_1)
                        i+=1
            self.accountSelect.Set(a)
            self.accountSelect.SetSelection(0)
            self.Tip(evt)
#------------------------------------------------------------------------------------------------------
#直销密码
    def pwd(self):        
        leixing = self.select1.GetStringSelection() 
        if leixing=="线上环境" or leixing=="dep3环境":
            lei = "线上"
        else:
            lei = '本地'
        name = self.select3.GetStringSelection() 
        staff = self.select4.GetStringSelection()   
        for i in range(self.fkw_sheet.nrows-1):
            excel_name = self.fkw_sheet.col(3)[i+1].value 
            excel_staff = self.fkw_sheet.col(6)[i+1].value           
            if self.fkw_sheet.col(1)[i+1].value==lei and excel_name==name:
                p = self.fkw_sheet.col(4)[i+1].value
                if isinstance(p, float):    
                    p = str(int(self.fkw_sheet.col(4)[i+1].value))
            if self.fkw_sheet.col(1)[i+1].value==lei and excel_staff==staff:
                staffPwd =self.fkw_sheet.col(7)[i+1].value
                if isinstance(staffPwd, float): 
                    staffPwd=str(int(self.fkw_sheet.col(7)[i+1].value))       
        return p,staffPwd
    
    def dev_pwd(self):        
        lei = '本地'
        name = self.accountSelect.GetStringSelection() 
        staff = self.select4.GetStringSelection()   
        for i in range(self.fkw_sheet.nrows-1):
            excel_name = self.fkw_sheet.col(3)[i+1].value 
            excel_staff = self.fkw_sheet.col(6)[i+1].value           
            if self.fkw_sheet.col(1)[i+1].value==lei and excel_name==name:
                p =self.fkw_sheet.col(4)[i+1].value
                if isinstance(p, float):                
                    p =str(int(self.fkw_sheet.col(4)[i+1].value))
            if self.fkw_sheet.col(1)[i+1].value==lei and excel_staff==staff:
                staffPwd =self.fkw_sheet.col(7)[i+1].value
                if isinstance(staffPwd, float): 
                    staffPwd=str(int(self.fkw_sheet.col(7)[i+1].value))      
        return p,staffPwd
#------------------------------------------------------------------------------------------------------
#OEM密码
    def pwd_fen(self):
        name =  self.select3.GetStringSelection()
        p1 =""
        leixing = self.select1.GetStringSelection()
        staff = self.select4.GetStringSelection()
        if leixing=="线上环境" or leixing=="dep3环境":
            lei = "线上"
        else:
            lei = '本地'
        for i in range(self.oem_sheet.nrows-1):
            exclename = self.oem_sheet.col(3)[i+1].value
            excel_staff = self.oem_sheet.col(7)[i+1].value
            if self.oem_sheet.col(1)[i+1].value==lei and exclename==name:        
                p1 = self.oem_sheet.col(5)[i+1].value
                if isinstance(p1, float): 
                    p1 = str(int(self.oem_sheet.col(5)[i+1].value))
            if self.oem_sheet.col(1)[i+1].value==lei and excel_staff==staff:
                staffPwd1 = self.oem_sheet.col(8)[i+1].value
                if isinstance(staffPwd1, float): 
                    staffPwd1 =str(int(self.oem_sheet.col(8)[i+1].value))
        return p1,staffPwd1                 
#------------------------------------------------------------------------------------------------------
#直销默认帐号            
    def moren_name(self):       
        a=[]
        for i in range(self.fkw_sheet.nrows-1):
                if self.fkw_sheet.col(1)[i+1].value=="线上" :
                    cell_1 = self.fkw_sheet.col(3)[i+1].value
                    a.append(cell_1)
                    i+=1
        return a
     
    def dev_moren_name(self):
        a=[]
        for i in range(self.fkw_sheet.nrows-1):
                if self.fkw_sheet.col(1)[i+1].value=="本地" :
                    cell_1 = self.fkw_sheet.col(3)[i+1].value
                    a.append(cell_1)
                    i+=1
        return a
    
    def moren_staff(self):
        a=[]
        for i in range(self.fkw_sheet.nrows-1):
            if self.fkw_sheet.col(1)[i+1].value=="线上" :
                    cell_1 = self.fkw_sheet.col(6)[i+1].value
                    if cell_1!="":
                        a.append(cell_1)
                    i+=1
        return a

#默认备注
    def moren_tip(self):       
        name = self.select3.GetStringSelection()
        if name!="":
            for i in range(self.fkw_sheet.nrows-1):
                if self.fkw_sheet.col(1)[i+1].value=="线上" and self.fkw_sheet.col(3)[i+1].value==name:
                            if self.fkw_sheet.col(5)[i+1].value !="":
                                self.tip.SetValue(self.fkw_sheet.col(5)[i+1].value)
                            if self.fkw_sheet.col(5)[i+1].value =="":
                                self.tip.SetValue("该帐号无备注")

    def moren_agency_input(self):
        '''设置默认代理商'''
        get_agency_account = self.other_sheet.col(2)[22].value
        get_agency_pwd = self.other_sheet.col(3)[22].value
        
        self.agency_account_input.SetValue(get_agency_account)
        self.agency_pwd_input.SetValue(get_agency_pwd)
        
    def get_agency_input(self,evt):
        '''获取excel里代理商帐号密码'''
        leixing = self.select_agency.GetStringSelection()
        if leixing =="线上环境":
            get_agency_account = self.other_sheet.col(2)[22].value
            get_agency_pwd = self.other_sheet.col(3)[22].value
            
        elif leixing=="dep3环境":
            get_agency_account = self.other_sheet.col(2)[23].value
            get_agency_pwd = self.other_sheet.col(3)[23].value
        else:
            # 本地环境
            get_agency_account = self.other_sheet.col(2)[24].value
            get_agency_pwd = self.other_sheet.col(3)[24].value
            
        self.agency_account_input.SetValue(get_agency_account)
        self.agency_pwd_input.SetValue(get_agency_pwd)
 
    def jz(self,evt):
        '''登录帐号      '''
        zhifen = self.selectx.GetStringSelection()
        admin = self.select3.GetStringSelection()
        staff = self.select4.GetStringSelection()
        is_mallm = self.select_is_mallm.GetStringSelection()
        self.browser()
#登录
        if zhifen =="直销帐号": 
            pwd = self.pwd()[0]
            staffPwd = self.pwd()[1]
            leixing = self.select1.GetStringSelection()
            
            if is_mallm == "PC后台登录":
                if leixing =="线上环境":
                    url =self.other_sheet.col(1)[1].value
                elif leixing=="dep3环境":
                    url =self.other_sheet.col(1)[2].value
                elif leixing=="本地环境":
                    url =self.other_sheet.col(1)[3].value
                else:
                    url =self.other_sheet.col(1)[16].value
                
                self.driver.get(url)
                #调用函数传参并登录
                self.fkw_login(admin,pwd,staff,staffPwd)
                #调用关闭弹窗函数关闭登录后的弹窗
                self.close_pop() 
                 
            else:
                # 商家助手
                if leixing =="线上环境":
                    url =self.other_sheet.col(1)[26].value
                elif leixing=="dep3环境":
                    url =self.other_sheet.col(1)[27].value
                else:
                    url =self.other_sheet.col(1)[28].value
                    
                self.driver.get(url)
                #调用函数传参并登录
                self.fkw_mallm_login(admin,pwd,staff,staffPwd)    
        
 
        if zhifen =="OEM帐号":
            oem_pwd =self.pwd_fen()[0]
            staff_pwd =self.pwd_fen()[1]
            oem_name = admin.split()[0]
#             oem_staff_name = admin.split()[1]
            leixing = self.select1.GetStringSelection()
            if is_mallm == "PC后台登录":
                if leixing =="线上环境":
                    url =self.other_sheet.col(1)[5].value
                elif leixing=="dep3环境":
                    url =self.other_sheet.col(1)[6].value
                elif leixing=="本地环境":
                    url =self.other_sheet.col(1)[7].value
                else:
                    url =self.other_sheet.col(1)[7].value
                self.driver.get(url)
                self.oem_login(oem_name, oem_pwd,staff,staff_pwd)
                #调用关闭弹窗函数关闭登录后的弹窗
                self.close_pop()
            else:
                if leixing =="线上环境":
                    url =self.other_sheet.col(1)[30].value
                elif leixing=="dep3环境":
                    url =self.other_sheet.col(1)[31].value
                else:
                    url =self.other_sheet.col(1)[32].value
                self.driver.get(url)
                self.oem_mallm_login(oem_name, oem_pwd,staff,staff_pwd)        
             
    #开发环境登录
    def devjz(self,evt):
        admin = self.accountSelect.GetStringSelection()
        self.browser()
        #登录      
        pwd = self.dev_pwd()[0]
        url1 =self.other_sheet.col(1)[17].value
        url2 =self.other_sheet.col(1)[18].value
        devname = self.dev.GetValue()
        url = (url1+devname+url2)
        self.driver.get(url)
        #调用函数传参并登录
        self.fkw_login(admin,pwd)                  
         
   
    def openf(self,evt):
        try:
            if self.excel_path =="同一目录":
                startfile("login.xls")
        
            if self.excel_path=="doc下":
                startfile('%s\doc\login.xls'%self.path)
        except:
            dlg = wx.MessageDialog(self, '请将表格文件放置在程序所在文件或doc文件夹下','粗大事啦',style = wx.ICON_ERROR)
            dlg.ShowModal()
            dlg.Destroy()
 
    #结束conhost.exe进程 和浏览器进程以及删除临时文件      
    def OnClose(self,evt):
        self.task_kill()
        os.system('taskkill>nul 2>nul /IM conhost.exe /F')
        try:
            dir_path=self.other_sheet.col(1)[34].value
            del_path = r"%s"%dir_path
            all_del_dir = os.listdir(del_path)
            
            for del_dir in all_del_dir: 
                    log_name = del_dir.split("_")[0]
                    if log_name == "scoped":
                        try:
                            del_log_air = os.path.join(del_path, del_dir)
                            shutil.rmtree(del_log_air)
                        except:
                            pass
        except:
            pass
        evt.Skip()


if __name__ == '__main__':
    app = wx.App(0)
    f = LoginFrame()
    a = f.get_element()
    app.MainLoop()