# 自动登录功能使用说明

## 🎯 功能概述

基于Login.py文件的实现思路，我们为Web版账号管理工具添加了自动登录功能。该功能可以自动填写账号密码并执行登录操作，大大提高工作效率。

## ✨ 主要特性

### 1. 智能平台识别
- **直销平台**：自动识别新版/旧版登录页面
- **OEM平台**：支持OEM账号登录
- **代理商平台**：支持代理商账号登录  
- **OSS平台**：支持OSS管理后台登录

### 2. 员工账号支持
- 自动检测是否需要员工登录
- 支持员工账号和密码自动填写
- 智能处理员工登录复选框

### 3. 安全机制
- 模拟人工输入，避免被检测
- 智能等待页面加载完成
- 自动关闭登录后弹窗

## 🚀 使用方法

### 步骤1：配置账号信息
1. 在账号管理界面点击"添加账号"
2. 填写完整的账号信息：
   - 平台类型（直销/OEM/代理商平台/OSS）
   - 环境（线上/本地等）
   - 用户名和密码
   - 员工账号和密码（如需要）
   - 登录网址
   - 备注信息

### 步骤2：执行自动登录
1. 在账号列表中找到目标账号
2. 点击"自动登录"按钮
3. 系统会自动：
   - 打开新窗口访问登录页面
   - 等待页面加载完成
   - 自动填写账号密码
   - 点击登录按钮
   - 关闭登录后弹窗

## 🔧 技术实现

### 核心原理
参考Login.py中的Selenium自动化思路，在Web环境中实现：

```javascript
// 1. 页面检测
await waitForPageLoad()

// 2. 元素定位
const usernameInput = await waitForElement('input[name="username"]')

// 3. 模拟输入
await fillInput(selector, value)

// 4. 执行点击
await clickElement('button[type="submit"]')
```

### 选择器策略
使用多重选择器确保兼容性：
```javascript
const usernameSelectors = [
  'input[name="username"]',
  'input[placeholder*="用户名"]', 
  'input[placeholder*="账号"]',
  '#username'
];
```

### 登录流程适配

#### 直销平台
- **新版页面**：支持员工登录切换
- **旧版页面**：传统用户名密码登录

#### OEM平台  
- 支持OEM账号和员工账号
- 自动处理员工登录复选框

#### 代理商平台
- 自动选择代理商登录类型
- 填写代理商账号密码

#### OSS平台
- 简单的用户名密码登录

## ⚠️ 注意事项

### 浏览器兼容性
- **推荐**：Chrome、Edge等现代浏览器
- **限制**：部分浏览器可能阻止跨域脚本注入

### 安全考虑
- 账号信息存储在浏览器本地
- 不会向外部服务器发送敏感信息
- 建议定期更新密码

### 使用限制
- 需要允许浏览器弹窗
- 某些网站可能有反自动化机制
- 跨域限制可能影响脚本注入

## 🛠️ 故障排除

### 常见问题

#### 1. 无法打开新窗口
**原因**：浏览器阻止弹窗
**解决**：在浏览器设置中允许弹窗

#### 2. 自动登录失败
**原因**：页面元素变化或网络延迟
**解决**：
- 检查网址是否正确
- 确认账号密码无误
- 重试或手动登录

#### 3. 跨域脚本注入失败
**原因**：浏览器安全策略
**解决**：
- 使用Chrome等支持的浏览器
- 手动输入账号密码

### 调试信息
打开浏览器开发者工具（F12），查看Console输出：
- `开始自动登录...` - 脚本开始执行
- `自动登录成功` - 登录操作完成
- `自动登录失败: xxx` - 具体错误信息

## 📋 支持的登录页面元素

### 用户名输入框
```css
input[name="username"]
input[placeholder*="用户名"] 
input[placeholder*="账号"]
#username
```

### 密码输入框
```css
input[name="password"]
input[type="password"]
#password
```

### 登录按钮
```css
button[type="submit"]
.login-btn
.btn-login
#loginBtn
```

### 员工登录
```css
input[type="checkbox"][name*="staff"]
.staff-checkbox input
input[name="isStaff"]
```

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 基础自动登录功能
- ✅ 多平台支持（直销/OEM/代理商/OSS）
- ✅ 员工账号登录支持
- ✅ 智能页面检测
- ✅ 自动弹窗关闭
- ✅ 错误处理和通知

### 计划功能
- 🔄 验证码识别
- 🔄 更多平台支持
- 🔄 批量登录功能
- 🔄 登录状态检测

## 💡 使用建议

1. **首次使用**：建议先测试一个账号，确认功能正常
2. **网络环境**：确保网络稳定，避免登录超时
3. **账号安全**：定期更新密码，不要使用重要账号测试
4. **备份数据**：定期导出账号数据进行备份

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器Console错误信息
2. 确认账号信息配置正确
3. 尝试手动登录验证账号有效性
4. 联系技术支持获取帮助

---

**注意**：此功能仅用于提高工作效率，请遵守相关网站的使用条款和安全规定。
