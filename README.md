# km测试工具合集

一个专业的React前端测试工具集，使用React和Tailwind CSS构建。

## 项目结构

```
├── public/
│   └── index.html          # HTML模板
├── src/
│   ├── components/
│   │   ├── Header.js       # 顶部导航栏
│   │   ├── CategoryNav.js  # 分类导航
│   │   ├── ToolSection.js  # 工具区域容器
│   │   └── tools/          # 各个工具组件
│   │       ├── DataGenerator.js    # 数据生成工具
│   │       ├── TextCounter.js      # 字数统计工具
│   │       ├── TextCompare.js      # 文本对比工具
│   │       ├── RegexTester.js      # 正则测试工具
│   │       ├── JsonCompare.js      # JSON对比工具
│   │       ├── TimeTools.js        # 时间处理工具
│   │       ├── LogAnalyzer.js      # 日志分析工具
│   │       ├── ImageProcessor.js   # 图片处理工具
│   │       ├── EncodingTools.js    # 编码/加密工具
│   │       └── RandomQuestions.js  # 随机题目工具
│   ├── App.js              # 主应用组件
│   ├── index.js            # 应用入口
│   └── index.css           # 全局样式
├── demo.html               # 演示版本（可直接在浏览器打开）
├── package.json            # 项目配置
├── tailwind.config.js      # Tailwind配置
└── postcss.config.js       # PostCSS配置
```

## 功能特性

### 已实现的工具

1. **数据生成工具**
   - 随机内容生成器（支持多种语言和内容类型）
   - 随机邮箱生成器
   - 随机电话号码生成器
   - 随机地址生成器
   - 随机身份证生成器

2. **字数统计工具**
   - 字符数统计（含/不含空格）
   - 单词数统计
   - 行数和段落数统计
   - 实时统计更新

3. **文本对比工具**
   - 逐行文本对比
   - 支持忽略大小写、空白字符、空行
   - 相似度计算
   - 差异报告导出

4. **正则表达式测试工具**
   - 正则表达式验证和测试
   - 支持多种标志（g, i, m, s, u, y）
   - 匹配结果高亮显示
   - 分组捕获详情
   - 替换功能测试

5. **JSON数据对比工具**
   - JSON格式验证
   - 深度对比JSON结构
   - 差异详情展示
   - 结构对比和统计摘要

6. **时间处理工具**
   - 时间戳与日期互转
   - 时间单位换算
   - 日期计算器

7. **日志分析工具**
   - 日志文件导入
   - 统计分析（错误、警告、信息日志）
   - 日志过滤和搜索

8. **图片处理工具**
   - 图片上传和预览
   - 格式转换设置
   - 压缩和尺寸调整

9. **编码/加密工具**
   - Base64编码/解码
   - URL编码/解码
   - HTML编码/解码
   - Unicode编码/解码
   - 十六进制编码/解码
   - MD5、DES、AES、RSA加密（界面已实现，需要后端支持）

10. **随机题目生成工具**
    - 支持多种题目类型（单选、多选、判断、填空、简答、编程题）
    - 题目库导入和解析
    - 随机题目生成和预览
    - 题目导出和复制功能
    - 标准化题目格式规范

## 快速开始

### 方法一：直接查看演示版

1. 打开 `demo.html` 文件在浏览器中查看
2. 这个版本包含了主要功能的演示，无需安装依赖

### 方法二：完整React项目

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm start
```

3. 在浏览器中打开 [http://localhost:3000](http://localhost:3000)

## 技术栈

- **React 18** - 前端框架
- **Tailwind CSS** - 样式框架
- **JavaScript ES6+** - 编程语言

## 设计特点

1. **响应式设计** - 适配桌面和移动设备
2. **组件化架构** - 每个工具都是独立的React组件
3. **现代UI** - 使用Tailwind CSS构建美观的界面
4. **用户友好** - 直观的操作界面和实时反馈
5. **可扩展性** - 易于添加新的工具组件

## 浏览器兼容性

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

### 添加新工具

1. 在 `src/components/tools/` 目录下创建新的工具组件
2. 在 `src/App.js` 中导入并添加到 `categories` 数组
3. 按照现有组件的模式实现功能

### 样式规范

- 使用Tailwind CSS类名
- 遵循现有的设计模式
- 保持组件间的一致性

## 注意事项

1. 某些加密功能（如MD5、AES等）需要专业的加密库支持
2. 图片处理功能的实际转换需要后端支持
3. 大文件处理可能受到浏览器内存限制

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现9个主要工具类别
- 完整的UI界面和交互功能
