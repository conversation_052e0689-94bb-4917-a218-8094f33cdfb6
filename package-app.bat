@echo off
chcp 65001 >nul
title 打包km测试工具合集
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  打包km测试工具合集                          ║
echo ║                 正在创建可分发版本...                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 创建分发目录
if exist "km-test-tools-dist" rmdir /s /q "km-test-tools-dist"
mkdir "km-test-tools-dist"

echo [1/4] 构建React应用...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo ✅ React应用构建完成

echo [2/4] 复制必要文件...
REM 复制build文件夹
xcopy "build" "km-test-tools-dist\build" /E /I /Q
REM 复制服务器文件
copy "server.js" "km-test-tools-dist\"
copy "package.json" "km-test-tools-dist\"
copy "启动km测试工具合集.bat" "km-test-tools-dist\"
echo ✅ 文件复制完成

echo [3/4] 创建简化的package.json...
REM 创建简化的package.json，只包含运行时依赖
echo { > "km-test-tools-dist\package.json"
echo   "name": "km-test-tools", >> "km-test-tools-dist\package.json"
echo   "version": "1.0.0", >> "km-test-tools-dist\package.json"
echo   "description": "km测试工具合集", >> "km-test-tools-dist\package.json"
echo   "main": "server.js", >> "km-test-tools-dist\package.json"
echo   "scripts": { >> "km-test-tools-dist\package.json"
echo     "start": "node server.js" >> "km-test-tools-dist\package.json"
echo   }, >> "km-test-tools-dist\package.json"
echo   "dependencies": {} >> "km-test-tools-dist\package.json"
echo } >> "km-test-tools-dist\package.json"
echo ✅ 配置文件创建完成

echo [4/4] 创建启动脚本...
REM 创建新的启动脚本
echo @echo off > "km-test-tools-dist\启动km测试工具合集.bat"
echo chcp 65001 ^>nul >> "km-test-tools-dist\启动km测试工具合集.bat"
echo title km测试工具合集 >> "km-test-tools-dist\启动km测试工具合集.bat"
echo color 0A >> "km-test-tools-dist\启动km测试工具合集.bat"
echo. >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ╔══════════════════════════════════════════════════════════════╗ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║                    km测试工具合集                            ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║                   正在启动应用...                           ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ╚══════════════════════════════════════════════════════════════╝ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo. >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo 检查Node.js环境... >> "km-test-tools-dist\启动km测试工具合集.bat"
echo node --version ^>nul 2^>^&1 >> "km-test-tools-dist\启动km测试工具合集.bat"
echo if %%errorlevel%% neq 0 ^( >> "km-test-tools-dist\启动km测试工具合集.bat"
echo     echo ❌ 错误：未检测到Node.js >> "km-test-tools-dist\启动km测试工具合集.bat"
echo     echo 请先安装Node.js: https://nodejs.org/ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo     pause >> "km-test-tools-dist\启动km测试工具合集.bat"
echo     exit /b 1 >> "km-test-tools-dist\启动km测试工具合集.bat"
echo ^) >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ✅ Node.js环境正常 >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo. >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ╔══════════════════════════════════════════════════════════════╗ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║  应用启动成功！                                              ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║  地址: http://localhost:3000                                 ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║  浏览器将自动打开，如未打开请手动访问上述地址                ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║                                                              ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ║  按 Ctrl+C 可以停止应用                                      ║ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo ╚══════════════════════════════════════════════════════════════╝ >> "km-test-tools-dist\启动km测试工具合集.bat"
echo echo. >> "km-test-tools-dist\启动km测试工具合集.bat"
echo node server.js >> "km-test-tools-dist\启动km测试工具合集.bat"
echo ✅ 启动脚本创建完成

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    打包完成！                                ║
echo ║                                                              ║
echo ║  分发文件夹: km-test-tools-dist                              ║
echo ║  启动文件: 启动km测试工具合集.bat                            ║
echo ║                                                              ║
echo ║  您可以将整个 km-test-tools-dist 文件夹复制到任何            ║
echo ║  安装了Node.js的Windows电脑上运行                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
