═══════════════════════════════════════════════════════════════
                    km测试工具合集 - 使用说明
═══════════════════════════════════════════════════════════════

📋 系统要求：
- Windows 操作系统
- Node.js (建议v16或更高版本)
- 下载地址：https://nodejs.org/

🚀 启动方法：
1. 双击 "启动km测试工具合集.bat"
2. 等待浏览器自动打开
3. 如果浏览器未自动打开，手动访问：http://localhost:3000

🛑 停止方法：
- 在命令行窗口按 Ctrl+C
- 或直接关闭命令行窗口

🔧 功能介绍：
1. 账号管理 - Selenium自动登录功能
2. 时间处理 - 时间戳转换、格式化
3. 编码/加密 - MD5、SHA、AES、DES、RSA等专业加密
4. 文件处理 - 图片处理工具
5. 字数统计 - 文本分析统计
6. 数据生成 - 随机数据生成器
7. JSON数据对比 - JSON格式化和对比
8. 随机题目 - 题库管理和随机出题
9. 正则测试 - 正则表达式测试工具

❗ 常见问题：
Q: 提示"node不是内部或外部命令"
A: 请安装Node.js并重启电脑

Q: 端口3000被占用
A: 关闭占用端口的程序，或联系技术支持

Q: 浏览器未自动打开
A: 手动在浏览器中访问 http://localhost:3000

📞 技术支持：
如遇到其他问题，请检查：
1. Node.js是否正确安装
2. 防火墙是否拦截
3. 杀毒软件是否误报

版本：1.0.0
更新时间：2024年
