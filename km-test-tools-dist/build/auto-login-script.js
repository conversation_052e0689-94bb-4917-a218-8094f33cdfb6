// 自动登录脚本 - 在目标页面执行
(function() {
    'use strict';
    
    console.log('🚀 自动登录脚本已加载');
    
    // 从URL参数获取登录信息
    const urlParams = new URLSearchParams(window.location.search);
    const shouldAutoLogin = urlParams.get('autoLogin') === 'true';
    const username = urlParams.get('username');
    const password = urlParams.get('password');
    
    if (!shouldAutoLogin || !username || !password) {
        console.log('❌ 未检测到自动登录参数，脚本退出');
        return;
    }
    
    console.log('✅ 检测到自动登录参数，用户名:', username);
    
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async function performAutoLogin() {
        try {
            console.log('🔄 开始自动登录流程...');
            
            // 等待页面完全加载
            await sleep(3000);
            
            console.log('🔍 查找登录元素...');
            
            // 查找用户名输入框
            let usernameInput = document.querySelector('input[type="text"]') ||
                               document.querySelector('input[name="username"]') ||
                               document.querySelector('input[placeholder*="用户名"]') ||
                               document.querySelector('input[placeholder*="账号"]');
            
            // 查找密码输入框
            let passwordInput = document.querySelector('input[type="password"]') ||
                               document.querySelector('input[name="password"]');
            
            console.log('🔍 元素查找结果:', {
                username: !!usernameInput,
                password: !!passwordInput,
                usernameElement: usernameInput,
                passwordElement: passwordInput
            });
            
            if (!usernameInput || !passwordInput) {
                console.error('❌ 未找到必要的输入框');
                alert('未找到登录输入框，请手动登录');
                return;
            }
            
            // 填写用户名
            console.log('📝 填写用户名:', username);
            usernameInput.focus();
            usernameInput.click();
            await sleep(200);
            usernameInput.value = '';
            usernameInput.value = username;
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
            usernameInput.dispatchEvent(new Event('blur', { bubbles: true }));
            await sleep(500);
            
            // 填写密码
            console.log('📝 填写密码...');
            passwordInput.focus();
            passwordInput.click();
            await sleep(200);
            passwordInput.value = '';
            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
            passwordInput.dispatchEvent(new Event('blur', { bubbles: true }));
            await sleep(500);
            
            // 验证填写结果
            console.log('✅ 填写完成，验证结果:', {
                usernameValue: usernameInput.value,
                passwordValue: passwordInput.value.replace(/./g, '*')
            });
            
            // 查找登录按钮
            console.log('🔍 查找登录按钮...');
            let loginButton = document.querySelector('#login_button') ||
                             document.querySelector('.login_button') ||
                             document.querySelector('div[onclick*="login"]') ||
                             document.querySelector('button[type="submit"]') ||
                             document.querySelector('input[type="submit"]');
            
            // 通过文本查找登录按钮
            if (!loginButton) {
                console.log('🔍 通过文本查找登录按钮...');
                const allElements = document.querySelectorAll('div, button, a, span');
                for (const element of allElements) {
                    const text = element.textContent.trim();
                    if (text === '登录' || text === '登 录' || text === 'Login') {
                        loginButton = element;
                        console.log('✅ 通过文本找到登录按钮:', text, element);
                        break;
                    }
                }
            }
            
            if (loginButton) {
                console.log('🔘 找到登录按钮，准备点击...', loginButton);
                await sleep(1000);
                
                // 尝试多种点击方式
                try {
                    console.log('🔘 尝试方式1: 直接点击');
                    loginButton.click();
                    console.log('✅ 登录按钮点击成功');
                } catch (e1) {
                    console.warn('❌ 方式1失败:', e1.message);
                    try {
                        console.log('🔘 尝试方式2: onclick调用');
                        if (loginButton.onclick) {
                            loginButton.onclick();
                            console.log('✅ 通过onclick点击成功');
                        } else {
                            throw new Error('没有onclick方法');
                        }
                    } catch (e2) {
                        console.warn('❌ 方式2失败:', e2.message);
                        try {
                            console.log('🔘 尝试方式3: 事件分发');
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            loginButton.dispatchEvent(clickEvent);
                            console.log('✅ 通过事件分发点击成功');
                        } catch (e3) {
                            console.error('❌ 所有点击方式都失败:', e3.message);
                            alert('登录按钮点击失败，请手动点击登录按钮');
                        }
                    }
                }
                
                console.log('🎉 自动登录流程完成！');
                
                // 显示成功通知
                setTimeout(() => {
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #4CAF50;
                        color: white;
                        padding: 15px 20px;
                        border-radius: 5px;
                        font-size: 14px;
                        z-index: 10000;
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    `;
                    notification.textContent = '✅ 自动登录完成！';
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                }, 1000);
                
            } else {
                console.error('❌ 未找到登录按钮');
                alert('未找到登录按钮，账号密码已填入，请手动点击登录');
            }
            
        } catch (error) {
            console.error('❌ 自动登录过程出错:', error);
            alert('自动登录出错: ' + error.message);
        }
    }
    
    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(performAutoLogin, 1000);
        });
    } else {
        setTimeout(performAutoLogin, 1000);
    }
    
})();
