/*! For license information please see main.5876a3f8.js.LICENSE.txt */
(()=>{var e={2:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){var e=t,n=e.lib,r=n.WordArray,i=n.Hasher,a=e.algo,s=[],o=a.SHA1=i.extend({_doReset:function(){this._hash=new r.init([**********,**********,**********,271733878,**********])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],o=n[3],l=n[4],c=0;c<80;c++){if(c<16)s[c]=0|e[t+c];else{var u=s[c-3]^s[c-8]^s[c-14]^s[c-16];s[c]=u<<1|u>>>31}var d=(r<<5|r>>>27)+l+s[c];d+=c<20?**********+(i&a|~i&o):c<40?**********+(i^a^o):c<60?(i&a|i&o|a&o)-**********:(i^a^o)-899497514,l=o,o=a,a=i<<30|i>>>2,i=r,r=d}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+a|0,n[3]=n[3]+o|0,n[4]=n[4]+l|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/**********),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA1=i._createHelper(o),e.HmacSHA1=i._createHmacHelper(o)}(),t.SHA1)}()},43:(e,t,n)=>{"use strict";e.exports=n(202)},49:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function n(e){if(255===(e>>24&255)){var t=e>>16&255,n=e>>8&255,r=255&e;255===t?(t=0,255===n?(n=0,255===r?r=0:++r):++n):++t,e=0,e+=t<<16,e+=n<<8,e+=r}else e+=1<<24;return e}function r(e){return 0===(e[0]=n(e[0]))&&(e[1]=n(e[1])),e}var i=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,a=this._iv,s=this._counter;a&&(s=this._counter=a.slice(0),this._iv=void 0),r(s);var o=s.slice(0);n.encryptBlock(o,0);for(var l=0;l<i;l++)e[t+l]^=o[l]}});return e.Decryptor=i,e}(),t.mode.CTRGladman)}()},61:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(749),function(){var e=t,n=e.lib.Hasher,r=e.x64,i=r.Word,a=r.WordArray,s=e.algo;function o(){return i.create.apply(i,arguments)}var l=[o(1116352408,3609767458),o(1899447441,602891725),o(3049323471,3964484399),o(3921009573,2173295548),o(961987163,4081628472),o(1508970993,3053834265),o(2453635748,2937671579),o(2870763221,3664609560),o(3624381080,2734883394),o(310598401,1164996542),o(607225278,1323610764),o(1426881987,3590304994),o(1925078388,4068182383),o(2162078206,991336113),o(2614888103,633803317),o(3248222580,3479774868),o(3835390401,2666613458),o(4022224774,944711139),o(264347078,2341262773),o(604807628,2007800933),o(770255983,1495990901),o(1249150122,1856431235),o(1555081692,3175218132),o(1996064986,2198950837),o(2554220882,3999719339),o(2821834349,766784016),o(2952996808,2566594879),o(3210313671,3203337956),o(3336571891,1034457026),o(3584528711,2466948901),o(113926993,3758326383),o(338241895,168717936),o(666307205,1188179964),o(773529912,1546045734),o(1294757372,1522805485),o(1396182291,2643833823),o(1695183700,2343527390),o(1986661051,1014477480),o(2177026350,1206759142),o(2456956037,344077627),o(2730485921,1290863460),o(2820302411,3158454273),o(3259730800,3505952657),o(3345764771,106217008),o(3516065817,3606008344),o(3600352804,1432725776),o(4094571909,1467031594),o(275423344,851169720),o(430227734,3100823752),o(506948616,1363258195),o(659060556,3750685593),o(883997877,3785050280),o(958139571,3318307427),o(1322822218,3812723403),o(1537002063,2003034995),o(1747873779,3602036899),o(1955562222,1575990012),o(2024104815,1125592928),o(2227730452,2716904306),o(2361852424,442776044),o(2428436474,593698344),o(2756734187,3733110249),o(3204031479,2999351573),o(3329325298,3815920427),o(3391569614,3928383900),o(3515267271,566280711),o(3940187606,3454069534),o(4118630271,4000239992),o(116418474,1914138554),o(174292421,2731055270),o(289380356,3203993006),o(460393269,320620315),o(685471733,587496836),o(852142971,1086792851),o(1017036298,365543100),o(1126000580,2618297676),o(1288033470,3409855158),o(1501505948,4234509866),o(1607167915,987167468),o(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=o()}();var u=s.SHA512=n.extend({_doReset:function(){this._hash=new a.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],s=n[3],o=n[4],u=n[5],d=n[6],f=n[7],h=r.high,p=r.low,m=i.high,g=i.low,v=a.high,y=a.low,b=s.high,x=s.low,w=o.high,S=o.low,k=u.high,N=u.low,j=d.high,E=d.low,_=f.high,C=f.low,T=h,B=p,D=m,R=g,P=v,O=y,A=b,z=x,L=w,M=S,I=k,F=N,V=j,H=E,U=_,q=C,W=0;W<80;W++){var K,$,Q=c[W];if(W<16)$=Q.high=0|e[t+2*W],K=Q.low=0|e[t+2*W+1];else{var J=c[W-15],G=J.high,X=J.low,Z=(G>>>1|X<<31)^(G>>>8|X<<24)^G>>>7,Y=(X>>>1|G<<31)^(X>>>8|G<<24)^(X>>>7|G<<25),ee=c[W-2],te=ee.high,ne=ee.low,re=(te>>>19|ne<<13)^(te<<3|ne>>>29)^te>>>6,ie=(ne>>>19|te<<13)^(ne<<3|te>>>29)^(ne>>>6|te<<26),ae=c[W-7],se=ae.high,oe=ae.low,le=c[W-16],ce=le.high,ue=le.low;$=($=($=Z+se+((K=Y+oe)>>>0<Y>>>0?1:0))+re+((K+=ie)>>>0<ie>>>0?1:0))+ce+((K+=ue)>>>0<ue>>>0?1:0),Q.high=$,Q.low=K}var de,fe=L&I^~L&V,he=M&F^~M&H,pe=T&D^T&P^D&P,me=B&R^B&O^R&O,ge=(T>>>28|B<<4)^(T<<30|B>>>2)^(T<<25|B>>>7),ve=(B>>>28|T<<4)^(B<<30|T>>>2)^(B<<25|T>>>7),ye=(L>>>14|M<<18)^(L>>>18|M<<14)^(L<<23|M>>>9),be=(M>>>14|L<<18)^(M>>>18|L<<14)^(M<<23|L>>>9),xe=l[W],we=xe.high,Se=xe.low,ke=U+ye+((de=q+be)>>>0<q>>>0?1:0),Ne=ve+me;U=V,q=H,V=I,H=F,I=L,F=M,L=A+(ke=(ke=(ke=ke+fe+((de+=he)>>>0<he>>>0?1:0))+we+((de+=Se)>>>0<Se>>>0?1:0))+$+((de+=K)>>>0<K>>>0?1:0))+((M=z+de|0)>>>0<z>>>0?1:0)|0,A=P,z=O,P=D,O=R,D=T,R=B,T=ke+(ge+pe+(Ne>>>0<ve>>>0?1:0))+((B=de+Ne|0)>>>0<de>>>0?1:0)|0}p=r.low=p+B,r.high=h+T+(p>>>0<B>>>0?1:0),g=i.low=g+R,i.high=m+D+(g>>>0<R>>>0?1:0),y=a.low=y+O,a.high=v+P+(y>>>0<O>>>0?1:0),x=s.low=x+z,s.high=b+A+(x>>>0<z>>>0?1:0),S=o.low=S+M,o.high=w+L+(S>>>0<M>>>0?1:0),N=u.low=N+F,u.high=k+I+(N>>>0<F>>>0?1:0),E=d.low=E+H,d.high=j+V+(E>>>0<H>>>0?1:0),C=f.low=C+q,f.high=_+U+(C>>>0<q>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[30+(r+128>>>10<<5)]=Math.floor(n/**********),t[31+(r+128>>>10<<5)]=n,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=n._createHelper(u),e.HmacSHA512=n._createHmacHelper(u)}(),t.SHA512)}()},87:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding)}()},92:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(749),n(61),function(){var e=t,n=e.x64,r=n.Word,i=n.WordArray,a=e.algo,s=a.SHA512,o=a.SHA384=s.extend({_doReset:function(){this._hash=new i.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=s._doFinalize.call(this);return e.sigBytes-=16,e}});e.SHA384=s._createHelper(o),e.HmacSHA384=s._createHmacHelper(o)}(),t.SHA384)}()},99:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.mode.ECB=function(){var e=t.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e}(),t.mode.ECB)}()},111:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(749),n(503),n(168),n(523),n(646),n(199),n(2),n(368),n(641),n(61),n(92),n(600),n(423),n(220),n(930),n(211),n(238),n(112),n(962),n(49),n(380),n(99),n(884),n(132),n(804),n(548),n(87),n(708),n(780),n(475),n(638),n(875),n(659),n(501),t)}()},112:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function n(e,t,n,r){var i,a=this._iv;a?(i=a.slice(0),this._iv=void 0):i=this._prevBlock,r.encryptBlock(i,0);for(var s=0;s<n;s++)e[t+s]^=i[s]}return e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;n.call(this,e,t,i,r),this._prevBlock=e.slice(t,t+i)}}),e.Decryptor=e.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,a=e.slice(t,t+i);n.call(this,e,t,i,r),this._prevBlock=a}}),e}(),t.mode.CFB)}()},132:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.pad.Iso10126={pad:function(e,n){var r=4*n,i=r-e.sigBytes%r;e.concat(t.lib.WordArray.random(i-1)).concat(t.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},t.pad.Iso10126)}()},153:(e,t,n)=>{"use strict";var r=n(43),i=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)s.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:i,type:e,key:c,ref:u,props:a,_owner:o.current}}t.jsx=c,t.jsxs=c},168:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){var e=t,n=e.lib.WordArray,r=e.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i+=2){var a=t[i>>>2]>>>16-i%4*8&65535;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return n.create(r,2*t)}},r.Utf16LE={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a+=2){var s=i(t[a>>>2]>>>16-a%4*8&65535);r.push(String.fromCharCode(s))}return r.join("")},parse:function(e){for(var t=e.length,r=[],a=0;a<t;a++)r[a>>>1]|=i(e.charCodeAt(a)<<16-a%2*16);return n.create(r,2*t)}}}(),t.enc.Utf16)}()},199:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,s=n.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=***********e.abs(e.sin(t+1))|0}();var l=s.MD5=a.extend({_doReset:function(){this._hash=new i.init([**********,**********,**********,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var a=this._hash.words,s=e[t+0],l=e[t+1],h=e[t+2],p=e[t+3],m=e[t+4],g=e[t+5],v=e[t+6],y=e[t+7],b=e[t+8],x=e[t+9],w=e[t+10],S=e[t+11],k=e[t+12],N=e[t+13],j=e[t+14],E=e[t+15],_=a[0],C=a[1],T=a[2],B=a[3];_=c(_,C,T,B,s,7,o[0]),B=c(B,_,C,T,l,12,o[1]),T=c(T,B,_,C,h,17,o[2]),C=c(C,T,B,_,p,22,o[3]),_=c(_,C,T,B,m,7,o[4]),B=c(B,_,C,T,g,12,o[5]),T=c(T,B,_,C,v,17,o[6]),C=c(C,T,B,_,y,22,o[7]),_=c(_,C,T,B,b,7,o[8]),B=c(B,_,C,T,x,12,o[9]),T=c(T,B,_,C,w,17,o[10]),C=c(C,T,B,_,S,22,o[11]),_=c(_,C,T,B,k,7,o[12]),B=c(B,_,C,T,N,12,o[13]),T=c(T,B,_,C,j,17,o[14]),_=u(_,C=c(C,T,B,_,E,22,o[15]),T,B,l,5,o[16]),B=u(B,_,C,T,v,9,o[17]),T=u(T,B,_,C,S,14,o[18]),C=u(C,T,B,_,s,20,o[19]),_=u(_,C,T,B,g,5,o[20]),B=u(B,_,C,T,w,9,o[21]),T=u(T,B,_,C,E,14,o[22]),C=u(C,T,B,_,m,20,o[23]),_=u(_,C,T,B,x,5,o[24]),B=u(B,_,C,T,j,9,o[25]),T=u(T,B,_,C,p,14,o[26]),C=u(C,T,B,_,b,20,o[27]),_=u(_,C,T,B,N,5,o[28]),B=u(B,_,C,T,h,9,o[29]),T=u(T,B,_,C,y,14,o[30]),_=d(_,C=u(C,T,B,_,k,20,o[31]),T,B,g,4,o[32]),B=d(B,_,C,T,b,11,o[33]),T=d(T,B,_,C,S,16,o[34]),C=d(C,T,B,_,j,23,o[35]),_=d(_,C,T,B,l,4,o[36]),B=d(B,_,C,T,m,11,o[37]),T=d(T,B,_,C,y,16,o[38]),C=d(C,T,B,_,w,23,o[39]),_=d(_,C,T,B,N,4,o[40]),B=d(B,_,C,T,s,11,o[41]),T=d(T,B,_,C,p,16,o[42]),C=d(C,T,B,_,v,23,o[43]),_=d(_,C,T,B,x,4,o[44]),B=d(B,_,C,T,k,11,o[45]),T=d(T,B,_,C,E,16,o[46]),_=f(_,C=d(C,T,B,_,h,23,o[47]),T,B,s,6,o[48]),B=f(B,_,C,T,y,10,o[49]),T=f(T,B,_,C,j,15,o[50]),C=f(C,T,B,_,g,21,o[51]),_=f(_,C,T,B,k,6,o[52]),B=f(B,_,C,T,p,10,o[53]),T=f(T,B,_,C,w,15,o[54]),C=f(C,T,B,_,l,21,o[55]),_=f(_,C,T,B,b,6,o[56]),B=f(B,_,C,T,E,10,o[57]),T=f(T,B,_,C,v,15,o[58]),C=f(C,T,B,_,N,21,o[59]),_=f(_,C,T,B,m,6,o[60]),B=f(B,_,C,T,S,10,o[61]),T=f(T,B,_,C,h,15,o[62]),C=f(C,T,B,_,x,21,o[63]),a[0]=a[0]+_|0,a[1]=a[1]+C|0,a[2]=a[2]+T|0,a[3]=a[3]+B|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var a=e.floor(r/**********),s=r;n[15+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(n.length+1),this._process();for(var o=this._hash,l=o.words,c=0;c<4;c++){var u=l[c];l[c]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return o},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,i,a,s){var o=e+(t&n|~t&r)+i+s;return(o<<a|o>>>32-a)+t}function u(e,t,n,r,i,a,s){var o=e+(t&r|n&~r)+i+s;return(o<<a|o>>>32-a)+t}function d(e,t,n,r,i,a,s){var o=e+(t^n^r)+i+s;return(o<<a|o>>>32-a)+t}function f(e,t,n,r,i,a,s){var o=e+(n^(t|~r))+i+s;return(o<<a|o>>>32-a)+t}n.MD5=a._createHelper(l),n.HmacMD5=a._createHmacHelper(l)}(Math),t.MD5)}()},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,m(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,r){var i,a={},s=null,o=null;if(null!=t)for(i in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(s=""+t.key),t)S.call(t,i)&&!N.hasOwnProperty(i)&&(a[i]=t[i]);var l=arguments.length-2;if(1===l)a.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];a.children=c}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===a[i]&&(a[i]=l[i]);return{$$typeof:n,type:e,key:s,ref:o,props:a,_owner:k.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(e,t,i,a,s){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var l=!1;if(null===e)l=!0;else switch(o){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return s=s(l=e),e=""===a?"."+C(l,0):a,w(s)?(i="",null!=e&&(i=e.replace(_,"$&/")+"/"),T(s,t,i,"",function(e){return e})):null!=s&&(E(s)&&(s=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,i+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(_,"$&/")+"/")+e)),t.push(s)),1;if(l=0,a=""===a?".":a+":",w(e))for(var c=0;c<e.length;c++){var u=a+C(o=e[c],c);l+=T(o,t,i,u,s)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(o=e.next()).done;)l+=T(o=o.value,t,i,u=a+C(o,c++),s);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function B(e,t,n){if(null==e)return e;var r=[],i=0;return T(e,r,"","",function(e){return t.call(n,e,i++)}),r}function D(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},P={transition:null},O={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:P,ReactCurrentOwner:k};function A(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:B,forEach:function(e,t,n){B(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return B(e,function(){t++}),t},toArray:function(e){return B(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=i,t.Profiler=s,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,t.act=A,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),a=e.key,s=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,o=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)S.call(t,c)&&!N.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}return{$$typeof:n,type:e.type,key:a,ref:s,props:i,_owner:o}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:D}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=P.transition;P.transition={};try{e()}finally{P.transition=t}},t.unstable_act=A,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},211:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(2),n(220),function(){var e=t,n=e.lib,r=n.Base,i=n.WordArray,a=e.algo,s=a.MD5,o=a.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,r=this.cfg,a=r.hasher.create(),s=i.create(),o=s.words,l=r.keySize,c=r.iterations;o.length<l;){n&&a.update(n),n=a.update(e).finalize(t),a.reset();for(var u=1;u<c;u++)n=a.finalize(n),a.reset();s.concat(n)}return s.sigBytes=4*l,s}});e.EvpKDF=function(e,t,n){return o.create(n).compute(e,t)}}(),t.EvpKDF)}()},220:function(e,t,n){!function(){var t;e.exports=(t=n(488),void function(){var e=t,n=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=n.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,i=4*n;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var a=this._oKey=t.clone(),s=this._iKey=t.clone(),o=a.words,l=s.words,c=0;c<n;c++)o[c]^=1549556828,l[c]^=909522486;a.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())}()},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<a(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,s=i>>>1;r<s;){var o=2*(r+1)-1,l=e[o],c=o+1,u=e[c];if(0>a(l,n))c<i&&0>a(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[o]=n,r=o);else{if(!(c<i&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();t.unstable_now=function(){return o.now()-l}}var c=[],u=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(u);null!==t;){if(null===t.callback)i(u);else{if(!(t.startTime<=e))break;i(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,x(e),!m)if(null!==r(c))m=!0,P(S);else{var t=r(u);null!==t&&O(w,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,y(E),E=-1),p=!0;var a=h;try{for(x(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!T());){var s=f.callback;if("function"===typeof s){f.callback=null,h=f.priorityLevel;var o=s(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof o?f.callback=o:f===r(c)&&i(c),x(n)}else i(c);f=r(c)}if(null!==f)var l=!0;else{var d=r(u);null!==d&&O(w,d.startTime-n),l=!1}return l}finally{f=null,h=a,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,N=!1,j=null,E=-1,_=5,C=-1;function T(){return!(t.unstable_now()-C<_)}function B(){if(null!==j){var e=t.unstable_now();C=e;var n=!0;try{n=j(!0,e)}finally{n?k():(N=!1,j=null)}}else N=!1}if("function"===typeof b)k=function(){b(B)};else if("undefined"!==typeof MessageChannel){var D=new MessageChannel,R=D.port2;D.port1.onmessage=B,k=function(){R.postMessage(null)}}else k=function(){v(B,0)};function P(e){j=e,N||(N=!0,k())}function O(e,n){E=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,P(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,a){var s=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?s+a:s:a=s,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:a,expirationTime:o=a+o,sortIndex:-1},a>s?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(y(E),E=-1):g=!0,O(w,a-s))):(e.sortIndex=o,n(c,e),m||p||(m=!0,P(S))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},238:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(211),void(t.lib.Cipher||function(e){var n=t,r=n.lib,i=r.Base,a=r.WordArray,s=r.BufferedBlockAlgorithm,o=n.enc,l=(o.Utf8,o.Base64),c=n.algo.EvpKDF,u=r.Cipher=s.extend({cfg:i.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:v}return function(t){return{encrypt:function(n,r,i){return e(r).encrypt(t,n,r,i)},decrypt:function(n,r,i){return e(r).decrypt(t,n,r,i)}}}}()}),d=(r.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),n.mode={}),f=r.BlockCipherMode=i.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),h=d.CBC=function(){var t=f.extend();function n(t,n,r){var i,a=this._iv;a?(i=a,this._iv=e):i=this._prevBlock;for(var s=0;s<r;s++)t[n+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize;n.call(this,e,t,i),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,a=e.slice(t,t+i);r.decryptBlock(e,t),n.call(this,e,t,i),this._prevBlock=a}}),t}(),p=(n.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,i=r<<24|r<<16|r<<8|r,s=[],o=0;o<r;o+=4)s.push(i);var l=a.create(s,r);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},m=(r.BlockCipher=u.extend({cfg:u.cfg.extend({mode:h,padding:p}),reset:function(){var e;u.reset.call(this);var t=this.cfg,n=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(r,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),r.CipherParams=i.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}})),g=(n.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?a.create([1398893684,1701076831]).concat(n).concat(t):t).toString(l)},parse:function(e){var t,n=l.parse(e),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=a.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),m.create({ciphertext:n,salt:t})}},v=r.SerializableCipher=i.extend({cfg:i.extend({format:g}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var i=e.createEncryptor(n,r),a=i.finalize(t),s=i.cfg;return m.create({ciphertext:a,key:n,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),y=(n.kdf={}).OpenSSL={execute:function(e,t,n,r,i){if(r||(r=a.random(8)),i)s=c.create({keySize:t+n,hasher:i}).compute(e,r);else var s=c.create({keySize:t+n}).compute(e,r);var o=a.create(s.words.slice(t),4*n);return s.sigBytes=4*t,m.create({key:s,iv:o,salt:r})}},b=r.PasswordBasedCipher=v.extend({cfg:v.cfg.extend({kdf:y}),encrypt:function(e,t,n,r){var i=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize,r.salt,r.hasher);r.iv=i.iv;var a=v.encrypt.call(this,e,t,i.key,r);return a.mixIn(i),a},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var i=r.kdf.execute(n,e.keySize,e.ivSize,t.salt,r.hasher);return r.iv=i.iv,v.decrypt.call(this,e,t,i.key,r)}})}()))}()},368:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,s=n.algo,o=[],l=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return ***********(e-(0|e))|0}for(var r=2,i=0;i<64;)t(r)&&(i<8&&(o[i]=n(e.pow(r,.5))),l[i]=n(e.pow(r,1/3)),i++),r++}();var c=[],u=s.SHA256=a.extend({_doReset:function(){this._hash=new i.init(o.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],a=n[2],s=n[3],o=n[4],u=n[5],d=n[6],f=n[7],h=0;h<64;h++){if(h<16)c[h]=0|e[t+h];else{var p=c[h-15],m=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,g=c[h-2],v=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[h]=m+c[h-7]+v+c[h-16]}var y=r&i^r&a^i&a,b=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),x=f+((o<<26|o>>>6)^(o<<21|o>>>11)^(o<<7|o>>>25))+(o&u^~o&d)+l[h]+c[h];f=d,d=u,u=o,o=s+x|0,s=a,a=i,i=r,r=x+(b+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+a|0,n[3]=n[3]+s|0,n[4]=n[4]+o|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+f|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=e.floor(r/**********),n[15+(i+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});n.SHA256=a._createHelper(u),n.HmacSHA256=a._createHmacHelper(u)}(Math),t.SHA256)}()},380:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.mode.OFB=function(){var e=t.lib.BlockCipherMode.extend(),n=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=this._iv,a=this._keystream;i&&(a=this._keystream=i.slice(0),this._iv=void 0),n.encryptBlock(a,0);for(var s=0;s<r;s++)e[t+s]^=a[s]}});return e.Decryptor=n,e}(),t.mode.OFB)}()},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},423:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){var e=t,n=e.lib,r=n.WordArray,i=n.Hasher,a=e.algo,s=r.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),o=r.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),l=r.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=r.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=r.create([0,**********,**********,2400959708,2840853838]),d=r.create([1352829926,1548603684,1836072691,2053994217,0]),f=a.RIPEMD160=i.extend({_doReset:function(){this._hash=r.create([**********,**********,**********,271733878,**********])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,i=e[r];e[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var a,f,b,x,w,S,k,N,j,E,_,C=this._hash.words,T=u.words,B=d.words,D=s.words,R=o.words,P=l.words,O=c.words;for(S=a=C[0],k=f=C[1],N=b=C[2],j=x=C[3],E=w=C[4],n=0;n<80;n+=1)_=a+e[t+D[n]]|0,_+=n<16?h(f,b,x)+T[0]:n<32?p(f,b,x)+T[1]:n<48?m(f,b,x)+T[2]:n<64?g(f,b,x)+T[3]:v(f,b,x)+T[4],_=(_=y(_|=0,P[n]))+w|0,a=w,w=x,x=y(b,10),b=f,f=_,_=S+e[t+R[n]]|0,_+=n<16?v(k,N,j)+B[0]:n<32?g(k,N,j)+B[1]:n<48?m(k,N,j)+B[2]:n<64?p(k,N,j)+B[3]:h(k,N,j)+B[4],_=(_=y(_|=0,O[n]))+E|0,S=E,E=j,j=y(N,10),N=k,k=_;_=C[1]+b+j|0,C[1]=C[2]+x+E|0,C[2]=C[3]+w+S|0,C[3]=C[4]+a+k|0,C[4]=C[0]+f+N|0,C[0]=_},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,a=i.words,s=0;s<5;s++){var o=a[s];a[s]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}return i},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function h(e,t,n){return e^t^n}function p(e,t,n){return e&t|~e&n}function m(e,t,n){return(e|~t)^n}function g(e,t,n){return e&n|t&~n}function v(e,t,n){return e^(t|~n)}function y(e,t){return e<<t|e>>>32-t}e.RIPEMD160=i._createHelper(f),e.HmacRIPEMD160=i._createHmacHelper(f)}(Math),t.RIPEMD160)}()},475:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib,r=n.WordArray,i=n.BlockCipher,a=e.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],o=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],l=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],d=a.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],n=0;n<56;n++){var r=s[n]-1;t[n]=e[r>>>5]>>>31-r%32&1}for(var i=this._subKeys=[],a=0;a<16;a++){var c=i[a]=[],u=l[a];for(n=0;n<24;n++)c[n/6|0]|=t[(o[n]-1+u)%28]<<31-n%6,c[4+(n/6|0)]|=t[28+(o[n+24]-1+u)%28]<<31-n%6;for(c[0]=c[0]<<1|c[0]>>>31,n=1;n<7;n++)c[n]=c[n]>>>4*(n-1)+3;c[7]=c[7]<<5|c[7]>>>27}var d=this._invSubKeys=[];for(n=0;n<16;n++)d[n]=i[15-n]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,n){this._lBlock=e[t],this._rBlock=e[t+1],f.call(this,4,252645135),f.call(this,16,65535),h.call(this,2,858993459),h.call(this,8,16711935),f.call(this,1,1431655765);for(var r=0;r<16;r++){for(var i=n[r],a=this._lBlock,s=this._rBlock,o=0,l=0;l<8;l++)o|=c[l][((s^i[l])&u[l])>>>0];this._lBlock=s,this._rBlock=a^o}var d=this._lBlock;this._lBlock=this._rBlock,this._rBlock=d,f.call(this,1,1431655765),h.call(this,8,16711935),h.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(e,t){var n=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=n,this._lBlock^=n<<e}function h(e,t){var n=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=n,this._rBlock^=n<<e}e.DES=i._createHelper(d);var p=a.TripleDES=i.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),n=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=d.createEncryptor(r.create(t)),this._des2=d.createEncryptor(r.create(n)),this._des3=d.createEncryptor(r.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(p)}(),t.TripleDES)}()},477:()=>{},488:function(e,t,n){e.exports=function(){var e=e||function(e,t){var r;if("undefined"!==typeof window&&window.crypto&&(r=window.crypto),"undefined"!==typeof self&&self.crypto&&(r=self.crypto),"undefined"!==typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!==typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&"undefined"!==typeof n.g&&n.g.crypto&&(r=n.g.crypto),!r)try{r=n(477)}catch(g){}var i=function(){if(r){if("function"===typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(g){}if("function"===typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(g){}}throw new Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},o=s.lib={},l=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=l.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||d).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var a=0;a<i;a++){var s=n[a>>>2]>>>24-a%4*8&255;t[r+a>>>2]|=s<<24-(r+a)%4*8}else for(var o=0;o<i;o+=4)t[r+o>>>2]=n[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=l.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(i());return new c.init(t,e)}}),u=s.enc={},d=u.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var a=t[i>>>2]>>>24-i%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(n,t/2)}},f=u.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var a=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(a))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(n,t)}},h=u.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,i=r.words,a=r.sigBytes,s=this.blockSize,o=a/(4*s),l=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*s,u=e.min(4*l,a);if(l){for(var d=0;d<l;d+=s)this._doProcessBlock(i,d);n=i.splice(0,l),r.sigBytes-=u}return new c.init(n,u)},clone:function(){var e=l.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),m=(o.Hasher=p.extend({cfg:l.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new m.HMAC.init(e,n).finalize(t)}}}),s.algo={});return s}(Math);return e}()},501:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib.BlockCipher,r=e.algo;const i=16,a=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],s=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var o={pbox:[],sbox:[]};function l(e,t){let n=t>>24&255,r=t>>16&255,i=t>>8&255,a=255&t,s=e.sbox[0][n]+e.sbox[1][r];return s^=e.sbox[2][i],s+=e.sbox[3][a],s}function c(e,t,n){let r,a=t,s=n;for(let o=0;o<i;++o)a^=e.pbox[o],s=l(e,a)^s,r=a,a=s,s=r;return r=a,a=s,s=r,s^=e.pbox[i],a^=e.pbox[i+1],{left:a,right:s}}function u(e,t,n){let r,a=t,s=n;for(let o=i+1;o>1;--o)a^=e.pbox[o],s=l(e,a)^s,r=a,a=s,s=r;return r=a,a=s,s=r,s^=e.pbox[1],a^=e.pbox[0],{left:a,right:s}}function d(e,t,n){for(let i=0;i<4;i++){e.sbox[i]=[];for(let t=0;t<256;t++)e.sbox[i][t]=s[i][t]}let r=0;for(let s=0;s<i+2;s++)e.pbox[s]=a[s]^t[r],r++,r>=n&&(r=0);let o=0,l=0,u=0;for(let a=0;a<i+2;a+=2)u=c(e,o,l),o=u.left,l=u.right,e.pbox[a]=o,e.pbox[a+1]=l;for(let i=0;i<4;i++)for(let t=0;t<256;t+=2)u=c(e,o,l),o=u.left,l=u.right,e.sbox[i][t]=o,e.sbox[i][t+1]=l;return!0}var f=r.Blowfish=n.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4;d(o,t,n)}},encryptBlock:function(e,t){var n=c(o,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},decryptBlock:function(e,t){var n=u(o,e[t],e[t+1]);e[t]=n.left,e[t+1]=n.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=n._createHelper(f)}(),t.Blowfish)}()},503:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,n=e.init,r=e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!==typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,r=[],i=0;i<t;i++)r[i>>>2]|=e[i]<<24-i%4*8;n.call(this,r,t)}else n.apply(this,arguments)};r.prototype=e}}(),t.lib.WordArray)}()},523:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){var e=t,n=e.lib.WordArray;function r(e,t,r){for(var i=[],a=0,s=0;s<t;s++)if(s%4){var o=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;i[a>>>2]|=o<<24-a%4*8,a++}return n.create(i,a)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var i=[],a=0;a<n;a+=3)for(var s=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<n;o++)i.push(r.charAt(s>>>6*(3-o)&63));var l=r.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(e){var t=e.length,n=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var a=0;a<n.length;a++)i[n.charCodeAt(a)]=a}var s=n.charAt(64);if(s){var o=e.indexOf(s);-1!==o&&(t=o)}return r(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),t.enc.Base64)}()},548:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.pad.ZeroPadding={pad:function(e,t){var n=4*t;e.clamp(),e.sigBytes+=n-(e.sigBytes%n||n)},unpad:function(e){var t=e.words,n=e.sigBytes-1;for(n=e.sigBytes-1;n>=0;n--)if(t[n>>>2]>>>24-n%4*8&255){e.sigBytes=n+1;break}}},t.pad.ZeroPadding)}()},579:(e,t,n)=>{"use strict";e.exports=n(153)},600:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(749),function(e){var n=t,r=n.lib,i=r.WordArray,a=r.Hasher,s=n.x64.Word,o=n.algo,l=[],c=[],u=[];!function(){for(var e=1,t=0,n=0;n<24;n++){l[e+5*t]=(n+1)*(n+2)/2%64;var r=(2*e+3*t)%5;e=t%5,t=r}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,a=0;a<24;a++){for(var o=0,d=0,f=0;f<7;f++){if(1&i){var h=(1<<f)-1;h<32?d^=1<<h:o^=1<<h-32}128&i?i=i<<1^113:i<<=1}u[a]=s.create(o,d)}}();var d=[];!function(){for(var e=0;e<25;e++)d[e]=s.create()}();var f=o.SHA3=a.extend({cfg:a.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var n=this._state,r=this.blockSize/2,i=0;i<r;i++){var a=e[t+2*i],s=e[t+2*i+1];a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(C=n[i]).high^=s,C.low^=a}for(var o=0;o<24;o++){for(var f=0;f<5;f++){for(var h=0,p=0,m=0;m<5;m++)h^=(C=n[f+5*m]).high,p^=C.low;var g=d[f];g.high=h,g.low=p}for(f=0;f<5;f++){var v=d[(f+4)%5],y=d[(f+1)%5],b=y.high,x=y.low;for(h=v.high^(b<<1|x>>>31),p=v.low^(x<<1|b>>>31),m=0;m<5;m++)(C=n[f+5*m]).high^=h,C.low^=p}for(var w=1;w<25;w++){var S=(C=n[w]).high,k=C.low,N=l[w];N<32?(h=S<<N|k>>>32-N,p=k<<N|S>>>32-N):(h=k<<N-32|S>>>64-N,p=S<<N-32|k>>>64-N);var j=d[c[w]];j.high=h,j.low=p}var E=d[0],_=n[0];for(E.high=_.high,E.low=_.low,f=0;f<5;f++)for(m=0;m<5;m++){var C=n[w=f+5*m],T=d[w],B=d[(f+1)%5+5*m],D=d[(f+2)%5+5*m];C.high=T.high^~B.high&D.high,C.low=T.low^~B.low&D.low}C=n[0];var R=u[o];C.high^=R.high,C.low^=R.low}},_doFinalize:function(){var t=this._data,n=t.words,r=(this._nDataBytes,8*t.sigBytes),a=32*this.blockSize;n[r>>>5]|=1<<24-r%32,n[(e.ceil((r+1)/a)*a>>>5)-1]|=128,t.sigBytes=4*n.length,this._process();for(var s=this._state,o=this.cfg.outputLength/8,l=o/8,c=[],u=0;u<l;u++){var d=s[u],f=d.high,h=d.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),c.push(h),c.push(f)}return new i.init(c,o)},clone:function(){for(var e=a.clone.call(this),t=e._state=this._state.slice(0),n=0;n<25;n++)t[n]=t[n].clone();return e}});n.SHA3=a._createHelper(f),n.HmacSHA3=a._createHmacHelper(f)}(Math),t.SHA3)}()},638:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=r.RC4=n.extend({_doReset:function(){for(var e=this._key,t=e.words,n=e.sigBytes,r=this._S=[],i=0;i<256;i++)r[i]=i;i=0;for(var a=0;i<256;i++){var s=i%n,o=t[s>>>2]>>>24-s%4*8&255;a=(a+r[i]+o)%256;var l=r[i];r[i]=r[a],r[a]=l}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=a.call(this)},keySize:8,ivSize:0});function a(){for(var e=this._S,t=this._i,n=this._j,r=0,i=0;i<4;i++){n=(n+e[t=(t+1)%256])%256;var a=e[t];e[t]=e[n],e[n]=a,r|=e[(e[t]+e[n])%256]<<24-8*i}return this._i=t,this._j=n,r}e.RC4=n._createHelper(i);var s=r.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)a.call(this)}});e.RC4Drop=n._createHelper(s)}(),t.RC4)}()},641:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(368),function(){var e=t,n=e.lib.WordArray,r=e.algo,i=r.SHA256,a=r.SHA224=i.extend({_doReset:function(){this._hash=new n.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=i._doFinalize.call(this);return e.sigBytes-=4,e}});e.SHA224=i._createHelper(a),e.HmacSHA224=i._createHmacHelper(a)}(),t.SHA224)}()},646:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(){var e=t,n=e.lib.WordArray;function r(e,t,r){for(var i=[],a=0,s=0;s<t;s++)if(s%4){var o=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;i[a>>>2]|=o<<24-a%4*8,a++}return n.create(i,a)}e.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var n=e.words,r=e.sigBytes,i=t?this._safe_map:this._map;e.clamp();for(var a=[],s=0;s<r;s+=3)for(var o=(n[s>>>2]>>>24-s%4*8&255)<<16|(n[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|n[s+2>>>2]>>>24-(s+2)%4*8&255,l=0;l<4&&s+.75*l<r;l++)a.push(i.charAt(o>>>6*(3-l)&63));var c=i.charAt(64);if(c)for(;a.length%4;)a.push(c);return a.join("")},parse:function(e,t){void 0===t&&(t=!0);var n=e.length,i=t?this._safe_map:this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var s=0;s<i.length;s++)a[i.charCodeAt(s)]=s}var o=i.charAt(64);if(o){var l=e.indexOf(o);-1!==l&&(n=l)}return r(e,n,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),t.enc.Base64url)}()},659:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=[],a=[],s=[],o=r.RabbitLegacy=n.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],r=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)l.call(this);for(i=0;i<8;i++)r[i]^=n[i+4&7];if(t){var a=t.words,s=a[0],o=a[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),u=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),d=c>>>16|4294901760&u,f=u<<16|65535&c;for(r[0]^=c,r[1]^=d,r[2]^=u,r[3]^=f,r[4]^=c,r[5]^=d,r[6]^=u,r[7]^=f,i=0;i<4;i++)l.call(this)}},_doProcessBlock:function(e,t){var n=this._X;l.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),e[t+r]^=i[r]},blockSize:4,ivSize:2});function l(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((4294901760&r)*r|0)+((65535&r)*r|0);s[n]=l^c}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}e.RabbitLegacy=n._createHelper(o)}(),t.RabbitLegacy)}()},708:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),function(){var e=t,n=e.lib.CipherParams,r=e.enc.Hex;e.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){var t=r.parse(e);return n.create({ciphertext:t})}}}(),t.format.Hex)}()},730:(e,t,n)=>{"use strict";var r=n(43),i=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,o={};function l(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(o[e]=t,e=0;e<t.length;e++)s.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,i,a,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=s}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=g.hasOwnProperty(t)?g[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),_=Symbol.for("react.context"),C=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),B=Symbol.for("react.suspense_list"),D=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var P=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var O=Symbol.iterator;function A(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=O&&e[O]||e["@@iterator"])?e:null}var z,L=Object.assign;function M(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var I=!1;function F(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var i=c.stack.split("\n"),a=r.stack.split("\n"),s=i.length-1,o=a.length-1;1<=s&&0<=o&&i[s]!==a[o];)o--;for(;1<=s&&0<=o;s--,o--)if(i[s]!==a[o]){if(1!==s||1!==o)do{if(s--,0>--o||i[s]!==a[o]){var l="\n"+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=s&&0<=o);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function V(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=F(e.type,!1);case 11:return e=F(e.type.render,!1);case 1:return e=F(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case j:return"Profiler";case N:return"StrictMode";case T:return"Suspense";case B:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case _:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case C:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case D:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function U(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===N?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function q(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function W(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function K(e){e._valueTracker||(e._valueTracker=function(e){var t=W(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function $(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=W(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return L({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=q(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){X(e,t);var n=q(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,q(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Y(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+q(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return L({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:q(n)}}function ae(e,t){var n=q(t.value),r=q(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function se(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ue(e,t)})}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ve=L({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Ne=null;function je(e){if(e=bi(e)){if("function"!==typeof Se)throw Error(a(280));var t=e.stateNode;t&&(t=wi(t),Se(e.stateNode,e.type,t))}}function Ee(e){ke?Ne?Ne.push(e):Ne=[e]:ke=e}function _e(){if(ke){var e=ke,t=Ne;if(Ne=ke=null,je(e),t)for(e=0;e<t.length;e++)je(t[e])}}function Ce(e,t){return e(t)}function Te(){}var Be=!1;function De(e,t,n){if(Be)return e(t,n);Be=!0;try{return Ce(e,t,n)}finally{Be=!1,(null!==ke||null!==Ne)&&(Te(),_e())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wi(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var Pe=!1;if(u)try{var Oe={};Object.defineProperty(Oe,"passive",{get:function(){Pe=!0}}),window.addEventListener("test",Oe,Oe),window.removeEventListener("test",Oe,Oe)}catch(ue){Pe=!1}function Ae(e,t,n,r,i,a,s,o,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var ze=!1,Le=null,Me=!1,Ie=null,Fe={onError:function(e){ze=!0,Le=e}};function Ve(e,t,n,r,i,a,s,o,l){ze=!1,Le=null,Ae.apply(Fe,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ue(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function qe(e){if(He(e)!==e)throw Error(a(188))}function We(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var s=i.alternate;if(null===s){if(null!==(r=i.return)){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return qe(i),e;if(s===r)return qe(i),t;s=s.sibling}throw Error(a(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Ke(e):null}function Ke(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ke(e);if(null!==t)return t;e=e.sibling}return null}var $e=i.unstable_scheduleCallback,Qe=i.unstable_cancelCallback,Je=i.unstable_shouldYield,Ge=i.unstable_requestPaint,Xe=i.unstable_now,Ze=i.unstable_getCurrentPriorityLevel,Ye=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,at=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ot(e)/lt|0)|0},ot=Math.log,lt=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,a=e.pingedLanes,s=268435455&n;if(0!==s){var o=s&~i;0!==o?r=dt(o):0!==(a&=s)&&(r=dt(a))}else 0!==(s=n&~i)?r=dt(s):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(a=t&-t)||16===i&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-st(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Nt,jt,Et=!1,_t=[],Ct=null,Tt=null,Bt=null,Dt=new Map,Rt=new Map,Pt=[],Ot="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Bt=null;break;case"pointerover":case"pointerout":Dt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function zt(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[i]},null!==t&&(null!==(t=bi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Lt(e){var t=yi(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ue(n)))return e.blockedOn=t,void jt(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function It(e,t,n){Mt(e)&&n.delete(t)}function Ft(){Et=!1,null!==Ct&&Mt(Ct)&&(Ct=null),null!==Tt&&Mt(Tt)&&(Tt=null),null!==Bt&&Mt(Bt)&&(Bt=null),Dt.forEach(It),Rt.forEach(It)}function Vt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Ft)))}function Ht(e){function t(t){return Vt(t,e)}if(0<_t.length){Vt(_t[0],e);for(var n=1;n<_t.length;n++){var r=_t[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Vt(Ct,e),null!==Tt&&Vt(Tt,e),null!==Bt&&Vt(Bt,e),Dt.forEach(t),Rt.forEach(t),n=0;n<Pt.length;n++)(r=Pt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Pt.length&&null===(n=Pt[0]).blockedOn;)Lt(n),null===n.blockedOn&&Pt.shift()}var Ut=x.ReactCurrentBatchConfig,qt=!0;function Wt(e,t,n,r){var i=bt,a=Ut.transition;Ut.transition=null;try{bt=1,$t(e,t,n,r)}finally{bt=i,Ut.transition=a}}function Kt(e,t,n,r){var i=bt,a=Ut.transition;Ut.transition=null;try{bt=4,$t(e,t,n,r)}finally{bt=i,Ut.transition=a}}function $t(e,t,n,r){if(qt){var i=Jt(e,t,n,r);if(null===i)qr(e,t,r,Qt,n),At(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return Ct=zt(Ct,e,t,n,r,i),!0;case"dragenter":return Tt=zt(Tt,e,t,n,r,i),!0;case"mouseover":return Bt=zt(Bt,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return Dt.set(a,zt(Dt.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,Rt.set(a,zt(Rt.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Ot.indexOf(e)){for(;null!==i;){var a=bi(i);if(null!==a&&wt(a),null===(a=Jt(e,t,n,r))&&qr(e,t,r,Qt,n),a===i)break;i=a}null!==i&&r.stopPropagation()}else qr(e,t,r,null,n)}}var Qt=null;function Jt(e,t,n,r){if(Qt=null,null!==(e=yi(e=we(r))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ue(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Ye:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,Yt=null;function en(){if(Yt)return Yt;var e,t,n=Zt,r=n.length,i="value"in Xt?Xt.value:Xt.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===i[a-t];t++);return Yt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,i,a){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(i):i[s]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return L(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var sn,on,ln,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=an(cn),dn=L({},cn,{view:0,detail:0}),fn=an(dn),hn=L({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(sn=e.screenX-ln.screenX,on=e.screenY-ln.screenY):on=sn=0,ln=e),sn)},movementY:function(e){return"movementY"in e?e.movementY:on}}),pn=an(hn),mn=an(L({},hn,{dataTransfer:0})),gn=an(L({},dn,{relatedTarget:0})),vn=an(L({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=L({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),xn=an(L({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function jn(){return Nn}var En=L({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_n=an(En),Cn=an(L({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(L({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),Bn=an(L({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Dn=L({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(Dn),Pn=[9,13,27,32],On=u&&"CompositionEvent"in window,An=null;u&&"documentMode"in document&&(An=document.documentMode);var zn=u&&"TextEvent"in window&&!An,Ln=u&&(!On||An&&8<An&&11>=An),Mn=String.fromCharCode(32),In=!1;function Fn(e,t){switch(e){case"keyup":return-1!==Pn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Hn=!1;var Un={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Un[e.type]:"textarea"===t}function Wn(e,t,n,r){Ee(r),0<(t=Kr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,$n=null;function Qn(e){Mr(e,0)}function Jn(e){if($(xi(e)))return e}function Gn(e,t){if("change"===e)return t}var Xn=!1;if(u){var Zn;if(u){var Yn="oninput"in document;if(!Yn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Yn="function"===typeof er.oninput}Zn=Yn}else Zn=!1;Xn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Kn&&(Kn.detachEvent("onpropertychange",nr),$n=Kn=null)}function nr(e){if("value"===e.propertyName&&Jn($n)){var t=[];Wn(t,$n,e,we(e)),De(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),$n=n,(Kn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn($n)}function ar(e,t){if("click"===e)return Jn(t)}function sr(e,t){if("input"===e||"change"===e)return Jn(t)}var or="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(or(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!d.call(t,i)||!or(e[i],t[i]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,a=Math.min(r.start,i);r=void 0===r.end?a:Math.min(r.end,i),!e.extend&&a>r&&(i=r,r=a,a=i),i=ur(n,a);var s=ur(n,r);i&&s&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,vr=null,yr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&lr(yr,r)||(yr=r,0<(r=Kr(vr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Nr={};function jr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Nr)return kr[e]=n[t];return e}u&&(Nr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Er=jr("animationend"),_r=jr("animationiteration"),Cr=jr("animationstart"),Tr=jr("transitionend"),Br=new Map,Dr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){Br.set(e,t),l(t,[e])}for(var Pr=0;Pr<Dr.length;Pr++){var Or=Dr[Pr];Rr(Or.toLowerCase(),"on"+(Or[0].toUpperCase()+Or.slice(1)))}Rr(Er,"onAnimationEnd"),Rr(_r,"onAnimationIteration"),Rr(Cr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Tr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ar="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ar));function Lr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,s,o,l,c){if(Ve.apply(this,arguments),ze){if(!ze)throw Error(a(198));var u=Le;ze=!1,Le=null,Me||(Me=!0,Ie=u)}}(r,t,void 0,e),e.currentTarget=null}function Mr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var s=r.length-1;0<=s;s--){var o=r[s],l=o.instance,c=o.currentTarget;if(o=o.listener,l!==a&&i.isPropagationStopped())break e;Lr(i,o,c),a=l}else for(s=0;s<r.length;s++){if(l=(o=r[s]).instance,c=o.currentTarget,o=o.listener,l!==a&&i.isPropagationStopped())break e;Lr(i,o,c),a=l}}}if(Me)throw e=Ie,Me=!1,Ie=null,e}function Ir(e,t){var n=t[mi];void 0===n&&(n=t[mi]=new Set);var r=e+"__bubble";n.has(r)||(Ur(t,e,2,!1),n.add(r))}function Fr(e,t,n){var r=0;t&&(r|=4),Ur(n,e,r,t)}var Vr="_reactListening"+Math.random().toString(36).slice(2);function Hr(e){if(!e[Vr]){e[Vr]=!0,s.forEach(function(t){"selectionchange"!==t&&(zr.has(t)||Fr(t,!1,e),Fr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Vr]||(t[Vr]=!0,Fr("selectionchange",!1,t))}}function Ur(e,t,n,r){switch(Gt(t)){case 1:var i=Wt;break;case 4:i=Kt;break;default:i=$t}n=i.bind(null,t,n,e),i=void 0,!Pe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function qr(e,t,n,r,i){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var s=r.tag;if(3===s||4===s){var o=r.stateNode.containerInfo;if(o===i||8===o.nodeType&&o.parentNode===i)break;if(4===s)for(s=r.return;null!==s;){var l=s.tag;if((3===l||4===l)&&((l=s.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;s=s.return}for(;null!==o;){if(null===(s=yi(o)))return;if(5===(l=s.tag)||6===l){r=a=s;continue e}o=o.parentNode}}r=r.return}De(function(){var r=a,i=we(n),s=[];e:{var o=Br.get(e);if(void 0!==o){var l=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=_n;break;case"focusin":c="focus",l=gn;break;case"focusout":c="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Tn;break;case Er:case _r:case Cr:l=vn;break;case Tr:l=Bn;break;case"scroll":l=fn;break;case"wheel":l=Rn;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Cn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==o?o+"Capture":null:o;u=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Re(p,f))&&u.push(Wr(p,m,h)))),d)break;p=p.return}0<u.length&&(o=new l(o,c,null,n,i),s.push({event:o,listeners:u}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===xe||!(c=n.relatedTarget||n.fromElement)||!yi(c)&&!c[pi])&&(l||o)&&(o=i.window===i?i:(o=i.ownerDocument)?o.defaultView||o.parentWindow:window,l?(l=r,null!==(c=(c=n.relatedTarget||n.toElement)?yi(c):null)&&(c!==(d=He(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(l=null,c=r),l!==c)){if(u=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=Cn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?o:xi(l),h=null==c?o:xi(c),(o=new u(m,p+"leave",l,n,i)).target=d,o.relatedTarget=h,m=null,yi(i)===r&&((u=new u(f,p+"enter",c,n,i)).target=h,u.relatedTarget=d,m=u),d=m,l&&c)e:{for(f=c,p=0,h=u=l;h;h=$r(h))p++;for(h=0,m=f;m;m=$r(m))h++;for(;0<p-h;)u=$r(u),p--;for(;0<h-p;)f=$r(f),h--;for(;p--;){if(u===f||null!==f&&u===f.alternate)break e;u=$r(u),f=$r(f)}u=null}else u=null;null!==l&&Qr(s,o,l,u,!1),null!==c&&null!==d&&Qr(s,d,c,u,!0)}if("select"===(l=(o=r?xi(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===l&&"file"===o.type)var g=Gn;else if(qn(o))if(Xn)g=sr;else{g=ir;var v=rr}else(l=o.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(g=ar);switch(g&&(g=g(e,r))?Wn(s,g,n,i):(v&&v(e,o,r),"focusout"===e&&(v=o._wrapperState)&&v.controlled&&"number"===o.type&&ee(o,"number",o.value)),v=r?xi(r):window,e){case"focusin":(qn(v)||"true"===v.contentEditable)&&(gr=v,vr=r,yr=null);break;case"focusout":yr=vr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(s,n,i);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(s,n,i)}var y;if(On)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Hn?Fn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Ln&&"ko"!==n.locale&&(Hn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Hn&&(y=en()):(Zt="value"in(Xt=i)?Xt.value:Xt.textContent,Hn=!0)),0<(v=Kr(r,b)).length&&(b=new xn(b,e,null,n,i),s.push({event:b,listeners:v}),y?b.data=y:null!==(y=Vn(n))&&(b.data=y))),(y=zn?function(e,t){switch(e){case"compositionend":return Vn(t);case"keypress":return 32!==t.which?null:(In=!0,Mn);case"textInput":return(e=t.data)===Mn&&In?null:e;default:return null}}(e,n):function(e,t){if(Hn)return"compositionend"===e||!On&&Fn(e,t)?(e=en(),Yt=Zt=Xt=null,Hn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Kr(r,"onBeforeInput")).length&&(i=new xn("onBeforeInput","beforeinput",null,n,i),s.push({event:i,listeners:r}),i.data=y))}Mr(s,t)})}function Wr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,a=i.stateNode;5===i.tag&&null!==a&&(i=a,null!=(a=Re(e,n))&&r.unshift(Wr(e,a,i)),null!=(a=Re(e,t))&&r.push(Wr(e,a,i))),e=e.return}return r}function $r(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,i){for(var a=t._reactName,s=[];null!==n&&n!==r;){var o=n,l=o.alternate,c=o.stateNode;if(null!==l&&l===r)break;5===o.tag&&null!==c&&(o=c,i?null!=(l=Re(n,a))&&s.unshift(Wr(n,l,o)):i||null!=(l=Re(n,a))&&s.push(Wr(n,l,o))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Jr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Gr,"")}function Zr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(a(425))}function Yr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"===typeof setTimeout?setTimeout:void 0,ii="function"===typeof clearTimeout?clearTimeout:void 0,ai="function"===typeof Promise?Promise:void 0,si="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ai?function(e){return ai.resolve(null).then(e).catch(oi)}:ri;function oi(e){setTimeout(function(){throw e})}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void Ht(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);Ht(t)}function ci(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ui(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var di=Math.random().toString(36).slice(2),fi="__reactFiber$"+di,hi="__reactProps$"+di,pi="__reactContainer$"+di,mi="__reactEvents$"+di,gi="__reactListeners$"+di,vi="__reactHandles$"+di;function yi(e){var t=e[fi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[fi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ui(e);null!==e;){if(n=e[fi])return n;e=ui(e)}return t}n=(e=n).parentNode}return null}function bi(e){return!(e=e[fi]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function wi(e){return e[hi]||null}var Si=[],ki=-1;function Ni(e){return{current:e}}function ji(e){0>ki||(e.current=Si[ki],Si[ki]=null,ki--)}function Ei(e,t){ki++,Si[ki]=e.current,e.current=t}var _i={},Ci=Ni(_i),Ti=Ni(!1),Bi=_i;function Di(e,t){var n=e.type.contextTypes;if(!n)return _i;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Ri(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Pi(){ji(Ti),ji(Ci)}function Oi(e,t,n){if(Ci.current!==_i)throw Error(a(168));Ei(Ci,t),Ei(Ti,n)}function Ai(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(a(108,U(e)||"Unknown",i));return L({},n,r)}function zi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_i,Bi=Ci.current,Ei(Ci,e),Ei(Ti,Ti.current),!0}function Li(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Ai(e,t,Bi),r.__reactInternalMemoizedMergedChildContext=e,ji(Ti),ji(Ci),Ei(Ci,e)):ji(Ti),Ei(Ti,n)}var Mi=null,Ii=!1,Fi=!1;function Vi(e){null===Mi?Mi=[e]:Mi.push(e)}function Hi(){if(!Fi&&null!==Mi){Fi=!0;var e=0,t=bt;try{var n=Mi;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Mi=null,Ii=!1}catch(i){throw null!==Mi&&(Mi=Mi.slice(e+1)),$e(Ye,Hi),i}finally{bt=t,Fi=!1}}return null}var Ui=[],qi=0,Wi=null,Ki=0,$i=[],Qi=0,Ji=null,Gi=1,Xi="";function Zi(e,t){Ui[qi++]=Ki,Ui[qi++]=Wi,Wi=e,Ki=t}function Yi(e,t,n){$i[Qi++]=Gi,$i[Qi++]=Xi,$i[Qi++]=Ji,Ji=e;var r=Gi;e=Xi;var i=32-st(r)-1;r&=~(1<<i),n+=1;var a=32-st(t)+i;if(30<a){var s=i-i%5;a=(r&(1<<s)-1).toString(32),r>>=s,i-=s,Gi=1<<32-st(t)+i|n<<i|r,Xi=a+e}else Gi=1<<a|n<<i|r,Xi=e}function ea(e){null!==e.return&&(Zi(e,1),Yi(e,1,0))}function ta(e){for(;e===Wi;)Wi=Ui[--qi],Ui[qi]=null,Ki=Ui[--qi],Ui[qi]=null;for(;e===Ji;)Ji=$i[--Qi],$i[Qi]=null,Xi=$i[--Qi],$i[Qi]=null,Gi=$i[--Qi],$i[Qi]=null}var na=null,ra=null,ia=!1,aa=null;function sa(e,t){var n=Dc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function oa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,na=e,ra=ci(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,na=e,ra=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ji?{id:Gi,overflow:Xi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Dc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,na=e,ra=null,!0);default:return!1}}function la(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ca(e){if(ia){var t=ra;if(t){var n=t;if(!oa(e,t)){if(la(e))throw Error(a(418));t=ci(n.nextSibling);var r=na;t&&oa(e,t)?sa(r,n):(e.flags=-4097&e.flags|2,ia=!1,na=e)}}else{if(la(e))throw Error(a(418));e.flags=-4097&e.flags|2,ia=!1,na=e}}}function ua(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;na=e}function da(e){if(e!==na)return!1;if(!ia)return ua(e),ia=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ra)){if(la(e))throw fa(),Error(a(418));for(;t;)sa(e,t),t=ci(t.nextSibling)}if(ua(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ra=ci(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ra=null}}else ra=na?ci(e.stateNode.nextSibling):null;return!0}function fa(){for(var e=ra;e;)e=ci(e.nextSibling)}function ha(){ra=na=null,ia=!1}function pa(e){null===aa?aa=[e]:aa.push(e)}var ma=x.ReactCurrentBatchConfig;function ga(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var i=r,s=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===s?t.ref:(t=function(e){var t=i.refs;null===e?delete t[s]:t[s]=e},t._stringRef=s,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function va(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ya(e){return(0,e._init)(e._payload)}function ba(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Pc(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Lc(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===R&&ya(a)===t.type)?((r=i(t,n.props)).ref=ga(e,t,n),r.return=e,r):((r=Oc(n.type,n.key,n.props,null,e.mode,r)).ref=ga(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Mc(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Ac(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Lc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Oc(t.type,t.key,t.props,null,e.mode,n)).ref=ga(e,null,t),n.return=e,n;case S:return(t=Mc(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||A(t))return(t=Ac(t,e.mode,n,null)).return=e,t;va(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===i?c(e,t,n,r):null;case S:return n.key===i?u(e,t,n,r):null;case R:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||A(n))return null!==i?null:d(e,t,n,r,null);va(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case R:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||A(r))return d(t,e=e.get(n)||null,r,i,null);va(t,r)}return null}function m(i,a,o,l){for(var c=null,u=null,d=a,m=a=0,g=null;null!==d&&m<o.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=h(i,d,o[m],l);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(i,d),a=s(v,a,m),null===u?c=v:u.sibling=v,u=v,d=g}if(m===o.length)return n(i,d),ia&&Zi(i,m),c;if(null===d){for(;m<o.length;m++)null!==(d=f(i,o[m],l))&&(a=s(d,a,m),null===u?c=d:u.sibling=d,u=d);return ia&&Zi(i,m),c}for(d=r(i,d);m<o.length;m++)null!==(g=p(d,i,m,o[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),a=s(g,a,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach(function(e){return t(i,e)}),ia&&Zi(i,m),c}function g(i,o,l,c){var u=A(l);if("function"!==typeof u)throw Error(a(150));if(null==(l=u.call(l)))throw Error(a(151));for(var d=u=null,m=o,g=o=0,v=null,y=l.next();null!==m&&!y.done;g++,y=l.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=h(i,m,y.value,c);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(i,m),o=s(b,o,g),null===d?u=b:d.sibling=b,d=b,m=v}if(y.done)return n(i,m),ia&&Zi(i,g),u;if(null===m){for(;!y.done;g++,y=l.next())null!==(y=f(i,y.value,c))&&(o=s(y,o,g),null===d?u=y:d.sibling=y,d=y);return ia&&Zi(i,g),u}for(m=r(i,m);!y.done;g++,y=l.next())null!==(y=p(m,i,g,y.value,c))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),o=s(y,o,g),null===d?u=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(i,e)}),ia&&Zi(i,g),u}return function e(r,a,s,l){if("object"===typeof s&&null!==s&&s.type===k&&null===s.key&&(s=s.props.children),"object"===typeof s&&null!==s){switch(s.$$typeof){case w:e:{for(var c=s.key,u=a;null!==u;){if(u.key===c){if((c=s.type)===k){if(7===u.tag){n(r,u.sibling),(a=i(u,s.props.children)).return=r,r=a;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===R&&ya(c)===u.type){n(r,u.sibling),(a=i(u,s.props)).ref=ga(r,u,s),a.return=r,r=a;break e}n(r,u);break}t(r,u),u=u.sibling}s.type===k?((a=Ac(s.props.children,r.mode,l,s.key)).return=r,r=a):((l=Oc(s.type,s.key,s.props,null,r.mode,l)).ref=ga(r,a,s),l.return=r,r=l)}return o(r);case S:e:{for(u=s.key;null!==a;){if(a.key===u){if(4===a.tag&&a.stateNode.containerInfo===s.containerInfo&&a.stateNode.implementation===s.implementation){n(r,a.sibling),(a=i(a,s.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=Mc(s,r.mode,l)).return=r,r=a}return o(r);case R:return e(r,a,(u=s._init)(s._payload),l)}if(te(s))return m(r,a,s,l);if(A(s))return g(r,a,s,l);va(r,s)}return"string"===typeof s&&""!==s||"number"===typeof s?(s=""+s,null!==a&&6===a.tag?(n(r,a.sibling),(a=i(a,s)).return=r,r=a):(n(r,a),(a=Lc(s,r.mode,l)).return=r,r=a),o(r)):n(r,a)}}var xa=ba(!0),wa=ba(!1),Sa=Ni(null),ka=null,Na=null,ja=null;function Ea(){ja=Na=ka=null}function _a(e){var t=Sa.current;ji(Sa),e._currentValue=t}function Ca(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ta(e,t){ka=e,ja=Na=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bo=!0),e.firstContext=null)}function Ba(e){var t=e._currentValue;if(ja!==e)if(e={context:e,memoizedValue:t,next:null},null===Na){if(null===ka)throw Error(a(308));Na=e,ka.dependencies={lanes:0,firstContext:e}}else Na=Na.next=e;return t}var Da=null;function Ra(e){null===Da?Da=[e]:Da.push(e)}function Pa(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,Ra(t)):(n.next=i.next,i.next=n),t.interleaved=n,Oa(e,r)}function Oa(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Aa=!1;function za(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function La(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ma(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ia(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Cl)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Oa(e,n)}return null===(i=r.interleaved)?(t.next=t,Ra(r)):(t.next=i.next,i.next=t),r.interleaved=t,Oa(e,n)}function Fa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function Va(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?i=a=s:a=a.next=s,n=n.next}while(null!==n);null===a?i=a=t:a=a.next=t}else i=a=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ha(e,t,n,r){var i=e.updateQueue;Aa=!1;var a=i.firstBaseUpdate,s=i.lastBaseUpdate,o=i.shared.pending;if(null!==o){i.shared.pending=null;var l=o,c=l.next;l.next=null,null===s?a=c:s.next=c,s=l;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==s&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=l))}if(null!==a){var d=i.baseState;for(s=0,u=c=l=null,o=a;;){var f=o.lane,h=o.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:h,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var p=e,m=o;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=L({},d,f);break e;case 2:Aa=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[o]:f.push(o))}else h={eventTime:h,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=h,l=d):u=u.next=h,s|=f;if(null===(o=o.next)){if(null===(o=i.shared.pending))break;o=(f=o).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===u&&(l=d),i.baseState=l,i.firstBaseUpdate=c,i.lastBaseUpdate=u,null!==(t=i.shared.interleaved)){i=t;do{s|=i.lane,i=i.next}while(i!==t)}else null===a&&(i.shared.lanes=0);zl|=s,e.lanes=s,e.memoizedState=d}}function Ua(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!==typeof i)throw Error(a(191,i));i.call(r)}}}var qa={},Wa=Ni(qa),Ka=Ni(qa),$a=Ni(qa);function Qa(e){if(e===qa)throw Error(a(174));return e}function Ja(e,t){switch(Ei($a,t),Ei(Ka,e),Ei(Wa,qa),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ji(Wa),Ei(Wa,t)}function Ga(){ji(Wa),ji(Ka),ji($a)}function Xa(e){Qa($a.current);var t=Qa(Wa.current),n=le(t,e.type);t!==n&&(Ei(Ka,e),Ei(Wa,n))}function Za(e){Ka.current===e&&(ji(Wa),ji(Ka))}var Ya=Ni(0);function es(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ts=[];function ns(){for(var e=0;e<ts.length;e++)ts[e]._workInProgressVersionPrimary=null;ts.length=0}var rs=x.ReactCurrentDispatcher,is=x.ReactCurrentBatchConfig,as=0,ss=null,os=null,ls=null,cs=!1,us=!1,ds=0,fs=0;function hs(){throw Error(a(321))}function ps(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!or(e[n],t[n]))return!1;return!0}function ms(e,t,n,r,i,s){if(as=s,ss=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,rs.current=null===e||null===e.memoizedState?Zs:Ys,e=n(r,i),us){s=0;do{if(us=!1,ds=0,25<=s)throw Error(a(301));s+=1,ls=os=null,t.updateQueue=null,rs.current=eo,e=n(r,i)}while(us)}if(rs.current=Xs,t=null!==os&&null!==os.next,as=0,ls=os=ss=null,cs=!1,t)throw Error(a(300));return e}function gs(){var e=0!==ds;return ds=0,e}function vs(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ls?ss.memoizedState=ls=e:ls=ls.next=e,ls}function ys(){if(null===os){var e=ss.alternate;e=null!==e?e.memoizedState:null}else e=os.next;var t=null===ls?ss.memoizedState:ls.next;if(null!==t)ls=t,os=e;else{if(null===e)throw Error(a(310));e={memoizedState:(os=e).memoizedState,baseState:os.baseState,baseQueue:os.baseQueue,queue:os.queue,next:null},null===ls?ss.memoizedState=ls=e:ls=ls.next=e}return ls}function bs(e,t){return"function"===typeof t?t(e):t}function xs(e){var t=ys(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=os,i=r.baseQueue,s=n.pending;if(null!==s){if(null!==i){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(null!==i){s=i.next,r=r.baseState;var l=o=null,c=null,u=s;do{var d=u.lane;if((as&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(l=c=f,o=r):c=c.next=f,ss.lanes|=d,zl|=d}u=u.next}while(null!==u&&u!==s);null===c?o=r:c.next=l,or(r,t.memoizedState)||(bo=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{s=i.lane,ss.lanes|=s,zl|=s,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ws(e){var t=ys(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(null!==i){n.pending=null;var o=i=i.next;do{s=e(s,o.action),o=o.next}while(o!==i);or(s,t.memoizedState)||(bo=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Ss(){}function ks(e,t){var n=ss,r=ys(),i=t(),s=!or(r.memoizedState,i);if(s&&(r.memoizedState=i,bo=!0),r=r.queue,As(Es.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||null!==ls&&1&ls.memoizedState.tag){if(n.flags|=2048,Bs(9,js.bind(null,n,r,i,t),void 0,null),null===Tl)throw Error(a(349));0!==(30&as)||Ns(n,t,i)}return i}function Ns(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function js(e,t,n,r){t.value=n,t.getSnapshot=r,_s(t)&&Cs(e)}function Es(e,t,n){return n(function(){_s(t)&&Cs(e)})}function _s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!or(e,n)}catch(r){return!0}}function Cs(e){var t=Oa(e,1);null!==t&&nc(t,e,1,-1)}function Ts(e){var t=vs();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:bs,lastRenderedState:e},t.queue=e,e=e.dispatch=$s.bind(null,ss,e),[t.memoizedState,e]}function Bs(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ss.updateQueue)?(t={lastEffect:null,stores:null},ss.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ds(){return ys().memoizedState}function Rs(e,t,n,r){var i=vs();ss.flags|=e,i.memoizedState=Bs(1|t,n,void 0,void 0===r?null:r)}function Ps(e,t,n,r){var i=ys();r=void 0===r?null:r;var a=void 0;if(null!==os){var s=os.memoizedState;if(a=s.destroy,null!==r&&ps(r,s.deps))return void(i.memoizedState=Bs(t,n,a,r))}ss.flags|=e,i.memoizedState=Bs(1|t,n,a,r)}function Os(e,t){return Rs(8390656,8,e,t)}function As(e,t){return Ps(2048,8,e,t)}function zs(e,t){return Ps(4,2,e,t)}function Ls(e,t){return Ps(4,4,e,t)}function Ms(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Is(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ps(4,4,Ms.bind(null,t,e),n)}function Fs(){}function Vs(e,t){var n=ys();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ps(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Hs(e,t){var n=ys();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ps(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Us(e,t,n){return 0===(21&as)?(e.baseState&&(e.baseState=!1,bo=!0),e.memoizedState=n):(or(n,t)||(n=mt(),ss.lanes|=n,zl|=n,e.baseState=!0),t)}function qs(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=is.transition;is.transition={};try{e(!1),t()}finally{bt=n,is.transition=r}}function Ws(){return ys().memoizedState}function Ks(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Qs(e))Js(t,n);else if(null!==(n=Pa(e,t,n,r))){nc(n,e,r,ec()),Gs(n,t,r)}}function $s(e,t,n){var r=tc(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qs(e))Js(t,i);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var s=t.lastRenderedState,o=a(s,n);if(i.hasEagerState=!0,i.eagerState=o,or(o,s)){var l=t.interleaved;return null===l?(i.next=i,Ra(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(c){}null!==(n=Pa(e,t,i,r))&&(nc(n,e,r,i=ec()),Gs(n,t,r))}}function Qs(e){var t=e.alternate;return e===ss||null!==t&&t===ss}function Js(e,t){us=cs=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gs(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Xs={readContext:Ba,useCallback:hs,useContext:hs,useEffect:hs,useImperativeHandle:hs,useInsertionEffect:hs,useLayoutEffect:hs,useMemo:hs,useReducer:hs,useRef:hs,useState:hs,useDebugValue:hs,useDeferredValue:hs,useTransition:hs,useMutableSource:hs,useSyncExternalStore:hs,useId:hs,unstable_isNewReconciler:!1},Zs={readContext:Ba,useCallback:function(e,t){return vs().memoizedState=[e,void 0===t?null:t],e},useContext:Ba,useEffect:Os,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Rs(4194308,4,Ms.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Rs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Rs(4,2,e,t)},useMemo:function(e,t){var n=vs();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vs();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ks.bind(null,ss,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vs().memoizedState=e},useState:Ts,useDebugValue:Fs,useDeferredValue:function(e){return vs().memoizedState=e},useTransition:function(){var e=Ts(!1),t=e[0];return e=qs.bind(null,e[1]),vs().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ss,i=vs();if(ia){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Tl)throw Error(a(349));0!==(30&as)||Ns(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Os(Es.bind(null,r,s,e),[e]),r.flags|=2048,Bs(9,js.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=vs(),t=Tl.identifierPrefix;if(ia){var n=Xi;t=":"+t+"R"+(n=(Gi&~(1<<32-st(Gi)-1)).toString(32)+n),0<(n=ds++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=fs++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ys={readContext:Ba,useCallback:Vs,useContext:Ba,useEffect:As,useImperativeHandle:Is,useInsertionEffect:zs,useLayoutEffect:Ls,useMemo:Hs,useReducer:xs,useRef:Ds,useState:function(){return xs(bs)},useDebugValue:Fs,useDeferredValue:function(e){return Us(ys(),os.memoizedState,e)},useTransition:function(){return[xs(bs)[0],ys().memoizedState]},useMutableSource:Ss,useSyncExternalStore:ks,useId:Ws,unstable_isNewReconciler:!1},eo={readContext:Ba,useCallback:Vs,useContext:Ba,useEffect:As,useImperativeHandle:Is,useInsertionEffect:zs,useLayoutEffect:Ls,useMemo:Hs,useReducer:ws,useRef:Ds,useState:function(){return ws(bs)},useDebugValue:Fs,useDeferredValue:function(e){var t=ys();return null===os?t.memoizedState=e:Us(t,os.memoizedState,e)},useTransition:function(){return[ws(bs)[0],ys().memoizedState]},useMutableSource:Ss,useSyncExternalStore:ks,useId:Ws,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var n in t=L({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function no(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:L({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ro={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),i=tc(e),a=Ma(r,i);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ia(e,a,i))&&(nc(t,e,i,r),Fa(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),i=tc(e),a=Ma(r,i);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ia(e,a,i))&&(nc(t,e,i,r),Fa(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),i=Ma(n,r);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=Ia(e,i,r))&&(nc(t,e,r,n),Fa(t,e,r))}};function io(e,t,n,r,i,a,s){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,s):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,a))}function ao(e,t,n){var r=!1,i=_i,a=t.contextType;return"object"===typeof a&&null!==a?a=Ba(a):(i=Ri(t)?Bi:Ci.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Di(e,i):_i),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ro,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function so(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ro.enqueueReplaceState(t,t.state,null)}function oo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},za(e);var a=t.contextType;"object"===typeof a&&null!==a?i.context=Ba(a):(a=Ri(t)?Bi:Ci.current,i.context=Di(e,a)),i.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(no(e,t,a,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ro.enqueueReplaceState(i,i.state,null),Ha(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.flags|=4194308)}function lo(e,t){try{var n="",r=t;do{n+=V(r),r=r.return}while(r);var i=n}catch(a){i="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:i,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fo="function"===typeof WeakMap?WeakMap:Map;function ho(e,t,n){(n=Ma(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ql||(ql=!0,Wl=r),uo(0,t)},n}function po(e,t,n){(n=Ma(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){uo(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){uo(0,t),"function"!==typeof r&&(null===Kl?Kl=new Set([this]):Kl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mo(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fo;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=jc.bind(null,e,t,n),t.then(e,e))}function go(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vo(e,t,n,r,i){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ma(-1,1)).tag=2,Ia(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var yo=x.ReactCurrentOwner,bo=!1;function xo(e,t,n,r){t.child=null===e?wa(t,null,n,r):xa(t,e.child,n,r)}function wo(e,t,n,r,i){n=n.render;var a=t.ref;return Ta(t,i),r=ms(e,t,n,r,a,i),n=gs(),null===e||bo?(ia&&n&&ea(t),t.flags|=1,xo(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,qo(e,t,i))}function So(e,t,n,r,i){if(null===e){var a=n.type;return"function"!==typeof a||Rc(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Oc(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,ko(e,t,a,r,i))}if(a=e.child,0===(e.lanes&i)){var s=a.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(s,r)&&e.ref===t.ref)return qo(e,t,i)}return t.flags|=1,(e=Pc(a,r)).ref=t.ref,e.return=t,t.child=e}function ko(e,t,n,r,i){if(null!==e){var a=e.memoizedProps;if(lr(a,r)&&e.ref===t.ref){if(bo=!1,t.pendingProps=r=a,0===(e.lanes&i))return t.lanes=e.lanes,qo(e,t,i);0!==(131072&e.flags)&&(bo=!0)}}return Eo(e,t,n,r,i)}function No(e,t,n){var r=t.pendingProps,i=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ei(Pl,Rl),Rl|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ei(Pl,Rl),Rl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Ei(Pl,Rl),Rl|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Ei(Pl,Rl),Rl|=r;return xo(e,t,i,n),t.child}function jo(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Eo(e,t,n,r,i){var a=Ri(n)?Bi:Ci.current;return a=Di(t,a),Ta(t,i),n=ms(e,t,n,r,a,i),r=gs(),null===e||bo?(ia&&r&&ea(t),t.flags|=1,xo(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,qo(e,t,i))}function _o(e,t,n,r,i){if(Ri(n)){var a=!0;zi(t)}else a=!1;if(Ta(t,i),null===t.stateNode)Uo(e,t),ao(t,n,r),oo(t,n,r,i),r=!0;else if(null===e){var s=t.stateNode,o=t.memoizedProps;s.props=o;var l=s.context,c=n.contextType;"object"===typeof c&&null!==c?c=Ba(c):c=Di(t,c=Ri(n)?Bi:Ci.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof s.getSnapshotBeforeUpdate;d||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(o!==r||l!==c)&&so(t,s,r,c),Aa=!1;var f=t.memoizedState;s.state=f,Ha(t,r,s,i),l=t.memoizedState,o!==r||f!==l||Ti.current||Aa?("function"===typeof u&&(no(t,n,u,r),l=t.memoizedState),(o=Aa||io(t,n,o,r,f,l,c))?(d||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(t.flags|=4194308)):("function"===typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=c,r=o):("function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,La(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:to(t.type,o),s.props=c,d=t.pendingProps,f=s.context,"object"===typeof(l=n.contextType)&&null!==l?l=Ba(l):l=Di(t,l=Ri(n)?Bi:Ci.current);var h=n.getDerivedStateFromProps;(u="function"===typeof h||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(o!==d||f!==l)&&so(t,s,r,l),Aa=!1,f=t.memoizedState,s.state=f,Ha(t,r,s,i);var p=t.memoizedState;o!==d||f!==p||Ti.current||Aa?("function"===typeof h&&(no(t,n,h,r),p=t.memoizedState),(c=Aa||io(t,n,c,r,f,p,l)||!1)?(u||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,p,l),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof s.componentDidUpdate&&(t.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),s.props=r,s.state=p,s.context=l,r=c):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Co(e,t,n,r,a,i)}function Co(e,t,n,r,i,a){jo(e,t);var s=0!==(128&t.flags);if(!r&&!s)return i&&Li(t,n,!1),qo(e,t,a);r=t.stateNode,yo.current=t;var o=s&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&s?(t.child=xa(t,e.child,null,a),t.child=xa(t,null,o,a)):xo(e,t,o,a),t.memoizedState=r.state,i&&Li(t,n,!0),t.child}function To(e){var t=e.stateNode;t.pendingContext?Oi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Oi(0,t.context,!1),Ja(e,t.containerInfo)}function Bo(e,t,n,r,i){return ha(),pa(i),t.flags|=256,xo(e,t,n,r),t.child}var Do,Ro,Po,Oo,Ao={dehydrated:null,treeContext:null,retryLane:0};function zo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Lo(e,t,n){var r,i=t.pendingProps,s=Ya.current,o=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&s)),r?(o=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(s|=1),Ei(Ya,1&s),null===e)return ca(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,o?(i=t.mode,o=t.child,l={mode:"hidden",children:l},0===(1&i)&&null!==o?(o.childLanes=0,o.pendingProps=l):o=zc(l,i,0,null),e=Ac(e,i,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=zo(n),t.memoizedState=Ao,e):Mo(t,l));if(null!==(s=e.memoizedState)&&null!==(r=s.dehydrated))return function(e,t,n,r,i,s,o){if(n)return 256&t.flags?(t.flags&=-257,Io(e,t,o,r=co(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=zc({mode:"visible",children:r.children},i,0,null),(s=Ac(s,i,o,null)).flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,0!==(1&t.mode)&&xa(t,e.child,null,o),t.child.memoizedState=zo(o),t.memoizedState=Ao,s);if(0===(1&t.mode))return Io(e,t,o,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Io(e,t,o,r=co(s=Error(a(419)),r,void 0))}if(l=0!==(o&e.childLanes),bo||l){if(null!==(r=Tl)){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!==(i&(r.suspendedLanes|o))?0:i)&&i!==s.retryLane&&(s.retryLane=i,Oa(e,i),nc(r,e,i,-1))}return mc(),Io(e,t,o,r=co(Error(a(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=_c.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,ra=ci(i.nextSibling),na=t,ia=!0,aa=null,null!==e&&($i[Qi++]=Gi,$i[Qi++]=Xi,$i[Qi++]=Ji,Gi=e.id,Xi=e.overflow,Ji=t),t=Mo(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,s,n);if(o){o=i.fallback,l=t.mode,r=(s=e.child).sibling;var c={mode:"hidden",children:i.children};return 0===(1&l)&&t.child!==s?((i=t.child).childLanes=0,i.pendingProps=c,t.deletions=null):(i=Pc(s,c)).subtreeFlags=14680064&s.subtreeFlags,null!==r?o=Pc(r,o):(o=Ac(o,l,n,null)).flags|=2,o.return=t,i.return=t,i.sibling=o,t.child=i,i=o,o=t.child,l=null===(l=e.child.memoizedState)?zo(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},o.memoizedState=l,o.childLanes=e.childLanes&~n,t.memoizedState=Ao,i}return e=(o=e.child).sibling,i=Pc(o,{mode:"visible",children:i.children}),0===(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function Mo(e,t){return(t=zc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Io(e,t,n,r){return null!==r&&pa(r),xa(t,e.child,null,n),(e=Mo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Fo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ca(e.return,t,n)}function Vo(e,t,n,r,i){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=i)}function Ho(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(xo(e,t,r.children,n),0!==(2&(r=Ya.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Fo(e,n,t);else if(19===e.tag)Fo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ei(Ya,r),0===(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===es(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Vo(t,!1,i,n,a);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===es(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Vo(t,!0,n,null,a);break;case"together":Vo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Uo(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=Pc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Pc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Wo(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ko(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function $o(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ko(t),null;case 1:case 17:return Ri(t.type)&&Pi(),Ko(t),null;case 3:return r=t.stateNode,Ga(),ji(Ti),ji(Ci),ns(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(da(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==aa&&(sc(aa),aa=null))),Ro(e,t),Ko(t),null;case 5:Za(t);var i=Qa($a.current);if(n=t.type,null!==e&&null!=t.stateNode)Po(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Ko(t),null}if(e=Qa(Wa.current),da(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[fi]=t,r[hi]=s,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(i=0;i<Ar.length;i++)Ir(Ar[i],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":G(r,s),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Ir("invalid",r);break;case"textarea":ie(r,s),Ir("invalid",r)}for(var l in ye(n,s),i=null,s)if(s.hasOwnProperty(l)){var c=s[l];"children"===l?"string"===typeof c?r.textContent!==c&&(!0!==s.suppressHydrationWarning&&Zr(r.textContent,c,e),i=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==s.suppressHydrationWarning&&Zr(r.textContent,c,e),i=["children",""+c]):o.hasOwnProperty(l)&&null!=c&&"onScroll"===l&&Ir("scroll",r)}switch(n){case"input":K(r),Y(r,s,!0);break;case"textarea":K(r),se(r);break;case"select":case"option":break;default:"function"===typeof s.onClick&&(r.onclick=Yr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=oe(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fi]=t,e[hi]=r,Do(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),i=r;break;case"iframe":case"object":case"embed":Ir("load",e),i=r;break;case"video":case"audio":for(i=0;i<Ar.length;i++)Ir(Ar[i],e);i=r;break;case"source":Ir("error",e),i=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),i=r;break;case"details":Ir("toggle",e),i=r;break;case"input":G(e,r),i=J(e,r),Ir("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=L({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),Ir("invalid",e)}for(s in ye(n,i),c=i)if(c.hasOwnProperty(s)){var u=c[s];"style"===s?ge(e,u):"dangerouslySetInnerHTML"===s?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===s?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(o.hasOwnProperty(s)?null!=u&&"onScroll"===s&&Ir("scroll",e):null!=u&&b(e,s,u,l))}switch(n){case"input":K(e),Y(e,r,!1);break;case"textarea":K(e),se(e);break;case"option":null!=r.value&&e.setAttribute("value",""+q(r.value));break;case"select":e.multiple=!!r.multiple,null!=(s=r.value)?ne(e,!!r.multiple,s,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Yr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ko(t),null;case 6:if(e&&null!=t.stateNode)Oo(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Qa($a.current),Qa(Wa.current),da(t)){if(r=t.stateNode,n=t.memoizedProps,r[fi]=t,(s=r.nodeValue!==n)&&null!==(e=na))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}s&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fi]=t,t.stateNode=r}return Ko(t),null;case 13:if(ji(Ya),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ia&&null!==ra&&0!==(1&t.mode)&&0===(128&t.flags))fa(),ha(),t.flags|=98560,s=!1;else if(s=da(t),null!==r&&null!==r.dehydrated){if(null===e){if(!s)throw Error(a(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(a(317));s[fi]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ko(t),s=!1}else null!==aa&&(sc(aa),aa=null),s=!0;if(!s)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Ya.current)?0===Ol&&(Ol=3):mc())),null!==t.updateQueue&&(t.flags|=4),Ko(t),null);case 4:return Ga(),Ro(e,t),null===e&&Hr(t.stateNode.containerInfo),Ko(t),null;case 10:return _a(t.type._context),Ko(t),null;case 19:if(ji(Ya),null===(s=t.memoizedState))return Ko(t),null;if(r=0!==(128&t.flags),null===(l=s.rendering))if(r)Wo(s,!1);else{if(0!==Ol||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=es(e))){for(t.flags|=128,Wo(s,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(s=n).flags&=14680066,null===(l=s.alternate)?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=l.childLanes,s.lanes=l.lanes,s.child=l.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=l.memoizedProps,s.memoizedState=l.memoizedState,s.updateQueue=l.updateQueue,s.type=l.type,e=l.dependencies,s.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ei(Ya,1&Ya.current|2),t.child}e=e.sibling}null!==s.tail&&Xe()>Hl&&(t.flags|=128,r=!0,Wo(s,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=es(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Wo(s,!0),null===s.tail&&"hidden"===s.tailMode&&!l.alternate&&!ia)return Ko(t),null}else 2*Xe()-s.renderingStartTime>Hl&&1073741824!==n&&(t.flags|=128,r=!0,Wo(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=s.last)?n.sibling=l:t.child=l,s.last=l)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Xe(),t.sibling=null,n=Ya.current,Ei(Ya,r?1&n|2:1&n),t):(Ko(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rl)&&(Ko(t),6&t.subtreeFlags&&(t.flags|=8192)):Ko(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Qo(e,t){switch(ta(t),t.tag){case 1:return Ri(t.type)&&Pi(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ga(),ji(Ti),ji(Ci),ns(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Za(t),null;case 13:if(ji(Ya),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ji(Ya),null;case 4:return Ga(),null;case 10:return _a(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}Do=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ro=function(){},Po=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Qa(Wa.current);var a,s=null;switch(n){case"input":i=J(e,i),r=J(e,r),s=[];break;case"select":i=L({},i,{value:void 0}),r=L({},r,{value:void 0}),s=[];break;case"textarea":i=re(e,i),r=re(e,r),s=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Yr)}for(u in ye(n,r),n=null,i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&null!=i[u])if("style"===u){var l=i[u];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(o.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(l=null!=i?i[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(null!=c||null!=l))if("style"===u)if(l){for(a in l)!l.hasOwnProperty(a)||c&&c.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in c)c.hasOwnProperty(a)&&l[a]!==c[a]&&(n||(n={}),n[a]=c[a])}else n||(s||(s=[]),s.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,l=l?l.__html:void 0,null!=c&&l!==c&&(s=s||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(s=s||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(o.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Ir("scroll",e),s||l===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}},Oo=function(e,t,n,r){n!==r&&(t.flags|=4)};var Jo=!1,Go=!1,Xo="function"===typeof WeakSet?WeakSet:Set,Zo=null;function Yo(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Nc(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Nc(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var a=i.destroy;i.destroy=void 0,void 0!==a&&el(t,n,a)}i=i.next}while(i!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function il(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fi],delete t[hi],delete t[mi],delete t[gi],delete t[vi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sl(e){return 5===e.tag||3===e.tag||4===e.tag}function ol(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||sl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Yr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function cl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cl(e,t,n),e=e.sibling;null!==e;)cl(e,t,n),e=e.sibling}var ul=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(it,n)}catch(o){}switch(n.tag){case 5:Go||Yo(n,t);case 6:var r=ul,i=dl;ul=null,fl(e,t,n),dl=i,null!==(ul=r)&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ul.removeChild(n.stateNode));break;case 18:null!==ul&&(dl?(e=ul,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),Ht(e)):li(ul,n.stateNode));break;case 4:r=ul,i=dl,ul=n.stateNode.containerInfo,dl=!0,fl(e,t,n),ul=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Go&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var a=i,s=a.destroy;a=a.tag,void 0!==s&&(0!==(2&a)||0!==(4&a))&&el(n,t,s),i=i.next}while(i!==r)}fl(e,t,n);break;case 1:if(!Go&&(Yo(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){Nc(n,t,o)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Go=(r=Go)||null!==n.memoizedState,fl(e,t,n),Go=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xo),t.forEach(function(t){var r=Cc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;null!==l;){switch(l.tag){case 5:ul=l.stateNode,dl=!1;break e;case 3:case 4:ul=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===ul)throw Error(a(160));hl(s,o,i),ul=null,dl=!1;var c=i.alternate;null!==c&&(c.return=null),i.return=null}catch(u){Nc(i,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),vl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){Nc(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Nc(e,e.return,g)}}break;case 1:ml(t,e),vl(e),512&r&&null!==n&&Yo(n,n.return);break;case 5:if(ml(t,e),vl(e),512&r&&null!==n&&Yo(n,n.return),32&e.flags){var i=e.stateNode;try{fe(i,"")}catch(g){Nc(e,e.return,g)}}if(4&r&&null!=(i=e.stateNode)){var s=e.memoizedProps,o=null!==n?n.memoizedProps:s,l=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===l&&"radio"===s.type&&null!=s.name&&X(i,s),be(l,o);var u=be(l,s);for(o=0;o<c.length;o+=2){var d=c[o],f=c[o+1];"style"===d?ge(i,f):"dangerouslySetInnerHTML"===d?de(i,f):"children"===d?fe(i,f):b(i,d,f,u)}switch(l){case"input":Z(i,s);break;case"textarea":ae(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var p=s.value;null!=p?ne(i,!!s.multiple,p,!1):h!==!!s.multiple&&(null!=s.defaultValue?ne(i,!!s.multiple,s.defaultValue,!0):ne(i,!!s.multiple,s.multiple?[]:"",!1))}i[hi]=s}catch(g){Nc(e,e.return,g)}}break;case 6:if(ml(t,e),vl(e),4&r){if(null===e.stateNode)throw Error(a(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(g){Nc(e,e.return,g)}}break;case 3:if(ml(t,e),vl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){Nc(e,e.return,g)}break;case 4:default:ml(t,e),vl(e);break;case 13:ml(t,e),vl(e),8192&(i=e.child).flags&&(s=null!==i.memoizedState,i.stateNode.isHidden=s,!s||null!==i.alternate&&null!==i.alternate.memoizedState||(Vl=Xe())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Go=(u=Go)||d,ml(t,e),Go=u):ml(t,e),vl(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Zo=e,d=e.child;null!==d;){for(f=Zo=d;null!==Zo;){switch(p=(h=Zo).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Yo(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Nc(r,n,g)}}break;case 5:Yo(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Zo=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{i=f.stateNode,u?"function"===typeof(s=i.style).setProperty?s.setProperty("display","none","important"):s.display="none":(l=f.stateNode,o=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,l.style.display=me("display",o))}catch(g){Nc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){Nc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),vl(e),4&r&&pl(e);case 21:}}function vl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(sl(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(fe(i,""),r.flags&=-33),cl(e,ol(e),i);break;case 3:case 4:var s=r.stateNode.containerInfo;ll(e,ol(e),s);break;default:throw Error(a(161))}}catch(o){Nc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function yl(e,t,n){Zo=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Zo;){var i=Zo,a=i.child;if(22===i.tag&&r){var s=null!==i.memoizedState||Jo;if(!s){var o=i.alternate,l=null!==o&&null!==o.memoizedState||Go;o=Jo;var c=Go;if(Jo=s,(Go=l)&&!c)for(Zo=i;null!==Zo;)l=(s=Zo).child,22===s.tag&&null!==s.memoizedState?Sl(i):null!==l?(l.return=s,Zo=l):Sl(i);for(;null!==a;)Zo=a,bl(a,t,n),a=a.sibling;Zo=i,Jo=o,Go=c}xl(e)}else 0!==(8772&i.subtreeFlags)&&null!==a?(a.return=i,Zo=a):xl(e)}}function xl(e){for(;null!==Zo;){var t=Zo;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Go||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Go)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:to(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;null!==s&&Ua(t,s,r);break;case 3:var o=t.updateQueue;if(null!==o){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ua(t,o,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ht(f)}}}break;default:throw Error(a(163))}Go||512&t.flags&&il(t)}catch(h){Nc(t,t.return,h)}}if(t===e){Zo=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zo=n;break}Zo=t.return}}function wl(e){for(;null!==Zo;){var t=Zo;if(t===e){Zo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zo=n;break}Zo=t.return}}function Sl(e){for(;null!==Zo;){var t=Zo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Nc(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Nc(t,i,l)}}var a=t.return;try{il(t)}catch(l){Nc(t,a,l)}break;case 5:var s=t.return;try{il(t)}catch(l){Nc(t,s,l)}}}catch(l){Nc(t,t.return,l)}if(t===e){Zo=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Zo=o;break}Zo=t.return}}var kl,Nl=Math.ceil,jl=x.ReactCurrentDispatcher,El=x.ReactCurrentOwner,_l=x.ReactCurrentBatchConfig,Cl=0,Tl=null,Bl=null,Dl=0,Rl=0,Pl=Ni(0),Ol=0,Al=null,zl=0,Ll=0,Ml=0,Il=null,Fl=null,Vl=0,Hl=1/0,Ul=null,ql=!1,Wl=null,Kl=null,$l=!1,Ql=null,Jl=0,Gl=0,Xl=null,Zl=-1,Yl=0;function ec(){return 0!==(6&Cl)?Xe():-1!==Zl?Zl:Zl=Xe()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Cl)&&0!==Dl?Dl&-Dl:null!==ma.transition?(0===Yl&&(Yl=mt()),Yl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function nc(e,t,n,r){if(50<Gl)throw Gl=0,Xl=null,Error(a(185));vt(e,n,r),0!==(2&Cl)&&e===Tl||(e===Tl&&(0===(2&Cl)&&(Ll|=n),4===Ol&&oc(e,Dl)),rc(e,r),1===n&&0===Cl&&0===(1&t.mode)&&(Hl=Xe()+500,Ii&&Hi()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,a=e.pendingLanes;0<a;){var s=31-st(a),o=1<<s,l=i[s];-1===l?0!==(o&n)&&0===(o&r)||(i[s]=ht(o,t)):l<=t&&(e.expiredLanes|=o),a&=~o}}(e,t);var r=ft(e,e===Tl?Dl:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Ii=!0,Vi(e)}(lc.bind(null,e)):Vi(lc.bind(null,e)),si(function(){0===(6&Cl)&&Hi()}),n=null;else{switch(xt(r)){case 1:n=Ye;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tc(n,ic.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ic(e,t){if(Zl=-1,Yl=0,0!==(6&Cl))throw Error(a(327));var n=e.callbackNode;if(Sc()&&e.callbackNode!==n)return null;var r=ft(e,e===Tl?Dl:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var i=Cl;Cl|=2;var s=pc();for(Tl===e&&Dl===t||(Ul=null,Hl=Xe()+500,fc(e,t));;)try{yc();break}catch(l){hc(e,l)}Ea(),jl.current=s,Cl=i,null!==Bl?t=0:(Tl=null,Dl=0,t=Ol)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=ac(e,i))),1===t)throw n=Al,fc(e,0),oc(e,r),rc(e,Xe()),n;if(6===t)oc(e,r);else{if(i=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],a=i.getSnapshot;i=i.value;try{if(!or(a(),i))return!1}catch(o){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=gc(e,r))&&(0!==(s=pt(e))&&(r=s,t=ac(e,s))),1===t))throw n=Al,fc(e,0),oc(e,r),rc(e,Xe()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:wc(e,Fl,Ul);break;case 3:if(oc(e,r),(130023424&r)===r&&10<(t=Vl+500-Xe())){if(0!==ft(e,0))break;if(((i=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(wc.bind(null,e,Fl,Ul),t);break}wc(e,Fl,Ul);break;case 4:if(oc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-st(r);s=1<<o,(o=t[o])>i&&(i=o),r&=~s}if(r=i,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Nl(r/1960))-r)){e.timeoutHandle=ri(wc.bind(null,e,Fl,Ul),r);break}wc(e,Fl,Ul);break;default:throw Error(a(329))}}}return rc(e,Xe()),e.callbackNode===n?ic.bind(null,e):null}function ac(e,t){var n=Il;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Fl,Fl=n,null!==t&&sc(t)),e}function sc(e){null===Fl?Fl=e:Fl.push.apply(Fl,e)}function oc(e,t){for(t&=~Ml,t&=~Ll,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function lc(e){if(0!==(6&Cl))throw Error(a(327));Sc();var t=ft(e,0);if(0===(1&t))return rc(e,Xe()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=ac(e,r))}if(1===n)throw n=Al,fc(e,0),oc(e,t),rc(e,Xe()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Fl,Ul),rc(e,Xe()),null}function cc(e,t){var n=Cl;Cl|=1;try{return e(t)}finally{0===(Cl=n)&&(Hl=Xe()+500,Ii&&Hi())}}function uc(e){null!==Ql&&0===Ql.tag&&0===(6&Cl)&&Sc();var t=Cl;Cl|=1;var n=_l.transition,r=bt;try{if(_l.transition=null,bt=1,e)return e()}finally{bt=r,_l.transition=n,0===(6&(Cl=t))&&Hi()}}function dc(){Rl=Pl.current,ji(Pl)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Bl)for(n=Bl.return;null!==n;){var r=n;switch(ta(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Pi();break;case 3:Ga(),ji(Ti),ji(Ci),ns();break;case 5:Za(r);break;case 4:Ga();break;case 13:case 19:ji(Ya);break;case 10:_a(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Tl=e,Bl=e=Pc(e.current,null),Dl=Rl=t,Ol=0,Al=null,Ml=Ll=zl=0,Fl=Il=null,null!==Da){for(t=0;t<Da.length;t++)if(null!==(r=(n=Da[t]).interleaved)){n.interleaved=null;var i=r.next,a=n.pending;if(null!==a){var s=a.next;a.next=i,r.next=s}n.pending=r}Da=null}return e}function hc(e,t){for(;;){var n=Bl;try{if(Ea(),rs.current=Xs,cs){for(var r=ss.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}cs=!1}if(as=0,ls=os=ss=null,us=!1,ds=0,El.current=null,null===n||null===n.return){Ol=1,Al=t,Bl=null;break}e:{var s=e,o=n.return,l=n,c=t;if(t=Dl,l.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=go(o);if(null!==p){p.flags&=-257,vo(p,o,l,0,t),1&p.mode&&mo(s,u,t),c=u;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){mo(s,u,t),mc();break e}c=Error(a(426))}else if(ia&&1&l.mode){var v=go(o);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vo(v,o,l,0,t),pa(lo(c,l));break e}}s=c=lo(c,l),4!==Ol&&(Ol=2),null===Il?Il=[s]:Il.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t,Va(s,ho(0,c,t));break e;case 1:l=c;var y=s.type,b=s.stateNode;if(0===(128&s.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Kl||!Kl.has(b)))){s.flags|=65536,t&=-t,s.lanes|=t,Va(s,po(s,l,t));break e}}s=s.return}while(null!==s)}xc(n)}catch(x){t=x,Bl===n&&null!==n&&(Bl=n=n.return);continue}break}}function pc(){var e=jl.current;return jl.current=Xs,null===e?Xs:e}function mc(){0!==Ol&&3!==Ol&&2!==Ol||(Ol=4),null===Tl||0===(268435455&zl)&&0===(268435455&Ll)||oc(Tl,Dl)}function gc(e,t){var n=Cl;Cl|=2;var r=pc();for(Tl===e&&Dl===t||(Ul=null,fc(e,t));;)try{vc();break}catch(i){hc(e,i)}if(Ea(),Cl=n,jl.current=r,null!==Bl)throw Error(a(261));return Tl=null,Dl=0,Ol}function vc(){for(;null!==Bl;)bc(Bl)}function yc(){for(;null!==Bl&&!Je();)bc(Bl)}function bc(e){var t=kl(e.alternate,e,Rl);e.memoizedProps=e.pendingProps,null===t?xc(e):Bl=t,El.current=null}function xc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=$o(n,t,Rl)))return void(Bl=n)}else{if(null!==(n=Qo(n,t)))return n.flags&=32767,void(Bl=n);if(null===e)return Ol=6,void(Bl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Bl=t);Bl=t=e}while(null!==t);0===Ol&&(Ol=5)}function wc(e,t,n){var r=bt,i=_l.transition;try{_l.transition=null,bt=1,function(e,t,n,r){do{Sc()}while(null!==Ql);if(0!==(6&Cl))throw Error(a(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-st(n),a=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~a}}(e,s),e===Tl&&(Bl=Tl=null,Dl=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||$l||($l=!0,Tc(tt,function(){return Sc(),null})),s=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||s){s=_l.transition,_l.transition=null;var o=bt;bt=1;var l=Cl;Cl|=4,El.current=null,function(e,t){if(ei=qt,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(w){n=null;break e}var o=0,l=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=o+i),f!==s||0!==r&&3!==f.nodeType||(c=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++u===i&&(l=o),h===s&&++d===r&&(c=o),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},qt=!1,Zo=t;null!==Zo;)if(e=(t=Zo).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zo=e;else for(;null!==Zo;){t=Zo;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:to(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(a(163))}}catch(w){Nc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zo=e;break}Zo=t.return}m=tl,tl=!1}(e,n),gl(n,e),pr(ti),qt=!!ei,ti=ei=null,e.current=n,yl(n,e,i),Ge(),Cl=l,bt=o,_l.transition=s}else e.current=n;if($l&&($l=!1,Ql=e,Jl=i),s=e.pendingLanes,0===s&&(Kl=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(it,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ql)throw ql=!1,e=Wl,Wl=null,e;0!==(1&Jl)&&0!==e.tag&&Sc(),s=e.pendingLanes,0!==(1&s)?e===Xl?Gl++:(Gl=0,Xl=e):Gl=0,Hi()}(e,t,n,r)}finally{_l.transition=i,bt=r}return null}function Sc(){if(null!==Ql){var e=xt(Jl),t=_l.transition,n=bt;try{if(_l.transition=null,bt=16>e?16:e,null===Ql)var r=!1;else{if(e=Ql,Ql=null,Jl=0,0!==(6&Cl))throw Error(a(331));var i=Cl;for(Cl|=4,Zo=e.current;null!==Zo;){var s=Zo,o=s.child;if(0!==(16&Zo.flags)){var l=s.deletions;if(null!==l){for(var c=0;c<l.length;c++){var u=l[c];for(Zo=u;null!==Zo;){var d=Zo;switch(d.tag){case 0:case 11:case 15:nl(8,d,s)}var f=d.child;if(null!==f)f.return=d,Zo=f;else for(;null!==Zo;){var h=(d=Zo).sibling,p=d.return;if(al(d),d===u){Zo=null;break}if(null!==h){h.return=p,Zo=h;break}Zo=p}}}var m=s.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Zo=s}}if(0!==(2064&s.subtreeFlags)&&null!==o)o.return=s,Zo=o;else e:for(;null!==Zo;){if(0!==(2048&(s=Zo).flags))switch(s.tag){case 0:case 11:case 15:nl(9,s,s.return)}var y=s.sibling;if(null!==y){y.return=s.return,Zo=y;break e}Zo=s.return}}var b=e.current;for(Zo=b;null!==Zo;){var x=(o=Zo).child;if(0!==(2064&o.subtreeFlags)&&null!==x)x.return=o,Zo=x;else e:for(o=b;null!==Zo;){if(0!==(2048&(l=Zo).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(S){Nc(l,l.return,S)}if(l===o){Zo=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zo=w;break e}Zo=l.return}}if(Cl=i,Hi(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{bt=n,_l.transition=t}}return!1}function kc(e,t,n){e=Ia(e,t=ho(0,t=lo(n,t),1),1),t=ec(),null!==e&&(vt(e,1,t),rc(e,t))}function Nc(e,t,n){if(3===e.tag)kc(e,e,n);else for(;null!==t;){if(3===t.tag){kc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Kl||!Kl.has(r))){t=Ia(t,e=po(t,e=lo(n,e),1),1),e=ec(),null!==t&&(vt(t,1,e),rc(t,e));break}}t=t.return}}function jc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Tl===e&&(Dl&n)===n&&(4===Ol||3===Ol&&(130023424&Dl)===Dl&&500>Xe()-Vl?fc(e,0):Ml|=n),rc(e,t)}function Ec(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Oa(e,t))&&(vt(e,t,n),rc(e,n))}function _c(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ec(e,n)}function Cc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Ec(e,n)}function Tc(e,t){return $e(e,t)}function Bc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dc(e,t,n,r){return new Bc(e,t,n,r)}function Rc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Pc(e,t){var n=e.alternate;return null===n?((n=Dc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Oc(e,t,n,r,i,s){var o=2;if(r=e,"function"===typeof e)Rc(e)&&(o=1);else if("string"===typeof e)o=5;else e:switch(e){case k:return Ac(n.children,i,s,t);case N:o=8,i|=8;break;case j:return(e=Dc(12,n,t,2|i)).elementType=j,e.lanes=s,e;case T:return(e=Dc(13,n,t,i)).elementType=T,e.lanes=s,e;case B:return(e=Dc(19,n,t,i)).elementType=B,e.lanes=s,e;case P:return zc(n,i,s,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:o=10;break e;case _:o=9;break e;case C:o=11;break e;case D:o=14;break e;case R:o=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Dc(o,n,t,i)).elementType=e,t.type=r,t.lanes=s,t}function Ac(e,t,n,r){return(e=Dc(7,e,r,t)).lanes=n,e}function zc(e,t,n,r){return(e=Dc(22,e,r,t)).elementType=P,e.lanes=n,e.stateNode={isHidden:!1},e}function Lc(e,t,n){return(e=Dc(6,e,null,t)).lanes=n,e}function Mc(e,t,n){return(t=Dc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ic(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Fc(e,t,n,r,i,a,s,o,l){return e=new Ic(e,t,n,o,l),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Dc(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},za(a),e}function Vc(e){if(!e)return _i;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ri(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(Ri(n))return Ai(e,n,t)}return t}function Hc(e,t,n,r,i,a,s,o,l){return(e=Fc(n,r,!0,e,0,a,0,o,l)).context=Vc(null),n=e.current,(a=Ma(r=ec(),i=tc(n))).callback=void 0!==t&&null!==t?t:null,Ia(n,a,i),e.current.lanes=i,vt(e,i,r),rc(e,r),e}function Uc(e,t,n,r){var i=t.current,a=ec(),s=tc(i);return n=Vc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ma(a,s)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ia(i,t,s))&&(nc(e,i,s,a),Fa(e,i,s)),s}function qc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Wc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Kc(e,t){Wc(e,t),(e=e.alternate)&&Wc(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ti.current)bo=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bo=!1,function(e,t,n){switch(t.tag){case 3:To(t),ha();break;case 5:Xa(t);break;case 1:Ri(t.type)&&zi(t);break;case 4:Ja(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Ei(Sa,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ei(Ya,1&Ya.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Lo(e,t,n):(Ei(Ya,1&Ya.current),null!==(e=qo(e,t,n))?e.sibling:null);Ei(Ya,1&Ya.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Ho(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ei(Ya,Ya.current),r)break;return null;case 22:case 23:return t.lanes=0,No(e,t,n)}return qo(e,t,n)}(e,t,n);bo=0!==(131072&e.flags)}else bo=!1,ia&&0!==(1048576&t.flags)&&Yi(t,Ki,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Uo(e,t),e=t.pendingProps;var i=Di(t,Ci.current);Ta(t,n),i=ms(null,t,r,e,i,n);var s=gs();return t.flags|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ri(r)?(s=!0,zi(t)):s=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,za(t),i.updater=ro,t.stateNode=i,i._reactInternals=t,oo(t,r,e,n),t=Co(null,t,r,!0,s,n)):(t.tag=0,ia&&s&&ea(t),xo(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Uo(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"===typeof e)return Rc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===C)return 11;if(e===D)return 14}return 2}(r),e=to(r,e),i){case 0:t=Eo(null,t,r,e,n);break e;case 1:t=_o(null,t,r,e,n);break e;case 11:t=wo(null,t,r,e,n);break e;case 14:t=So(null,t,r,to(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Eo(e,t,r,i=t.elementType===r?i:to(r,i),n);case 1:return r=t.type,i=t.pendingProps,_o(e,t,r,i=t.elementType===r?i:to(r,i),n);case 3:e:{if(To(t),null===e)throw Error(a(387));r=t.pendingProps,i=(s=t.memoizedState).element,La(e,t),Ha(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Bo(e,t,r,n,i=lo(Error(a(423)),t));break e}if(r!==i){t=Bo(e,t,r,n,i=lo(Error(a(424)),t));break e}for(ra=ci(t.stateNode.containerInfo.firstChild),na=t,ia=!0,aa=null,n=wa(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===i){t=qo(e,t,n);break e}xo(e,t,r,n)}t=t.child}return t;case 5:return Xa(t),null===e&&ca(t),r=t.type,i=t.pendingProps,s=null!==e?e.memoizedProps:null,o=i.children,ni(r,i)?o=null:null!==s&&ni(r,s)&&(t.flags|=32),jo(e,t),xo(e,t,o,n),t.child;case 6:return null===e&&ca(t),null;case 13:return Lo(e,t,n);case 4:return Ja(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xa(t,null,r,n):xo(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,wo(e,t,r,i=t.elementType===r?i:to(r,i),n);case 7:return xo(e,t,t.pendingProps,n),t.child;case 8:case 12:return xo(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,Ei(Sa,r._currentValue),r._currentValue=o,null!==s)if(or(s.value,o)){if(s.children===i.children&&!Ti.current){t=qo(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var l=s.dependencies;if(null!==l){o=s.child;for(var c=l.firstContext;null!==c;){if(c.context===r){if(1===s.tag){(c=Ma(-1,n&-n)).tag=2;var u=s.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}s.lanes|=n,null!==(c=s.alternate)&&(c.lanes|=n),Ca(s.return,n,t),l.lanes|=n;break}c=c.next}}else if(10===s.tag)o=s.type===t.type?null:s.child;else if(18===s.tag){if(null===(o=s.return))throw Error(a(341));o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),Ca(o,n,t),o=s.sibling}else o=s.child;if(null!==o)o.return=s;else for(o=s;null!==o;){if(o===t){o=null;break}if(null!==(s=o.sibling)){s.return=o.return,o=s;break}o=o.return}s=o}xo(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Ta(t,n),r=r(i=Ba(i)),t.flags|=1,xo(e,t,r,n),t.child;case 14:return i=to(r=t.type,t.pendingProps),So(e,t,r,i=to(r.type,i),n);case 15:return ko(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:to(r,i),Uo(e,t),t.tag=1,Ri(r)?(e=!0,zi(t)):e=!1,Ta(t,n),ao(t,r,i),oo(t,r,i,n),Co(null,t,r,!0,e,n);case 19:return Ho(e,t,n);case 22:return No(e,t,n)}throw Error(a(156,t.tag))};var $c="function"===typeof reportError?reportError:function(e){console.error(e)};function Qc(e){this._internalRoot=e}function Jc(e){this._internalRoot=e}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zc(){}function Yc(e,t,n,r,i){var a=n._reactRootContainer;if(a){var s=a;if("function"===typeof i){var o=i;i=function(){var e=qc(s);o.call(e)}}Uc(t,s,e,i)}else s=function(e,t,n,r,i){if(i){if("function"===typeof r){var a=r;r=function(){var e=qc(s);a.call(e)}}var s=Hc(t,r,e,0,null,!1,0,"",Zc);return e._reactRootContainer=s,e[pi]=s.current,Hr(8===e.nodeType?e.parentNode:e),uc(),s}for(;i=e.lastChild;)e.removeChild(i);if("function"===typeof r){var o=r;r=function(){var e=qc(l);o.call(e)}}var l=Fc(e,0,!1,null,0,!1,0,"",Zc);return e._reactRootContainer=l,e[pi]=l.current,Hr(8===e.nodeType?e.parentNode:e),uc(function(){Uc(t,l,n,r)}),l}(n,t,e,i,r);return qc(s)}Jc.prototype.render=Qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Uc(e,t,null,null)},Jc.prototype.unmount=Qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc(function(){Uc(null,e,null,null)}),t[pi]=null}},Jc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pt.length&&0!==t&&t<Pt[n].priority;n++);Pt.splice(n,0,e),0===n&&Lt(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),rc(t,Xe()),0===(6&Cl)&&(Hl=Xe()+500,Hi()))}break;case 13:uc(function(){var t=Oa(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}}),Kc(e,1)}},St=function(e){if(13===e.tag){var t=Oa(e,134217728);if(null!==t)nc(t,e,134217728,ec());Kc(e,134217728)}},kt=function(e){if(13===e.tag){var t=tc(e),n=Oa(e,t);if(null!==n)nc(n,e,t,ec());Kc(e,t)}},Nt=function(){return bt},jt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=wi(r);if(!i)throw Error(a(90));$(r),Z(r,i)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=cc,Te=uc;var eu={usingClientEntryPoint:!1,Events:[bi,xi,wi,Ee,_e,cc]},tu={findFiberByHostInstance:yi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=We(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{it=ru.inject(nu),at=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gc(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gc(e))throw Error(a(299));var n=!1,r="",i=$c;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=Fc(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,Hr(8===e.nodeType?e.parentNode:e),new Qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=We(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Xc(t))throw Error(a(200));return Yc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gc(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,i=!1,s="",o=$c;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onRecoverableError&&(o=n.onRecoverableError)),t=Hc(t,null,e,1,null!=n?n:null,i,0,s,o),e[pi]=t.current,Hr(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Jc(t)},t.render=function(e,t,n){if(!Xc(t))throw Error(a(200));return Yc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xc(e))throw Error(a(40));return!!e._reactRootContainer&&(uc(function(){Yc(null,null,e,!1,function(){e._reactRootContainer=null,e[pi]=null})}),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xc(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return Yc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},749:function(e,t,n){!function(){var t;e.exports=(t=n(488),function(e){var n=t,r=n.lib,i=r.Base,a=r.WordArray,s=n.x64={};s.Word=i.extend({init:function(e,t){this.high=e,this.low=t}}),s.WordArray=i.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:8*t.length},toX32:function(){for(var e=this.words,t=e.length,n=[],r=0;r<t;r++){var i=e[r];n.push(i.high),n.push(i.low)}return a.create(n,this.sigBytes)},clone:function(){for(var e=i.clone.call(this),t=e.words=this.words.slice(0),n=t.length,r=0;r<n;r++)t[r]=t[r].clone();return e}})}(),t)}()},780:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib.BlockCipher,r=e.algo,i=[],a=[],s=[],o=[],l=[],c=[],u=[],d=[],f=[],h=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var p=r^r<<1^r<<2^r<<3^r<<4;p=p>>>8^255&p^99,i[n]=p,a[p]=n;var m=e[n],g=e[m],v=e[g],y=257*e[p]^16843008*p;s[n]=y<<24|y>>>8,o[n]=y<<16|y>>>16,l[n]=y<<8|y>>>24,c[n]=y,y=16843009*v^65537*g^257*m^16843008*n,u[p]=y<<24|y>>>8,d[p]=y<<16|y>>>16,f[p]=y<<8|y>>>24,h[p]=y,n?(n=m^e[e[e[v^m]]],r^=e[e[r]]):n=r=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],m=r.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*((this._nRounds=n+6)+1),a=this._keySchedule=[],s=0;s<r;s++)s<n?a[s]=t[s]:(c=a[s-1],s%n?n>6&&s%n==4&&(c=i[c>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c]):(c=i[(c=c<<8|c>>>24)>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c],c^=p[s/n|0]<<24),a[s]=a[s-n]^c);for(var o=this._invKeySchedule=[],l=0;l<r;l++){if(s=r-l,l%4)var c=a[s];else c=a[s-4];o[l]=l<4||s<=4?c:u[i[c>>>24]]^d[i[c>>>16&255]]^f[i[c>>>8&255]]^h[i[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,o,l,c,i)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,u,d,f,h,a),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,i,a,s,o){for(var l=this._nRounds,c=e[t]^n[0],u=e[t+1]^n[1],d=e[t+2]^n[2],f=e[t+3]^n[3],h=4,p=1;p<l;p++){var m=r[c>>>24]^i[u>>>16&255]^a[d>>>8&255]^s[255&f]^n[h++],g=r[u>>>24]^i[d>>>16&255]^a[f>>>8&255]^s[255&c]^n[h++],v=r[d>>>24]^i[f>>>16&255]^a[c>>>8&255]^s[255&u]^n[h++],y=r[f>>>24]^i[c>>>16&255]^a[u>>>8&255]^s[255&d]^n[h++];c=m,u=g,d=v,f=y}m=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&f])^n[h++],g=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[f>>>8&255]<<8|o[255&c])^n[h++],v=(o[d>>>24]<<24|o[f>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^n[h++],y=(o[f>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^n[h++],e[t]=m,e[t+1]=g,e[t+2]=v,e[t+3]=y},keySize:8});e.AES=n._createHelper(m)}(),t.AES)}()},804:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.pad.Iso97971={pad:function(e,n){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,n)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971)}()},853:(e,t,n)=>{"use strict";e.exports=n(234)},875:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(523),n(199),n(211),n(238),function(){var e=t,n=e.lib.StreamCipher,r=e.algo,i=[],a=[],s=[],o=r.Rabbit=n.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,n=0;n<4;n++)e[n]=16711935&(e[n]<<8|e[n]>>>24)|4278255360&(e[n]<<24|e[n]>>>8);var r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,n=0;n<4;n++)l.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(t){var a=t.words,s=a[0],o=a[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),u=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),d=c>>>16|4294901760&u,f=u<<16|65535&c;for(i[0]^=c,i[1]^=d,i[2]^=u,i[3]^=f,i[4]^=c,i[5]^=d,i[6]^=u,i[7]^=f,n=0;n<4;n++)l.call(this)}},_doProcessBlock:function(e,t){var n=this._X;l.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var r=0;r<4;r++)i[r]=16711935&(i[r]<<8|i[r]>>>24)|4278255360&(i[r]<<24|i[r]>>>8),e[t+r]^=i[r]},blockSize:4,ivSize:2});function l(){for(var e=this._X,t=this._C,n=0;n<8;n++)a[n]=t[n];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<a[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<a[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<a[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<a[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<a[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<a[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<a[6]>>>0?1:0)|0,this._b=t[7]>>>0<a[7]>>>0?1:0,n=0;n<8;n++){var r=e[n]+t[n],i=65535&r,o=r>>>16,l=((i*i>>>17)+i*o>>>15)+o*o,c=((4294901760&r)*r|0)+((65535&r)*r|0);s[n]=l^c}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}e.Rabbit=n._createHelper(o)}(),t.Rabbit)}()},884:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.pad.AnsiX923={pad:function(e,t){var n=e.sigBytes,r=4*t,i=r-n%r,a=n+i-1;e.clamp(),e.words[a>>>2]|=i<<24-a%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},t.pad.Ansix923)}()},930:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(368),n(220),function(){var e=t,n=e.lib,r=n.Base,i=n.WordArray,a=e.algo,s=a.SHA256,o=a.HMAC,l=a.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=this.cfg,r=o.create(n.hasher,e),a=i.create(),s=i.create([1]),l=a.words,c=s.words,u=n.keySize,d=n.iterations;l.length<u;){var f=r.update(t).finalize(s);r.reset();for(var h=f.words,p=h.length,m=f,g=1;g<d;g++){m=r.finalize(m),r.reset();for(var v=m.words,y=0;y<p;y++)h[y]^=v[y]}a.concat(f),c[0]++}return a.sigBytes=4*u,a}});e.PBKDF2=function(e,t,n){return l.create(n).compute(e,t)}}(),t.PBKDF2)}()},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},962:function(e,t,n){!function(){var t;e.exports=(t=n(488),n(238),t.mode.CTR=function(){var e=t.lib.BlockCipherMode.extend(),n=e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=this._iv,a=this._counter;i&&(a=this._counter=i.slice(0),this._iv=void 0);var s=a.slice(0);n.encryptBlock(s,0),a[r-1]=a[r-1]+1|0;for(var o=0;o<r;o++)e[t+o]^=s[o]}});return e.Decryptor=n,e}(),t.mode.CTR)}()}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(43),t=n(391);const r=()=>null;var i=n(579);const a=e=>{let{categories:t,activeCategory:n,onCategoryChange:r}=e;return(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:t.map(e=>(0,i.jsx)("button",{onClick:()=>r(e.name),className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ".concat(n===e.name?"bg-blue-600 text-white shadow-md transform scale-105":"bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 shadow-sm border border-gray-200"),children:e.name},e.id))})})},s=e=>{let{children:t}=e;return(0,i.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:t})})};function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function c(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach(function(t){c(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const f=()=>{const[t,n]=(0,e.useState)("random-content"),[r,a]=(0,e.useState)({}),[s,o]=(0,e.useState)({wordCount:100,language:"\u4e2d\u6587",contentType:"Lorem ipsum"}),[l,c]=(0,e.useState)({count:10,domain:"gmail.com"}),[u,f]=(0,e.useState)({count:10,type:"\u624b\u673a\u53f7\u7801"}),[h,p]=(0,e.useState)({count:10,type:"\u5b8c\u6574\u5730\u5740"}),[m,g]=(0,e.useState)({count:10,gender:"\u968f\u673a",ageMin:18,ageMax:65,region:"\u968f\u673a\u5730\u533a"}),v=e=>{navigator.clipboard.writeText(e)};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u6570\u636e\u751f\u6210\u5de5\u5177"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6 border-b border-gray-200",children:[{id:"random-content",name:"\u968f\u673a\u5185\u5bb9\u751f\u6210\u5668"},{id:"email-generator",name:"\u968f\u673a\u90ae\u7bb1\u751f\u6210\u5668"},{id:"phone-generator",name:"\u968f\u673a\u7535\u8bdd\u53f7\u7801\u751f\u6210\u5668"},{id:"address-generator",name:"\u968f\u673a\u5730\u5740\u751f\u6210\u5668"},{id:"id-generator",name:"\u968f\u673a\u8eab\u4efd\u8bc1\u751f\u6210\u5668"}].map(e=>(0,i.jsx)("button",{onClick:()=>n(e.id),className:"tab-button ".concat(t===e.id?"active":"inactive"),children:e.name},e.id))}),"random-content"===t&&(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u751f\u6210\u5b57\u6570\uff1a"}),(0,i.jsx)("input",{type:"number",value:s.wordCount,onChange:e=>o(t=>d(d({},t),{},{wordCount:parseInt(e.target.value)||100})),className:"input-field",min:"1",max:"10000"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u8bed\u8a00\u9009\u62e9\uff1a"}),(0,i.jsxs)("select",{value:s.language,onChange:e=>o(t=>d(d({},t),{},{language:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"\u4e2d\u6587",children:"\u4e2d\u6587"}),(0,i.jsx)("option",{value:"\u82f1\u6587",children:"\u82f1\u6587"}),(0,i.jsx)("option",{value:"\u65e5\u6587",children:"\u65e5\u6587"}),(0,i.jsx)("option",{value:"\u97e9\u6587",children:"\u97e9\u6587"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5185\u5bb9\u7c7b\u578b\uff1a"}),(0,i.jsxs)("select",{value:s.contentType,onChange:e=>o(t=>d(d({},t),{},{contentType:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"Lorem ipsum",children:"Lorem ipsum"}),(0,i.jsx)("option",{value:"\u6587\u7ae0\u6bb5\u843d",children:"\u6587\u7ae0\u6bb5\u843d"}),(0,i.jsx)("option",{value:"\u6545\u4e8b\u5185\u5bb9",children:"\u6545\u4e8b\u5185\u5bb9"}),(0,i.jsx)("option",{value:"\u6280\u672f\u6587\u6863",children:"\u6280\u672f\u6587\u6863"}),(0,i.jsx)("option",{value:"\u5546\u52a1\u6587\u672c",children:"\u5546\u52a1\u6587\u672c"}),(0,i.jsx)("option",{value:"\u65e5\u5e38\u5bf9\u8bdd",children:"\u65e5\u5e38\u5bf9\u8bdd"})]})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:()=>{const e={"Lorem ipsum":"Lorem ipsum dolor sit amet, consectetur adipiscing elit...","\u6587\u7ae0\u6bb5\u843d":"\u8fd9\u662f\u4e00\u4e2a\u793a\u4f8b\u6587\u7ae0\u6bb5\u843d\uff0c\u5305\u542b\u4e86\u4e30\u5bcc\u7684\u5185\u5bb9\u548c\u4fe1\u606f...","\u6545\u4e8b\u5185\u5bb9":"\u4ece\u524d\u6709\u4e00\u4e2a\u5c0f\u6751\u5e84\uff0c\u6751\u5e84\u91cc\u4f4f\u7740\u4e00\u4f4d\u5584\u826f\u7684\u8001\u4eba...","\u6280\u672f\u6587\u6863":"\u672c\u6587\u6863\u4ecb\u7ecd\u4e86\u7cfb\u7edf\u7684\u67b6\u6784\u8bbe\u8ba1\u548c\u5b9e\u73b0\u65b9\u6848...","\u5546\u52a1\u6587\u672c":"\u5c0a\u656c\u7684\u5ba2\u6237\uff0c\u611f\u8c22\u60a8\u5bf9\u6211\u4eec\u4ea7\u54c1\u7684\u5173\u6ce8\u548c\u652f\u6301...","\u65e5\u5e38\u5bf9\u8bdd":"\u4f60\u597d\uff0c\u4eca\u5929\u5929\u6c14\u771f\u4e0d\u9519\uff0c\u9002\u5408\u51fa\u53bb\u8d70\u8d70..."},t=(e[s.contentType]||e["Lorem ipsum"]).split(" "),n=t.slice(0,Math.min(s.wordCount,t.length)).join(" ");a(e=>d(d({},e),{},{randomContent:n}))},className:"btn-primary",children:"\u751f\u6210\u5185\u5bb9"}),(0,i.jsx)("button",{onClick:()=>v(r.randomContent||""),className:"btn-secondary",disabled:!r.randomContent,children:"\u590d\u5236\u7ed3\u679c"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u751f\u6210\u7ed3\u679c\uff1a"}),(0,i.jsx)("textarea",{value:r.randomContent||"",readOnly:!0,className:"result-area h-64",placeholder:"\u751f\u6210\u7684\u5185\u5bb9\u5c06\u5728\u8fd9\u91cc\u663e\u793a..."})]})]}),"email-generator"===t&&(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u90ae\u7bb1\u6570\u91cf\uff1a"}),(0,i.jsx)("input",{type:"number",value:l.count,onChange:e=>c(t=>d(d({},t),{},{count:parseInt(e.target.value)||10})),className:"input-field",min:"1",max:"1000"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u57df\u540d\u9009\u62e9\uff1a"}),(0,i.jsxs)("select",{value:l.domain,onChange:e=>c(t=>d(d({},t),{},{domain:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"gmail.com",children:"gmail.com"}),(0,i.jsx)("option",{value:"163.com",children:"163.com"}),(0,i.jsx)("option",{value:"qq.com",children:"qq.com"}),(0,i.jsx)("option",{value:"hotmail.com",children:"hotmail.com"}),(0,i.jsx)("option",{value:"yahoo.com",children:"yahoo.com"})]})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:()=>{const e=[],t=l.domain;for(let n=0;n<l.count;n++){const n="user".concat(Math.floor(1e4*Math.random()));e.push("".concat(n,"@").concat(t))}a(t=>d(d({},t),{},{emails:e.join("\n")}))},className:"btn-primary",children:"\u751f\u6210\u90ae\u7bb1"}),(0,i.jsx)("button",{onClick:()=>v(r.emails||""),className:"btn-secondary",disabled:!r.emails,children:"\u590d\u5236\u7ed3\u679c"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u751f\u6210\u7ed3\u679c\uff1a"}),(0,i.jsx)("textarea",{value:r.emails||"",readOnly:!0,className:"result-area h-64",placeholder:"\u751f\u6210\u7684\u90ae\u7bb1\u5c06\u5728\u8fd9\u91cc\u663e\u793a..."})]})]})]})},h=()=>{const[t,n]=(0,e.useState)(""),[r,a]=(0,e.useState)({charactersWithSpaces:0,charactersWithoutSpaces:0,words:0,lines:0,paragraphs:0});(0,e.useEffect)(()=>{s(t)},[t]);const s=e=>{const t=e.length,n=e.replace(/\s/g,"").length,r=""===e.trim()?0:e.trim().split(/\s+/).filter(e=>e.length>0).length,i=""===e?0:e.split("\n").length,s=""===e.trim()?0:e.trim().split(/\n\s*\n/).filter(e=>e.trim().length>0).length;a({charactersWithSpaces:t,charactersWithoutSpaces:n,words:r,lines:i,paragraphs:s})};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u5b57\u6570\u7edf\u8ba1\u5de5\u5177"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-2",children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u8f93\u5165\u6587\u672c\uff1a"}),(0,i.jsx)("textarea",{value:t,onChange:e=>n(e.target.value),className:"textarea-field h-96",placeholder:"\u8bf7\u5728\u6b64\u8f93\u5165\u9700\u8981\u7edf\u8ba1\u7684\u6587\u672c\u5185\u5bb9..."})]}),(0,i.jsx)("div",{className:"flex gap-3",children:(0,i.jsx)("button",{onClick:()=>{n("")},className:"btn-secondary",children:"\u6e05\u7a7a\u6587\u672c"})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\u7edf\u8ba1\u7ed3\u679c"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"\u5b57\u7b26\u6570\uff08\u542b\u7a7a\u683c\uff09"}),(0,i.jsx)("div",{className:"text-2xl font-bold text-blue-800",children:r.charactersWithSpaces.toLocaleString()})]}),(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"\u5b57\u7b26\u6570\uff08\u4e0d\u542b\u7a7a\u683c\uff09"}),(0,i.jsx)("div",{className:"text-2xl font-bold text-green-800",children:r.charactersWithoutSpaces.toLocaleString()})]}),(0,i.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"\u5355\u8bcd\u6570"}),(0,i.jsx)("div",{className:"text-2xl font-bold text-purple-800",children:r.words.toLocaleString()})]}),(0,i.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-sm text-orange-600 font-medium",children:"\u884c\u6570"}),(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-800",children:r.lines.toLocaleString()})]}),(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-sm text-red-600 font-medium",children:"\u6bb5\u843d\u6570"}),(0,i.jsx)("div",{className:"text-2xl font-bold text-red-800",children:r.paragraphs.toLocaleString()})]})]}),t.length>0&&(0,i.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"\u989d\u5916\u4fe1\u606f"}),(0,i.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,i.jsxs)("div",{children:["\u5e73\u5747\u6bcf\u884c\u5b57\u7b26\u6570: ",r.lines>0?Math.round(r.charactersWithSpaces/r.lines):0]}),(0,i.jsxs)("div",{children:["\u5e73\u5747\u6bcf\u6bb5\u5b57\u7b26\u6570: ",r.paragraphs>0?Math.round(r.charactersWithSpaces/r.paragraphs):0]}),(0,i.jsxs)("div",{children:["\u5e73\u5747\u6bcf\u6bb5\u5355\u8bcd\u6570: ",r.paragraphs>0?Math.round(r.words/r.paragraphs):0]})]})]})]})]})]})},p=()=>{const[t,n]=(0,e.useState)(""),[r,a]=(0,e.useState)({g:!0,i:!1,m:!1,s:!1,u:!1,y:!1}),[s,o]=(0,e.useState)(""),[l,c]=(0,e.useState)(""),[u,f]=(0,e.useState)("matches"),[h,p]=(0,e.useState)({isValid:!0,matches:[],groups:[],replaced:"",error:null});(0,e.useEffect)(()=>{m()},[t,r,s,l]);const m=()=>{if(t&&s)try{const e=Object.entries(r).filter(e=>{let[t,n]=e;return n}).map(e=>{let[t,n]=e;return t}).join(""),n=new RegExp(t,e),i=[],a=[];let o;if(r.g)for(;null!==(o=n.exec(s));)i.push({match:o[0],index:o.index,groups:o.slice(1)}),o.index===n.lastIndex&&n.lastIndex++;else o=n.exec(s),o&&i.push({match:o[0],index:o.index,groups:o.slice(1)});i.length>0&&i.forEach((e,t)=>{e.groups.forEach((e,n)=>{void 0!==e&&a.push({matchIndex:t,groupIndex:n+1,value:e})})});let c="";""!==l&&(c=s.replace(n,l)),p({isValid:!0,matches:i,groups:a,replaced:c,error:null})}catch(e){p({isValid:!1,matches:[],groups:[],replaced:"",error:e.message})}else p({isValid:!0,matches:[],groups:[],replaced:"",error:null})};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u6b63\u5219\u8868\u8fbe\u5f0f\u6d4b\u8bd5\u5de5\u5177"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u6b63\u5219\u8868\u8fbe\u5f0f\uff1a"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"/"}),(0,i.jsx)("input",{type:"text",value:t,onChange:e=>n(e.target.value),className:"input-field flex-1",placeholder:"\u8f93\u5165\u6b63\u5219\u8868\u8fbe\u5f0f..."}),(0,i.jsx)("span",{className:"text-gray-500",children:"/"}),(0,i.jsx)("div",{className:"flex space-x-1",children:Object.entries(r).map(e=>{let[t,n]=e;return(0,i.jsx)("button",{onClick:()=>(e=>{a(t=>d(d({},t),{},{[e]:!t[e]}))})(t),className:"px-2 py-1 text-xs rounded ".concat(n?"bg-blue-600 text-white":"bg-gray-200 text-gray-600 hover:bg-gray-300"),title:"".concat(t," flag"),children:t},t)})})]}),(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"\u5e38\u7528\u6a21\u5f0f\uff1a"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:[{name:"\u90ae\u7bb1",pattern:"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"},{name:"\u624b\u673a\u53f7",pattern:"1[3-9]\\d{9}"},{name:"IP\u5730\u5740",pattern:"\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b"},{name:"URL",pattern:"https?://[^\\s]+"},{name:"\u8eab\u4efd\u8bc1",pattern:"[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]"},{name:"\u4e2d\u6587\u5b57\u7b26",pattern:"[\\u4e00-\\u9fa5]+"}].map(e=>(0,i.jsx)("button",{onClick:()=>n(e.pattern),className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-600",children:e.name},e.name))})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u6d4b\u8bd5\u6587\u672c\uff1a"}),(0,i.jsx)("textarea",{value:s,onChange:e=>o(e.target.value),className:"textarea-field h-32",placeholder:"\u8f93\u5165\u8981\u6d4b\u8bd5\u7684\u6587\u672c..."})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:m,className:"btn-primary",children:"\u6d4b\u8bd5"}),(0,i.jsx)("button",{onClick:()=>{n(""),o(""),c(""),p({isValid:!0,matches:[],groups:[],replaced:"",error:null})},className:"btn-secondary",children:"\u6e05\u7a7a"}),(0,i.jsx)("button",{onClick:()=>{let e="";"matches"===u?e=h.matches.map(e=>e.match).join("\n"):"groups"===u?e=h.groups.map(e=>"Group ".concat(e.groupIndex,": ").concat(e.value)).join("\n"):"replace"===u&&(e=h.replaced),navigator.clipboard.writeText(e)},className:"btn-success",disabled:!h.matches.length,children:"\u590d\u5236\u7ed3\u679c"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,i.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"\u5339\u914d\u6570\u91cf"}),(0,i.jsx)("div",{className:"text-xl font-bold text-blue-800",children:h.matches.length})]}),(0,i.jsxs)("div",{className:"border rounded-lg p-3 ".concat(h.isValid?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,i.jsx)("div",{className:"text-sm font-medium ".concat(h.isValid?"text-green-600":"text-red-600"),children:"\u6b63\u5219\u72b6\u6001"}),(0,i.jsx)("div",{className:"text-xl font-bold ".concat(h.isValid?"text-green-800":"text-red-800"),children:h.isValid?"\u6709\u6548":"\u65e0\u6548"})]}),(0,i.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[(0,i.jsx)("div",{className:"text-sm text-purple-600 font-medium",children:"\u5206\u7ec4\u6570\u91cf"}),(0,i.jsx)("div",{className:"text-xl font-bold text-purple-800",children:h.groups.length})]})]}),h.error&&(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("div",{className:"text-red-800 font-medium",children:"\u9519\u8bef\u4fe1\u606f\uff1a"}),(0,i.jsx)("div",{className:"text-red-600 text-sm mt-1",children:h.error})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"flex space-x-1 border-b border-gray-200 mb-4",children:[{id:"matches",name:"\u5339\u914d\u7ed3\u679c"},{id:"groups",name:"\u5206\u7ec4\u8be6\u60c5"},{id:"replace",name:"\u66ff\u6362\u6d4b\u8bd5"}].map(e=>(0,i.jsx)("button",{onClick:()=>f(e.id),className:"tab-button ".concat(u===e.id?"active":"inactive"),children:e.name},e.id))}),"matches"===u&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"\u9ad8\u4eae\u663e\u793a\uff1a"}),(0,i.jsx)("div",{className:"result-area",dangerouslySetInnerHTML:{__html:(e=>{if(!h.matches.length||!h.isValid)return e;let t=e,n=0;return h.matches.forEach(e=>{const r=e.index+n,i=r+e.match.length,a=t.substring(0,r),s=t.substring(r,i),o=t.substring(i);t=a+'<mark class="bg-yellow-200 px-1 rounded">'.concat(s,"</mark>")+o,n+=47}),t})(s)||"\u8f93\u5165\u6b63\u5219\u8868\u8fbe\u5f0f\u548c\u6d4b\u8bd5\u6587\u672c\uff0c\u5339\u914d\u7ed3\u679c\u5c06\u5728\u8fd9\u91cc\u9ad8\u4eae\u663e\u793a"}})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"\u5339\u914d\u5217\u8868\uff1a"}),(0,i.jsx)("div",{className:"result-area",children:h.matches.length>0?h.matches.map((e,t)=>(0,i.jsxs)("div",{className:"mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded",children:[(0,i.jsx)("div",{className:"font-mono text-sm",children:e.match}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["\u4f4d\u7f6e: ",e.index]})]},t)):(0,i.jsx)("div",{className:"text-gray-500",children:"\u6682\u65e0\u5339\u914d\u7ed3\u679c"})})]})]}),"groups"===u&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"\u5206\u7ec4\u6355\u83b7\uff1a"}),(0,i.jsx)("div",{className:"result-area",children:h.groups.length>0?h.groups.map((e,t)=>(0,i.jsxs)("div",{className:"mb-2 p-2 bg-blue-50 border border-blue-200 rounded",children:[(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("span",{className:"font-medium",children:["\u5206\u7ec4 ",e.groupIndex,":"]}),(0,i.jsx)("span",{className:"font-mono ml-2",children:e.value})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["\u5339\u914d ",e.matchIndex+1]})]},t)):(0,i.jsx)("div",{className:"text-gray-500",children:"\u6682\u65e0\u5206\u7ec4\u4fe1\u606f"})})]}),"replace"===u&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u66ff\u6362\u6587\u672c\uff1a"}),(0,i.jsx)("input",{type:"text",value:l,onChange:e=>c(e.target.value),className:"input-field",placeholder:"\u8f93\u5165\u66ff\u6362\u6587\u672c..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"\u66ff\u6362\u7ed3\u679c\uff1a"}),(0,i.jsx)("div",{className:"result-area",children:l&&h.replaced||"\u8f93\u5165\u66ff\u6362\u6587\u672c\u67e5\u770b\u7ed3\u679c"})]})]})]})]})]})},m=()=>{const[t,n]=(0,e.useState)(""),[r,a]=(0,e.useState)(""),[s,o]=(0,e.useState)("differences"),[l,c]=(0,e.useState)({statusA:"\u5f85\u9a8c\u8bc1",statusB:"\u5f85\u9a8c\u8bc1",compareStatus:"\u5f85\u5bf9\u6bd4",differences:[],structure:null,summary:{sameFields:0,differentFields:0,newFields:0,deletedFields:0}}),u=(e,t)=>{try{const n=JSON.parse(e);t(JSON.stringify(n,null,2))}catch(n){}},f=e=>{try{return JSON.parse(e),{isValid:!0,error:null}}catch(t){return{isValid:!1,error:t.message}}},h=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";const r=[];return new Set([...Object.keys(e||{}),...Object.keys(t||{})]).forEach(i=>{const a=n?"".concat(n,".").concat(i):i,s=null===e||void 0===e?void 0:e[i],o=null===t||void 0===t?void 0:t[i];void 0===s?r.push({path:a,type:"added",valueA:void 0,valueB:o}):void 0===o?r.push({path:a,type:"deleted",valueA:s,valueB:void 0}):"object"!==typeof s||"object"!==typeof o||null===s||null===o||Array.isArray(s)||Array.isArray(o)?JSON.stringify(s)!==JSON.stringify(o)?r.push({path:a,type:"modified",valueA:s,valueB:o}):r.push({path:a,type:"same",valueA:s,valueB:o}):r.push(...h(s,o,a))}),r},p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if("object"!==typeof e||null===e)return{[t]:typeof e};const n={};return Object.keys(e).forEach(r=>{const i=t?"".concat(t,".").concat(r):r;"object"!==typeof e[r]||null===e[r]||Array.isArray(e[r])?n[i]=Array.isArray(e[r])?"array":typeof e[r]:Object.assign(n,p(e[r],i))}),n},m=e=>{switch(e){case"same":return"text-green-600 bg-green-50 border-green-200";case"modified":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"added":return"text-blue-600 bg-blue-50 border-blue-200";case"deleted":return"text-red-600 bg-red-50 border-red-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}},g=e=>{switch(e){case"same":return"\u76f8\u540c";case"modified":return"\u4fee\u6539";case"added":return"\u65b0\u589e";case"deleted":return"\u5220\u9664";default:return"\u672a\u77e5"}};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"JSON\u6570\u636e\u5bf9\u6bd4\u5de5\u5177"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)("label",{className:"text-lg font-medium text-gray-800",children:"JSON\u6570\u636e A\uff1a"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("button",{onClick:()=>u(t,n),className:"text-sm btn-secondary",children:"\u683c\u5f0f\u5316"}),(0,i.jsx)("button",{onClick:()=>n(""),className:"text-sm btn-secondary",children:"\u6e05\u7a7a"})]})]}),(0,i.jsx)("textarea",{value:t,onChange:e=>n(e.target.value),className:"textarea-field h-64 font-mono text-sm",placeholder:'\u8f93\u5165JSON\u6570\u636eA\uff0c\u4f8b\u5982\uff1a{"name": "\u5f20\u4e09", "age": 25}'})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)("label",{className:"text-lg font-medium text-gray-800",children:"JSON\u6570\u636e B\uff1a"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("button",{onClick:()=>u(r,a),className:"text-sm btn-secondary",children:"\u683c\u5f0f\u5316"}),(0,i.jsx)("button",{onClick:()=>a(""),className:"text-sm btn-secondary",children:"\u6e05\u7a7a"})]})]}),(0,i.jsx)("textarea",{value:r,onChange:e=>a(e.target.value),className:"textarea-field h-64 font-mono text-sm",placeholder:'\u8f93\u5165JSON\u6570\u636eB\uff0c\u4f8b\u5982\uff1a{"name": "\u674e\u56db", "age": 30}'})]})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:()=>{const e=f(t),n=f(r);let i={statusA:e.isValid?"\u6709\u6548":"\u65e0\u6548: ".concat(e.error),statusB:n.isValid?"\u6709\u6548":"\u65e0\u6548: ".concat(n.error),compareStatus:"\u5f85\u5bf9\u6bd4",differences:[],structure:null,summary:{sameFields:0,differentFields:0,newFields:0,deletedFields:0}};if(e.isValid&&n.isValid)try{const e=JSON.parse(t),n=JSON.parse(r),a=h(e,n),s=a.reduce((e,t)=>{switch(t.type){case"same":e.sameFields++;break;case"modified":e.differentFields++;break;case"added":e.newFields++;break;case"deleted":e.deletedFields++}return e},{sameFields:0,differentFields:0,newFields:0,deletedFields:0}),o=p(e),l=p(n);i=d(d({},i),{},{compareStatus:"\u5bf9\u6bd4\u5b8c\u6210",differences:a,structure:{A:o,B:l},summary:s})}catch(a){i.compareStatus="\u5bf9\u6bd4\u5931\u8d25: ".concat(a.message)}else i.compareStatus="\u8bf7\u786e\u4fdd\u4e24\u4e2aJSON\u90fd\u6709\u6548";c(i)},className:"btn-primary",children:"\u5bf9\u6bd4JSON"}),(0,i.jsx)("button",{onClick:()=>{n(""),a(""),c({statusA:"\u5f85\u9a8c\u8bc1",statusB:"\u5f85\u9a8c\u8bc1",compareStatus:"\u5f85\u5bf9\u6bd4",differences:[],structure:null,summary:{sameFields:0,differentFields:0,newFields:0,deletedFields:0}})},className:"btn-secondary",children:"\u6e05\u7a7a\u5168\u90e8"}),(0,i.jsx)("button",{onClick:()=>{let e="";if("differences"===s)e=l.differences.map(e=>"".concat(e.path,": ").concat(e.type," - A: ").concat(JSON.stringify(e.valueA)," | B: ").concat(JSON.stringify(e.valueB))).join("\n");else if("structure"===s){var t,n;e="\u7ed3\u6784A:\n".concat(JSON.stringify(null===(t=l.structure)||void 0===t?void 0:t.A,null,2),"\n\n\u7ed3\u6784B:\n").concat(JSON.stringify(null===(n=l.structure)||void 0===n?void 0:n.B,null,2))}else"summary"===s&&(e="\u5bf9\u6bd4\u6458\u8981:\n\u76f8\u540c\u5b57\u6bb5: ".concat(l.summary.sameFields,"\n\u4e0d\u540c\u5b57\u6bb5: ").concat(l.summary.differentFields,"\n\u65b0\u589e\u5b57\u6bb5: ").concat(l.summary.newFields,"\n\u5220\u9664\u5b57\u6bb5: ").concat(l.summary.deletedFields));navigator.clipboard.writeText(e)},className:"btn-success",disabled:!l.differences.length,children:"\u590d\u5236\u7ed3\u679c"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{className:"border rounded-lg p-3 ".concat(l.statusA.includes("\u6709\u6548")?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-600",children:"JSON A \u72b6\u6001"}),(0,i.jsx)("div",{className:"text-sm ".concat(l.statusA.includes("\u6709\u6548")?"text-green-800":"text-red-800"),children:l.statusA})]}),(0,i.jsxs)("div",{className:"border rounded-lg p-3 ".concat(l.statusB.includes("\u6709\u6548")?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-600",children:"JSON B \u72b6\u6001"}),(0,i.jsx)("div",{className:"text-sm ".concat(l.statusB.includes("\u6709\u6548")?"text-green-800":"text-red-800"),children:l.statusB})]}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-blue-600",children:"\u5bf9\u6bd4\u7ed3\u679c"}),(0,i.jsx)("div",{className:"text-sm text-blue-800",children:l.compareStatus})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"flex space-x-1 border-b border-gray-200 mb-4",children:[{id:"differences",name:"\u5dee\u5f02\u8be6\u60c5"},{id:"structure",name:"\u7ed3\u6784\u5bf9\u6bd4"},{id:"summary",name:"\u5bf9\u6bd4\u6458\u8981"}].map(e=>(0,i.jsx)("button",{onClick:()=>o(e.id),className:"tab-button ".concat(s===e.id?"active":"inactive"),children:e.name},e.id))}),"differences"===s&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"\u5dee\u5f02\u5217\u8868\uff1a"}),(0,i.jsx)("div",{className:"result-area max-h-96 overflow-y-auto",children:l.differences.length>0?l.differences.map((e,t)=>(0,i.jsxs)("div",{className:"mb-2 p-3 border rounded ".concat(m(e.type)),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,i.jsx)("span",{className:"font-medium text-sm",children:e.path}),(0,i.jsx)("span",{className:"text-xs px-2 py-1 rounded bg-white border",children:g(e.type)})]}),(0,i.jsxs)("div",{className:"text-xs space-y-1",children:[(0,i.jsxs)("div",{children:["A: ",(0,i.jsx)("span",{className:"font-mono",children:JSON.stringify(e.valueA)})]}),(0,i.jsxs)("div",{children:["B: ",(0,i.jsx)("span",{className:"font-mono",children:JSON.stringify(e.valueB)})]})]})]},t)):(0,i.jsx)("div",{className:"text-gray-500",children:"\u8f93\u5165\u4e24\u4e2aJSON\u6570\u636e\u8fdb\u884c\u5bf9\u6bd4\uff0c\u5dee\u5f02\u5c06\u5728\u8fd9\u91cc\u663e\u793a"})})]}),"structure"===s&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"\u7ed3\u6784\u5bf9\u6bd4\uff1a"}),(0,i.jsx)("div",{className:"result-area",children:l.structure?(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium text-sm mb-2",children:"JSON A \u7ed3\u6784\uff1a"}),(0,i.jsx)("pre",{className:"text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(l.structure.A,null,2)})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h5",{className:"font-medium text-sm mb-2",children:"JSON B \u7ed3\u6784\uff1a"}),(0,i.jsx)("pre",{className:"text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(l.structure.B,null,2)})]})]}):(0,i.jsx)("div",{className:"text-gray-500",children:"JSON\u7ed3\u6784\u5bf9\u6bd4\u4fe1\u606f\u5c06\u5728\u8fd9\u91cc\u663e\u793a"})})]}),"summary"===s&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"\u5bf9\u6bd4\u6458\u8981\uff1a"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 text-center",children:[(0,i.jsx)("div",{className:"text-sm text-green-600 font-medium",children:"\u76f8\u540c\u5b57\u6bb5"}),(0,i.jsx)("div",{className:"text-xl font-bold text-green-800",children:l.summary.sameFields})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center",children:[(0,i.jsx)("div",{className:"text-sm text-yellow-600 font-medium",children:"\u4e0d\u540c\u5b57\u6bb5"}),(0,i.jsx)("div",{className:"text-xl font-bold text-yellow-800",children:l.summary.differentFields})]}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 text-center",children:[(0,i.jsx)("div",{className:"text-sm text-blue-600 font-medium",children:"\u65b0\u589e\u5b57\u6bb5"}),(0,i.jsx)("div",{className:"text-xl font-bold text-blue-800",children:l.summary.newFields})]}),(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 text-center",children:[(0,i.jsx)("div",{className:"text-sm text-red-600 font-medium",children:"\u5220\u9664\u5b57\u6bb5"}),(0,i.jsx)("div",{className:"text-xl font-bold text-red-800",children:l.summary.deletedFields})]})]})]})]})]})]})},g=()=>{const[t,n]=(0,e.useState)("timestamp"),[r,a]=(0,e.useState)(""),[s,o]=(0,e.useState)(""),[l,c]=(0,e.useState)("");return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u65f6\u95f4\u5904\u7406\u5de5\u5177"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6 border-b border-gray-200",children:[{id:"timestamp",name:"\u65f6\u95f4\u6233\u8f6c\u6362"},{id:"calculator",name:"\u65f6\u95f4\u6362\u7b97\u5de5\u5177"},{id:"date-calc",name:"\u65e5\u671f\u8ba1\u7b97\u5668"}].map(e=>(0,i.jsx)("button",{onClick:()=>n(e.id),className:"tab-button ".concat(t===e.id?"active":"inactive"),children:e.name},e.id))}),"timestamp"===t&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u65f6\u95f4\u6233\u8f6c\u65e5\u671f"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("input",{type:"text",value:r,onChange:e=>a(e.target.value),placeholder:"\u8f93\u5165\u65f6\u95f4\u6233\uff08\u79d2\uff09",className:"input-field"}),(0,i.jsx)("button",{onClick:()=>{try{const e=parseInt(r),t=new Date(1e3*e);c(t.toLocaleString("zh-CN"))}catch(e){c("\u8f6c\u6362\u5931\u8d25\uff1a\u8bf7\u8f93\u5165\u6709\u6548\u7684\u65f6\u95f4\u6233")}},className:"btn-primary w-full",children:"\u8f6c\u6362\u4e3a\u65e5\u671f"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u65e5\u671f\u8f6c\u65f6\u95f4\u6233"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("input",{type:"datetime-local",value:s,onChange:e=>o(e.target.value),className:"input-field"}),(0,i.jsx)("button",{onClick:()=>{try{const e=new Date(s),t=Math.floor(e.getTime()/1e3);c(t.toString())}catch(e){c("\u8f6c\u6362\u5931\u8d25\uff1a\u8bf7\u8f93\u5165\u6709\u6548\u7684\u65e5\u671f\u65f6\u95f4")}},className:"btn-primary w-full",children:"\u8f6c\u6362\u4e3a\u65f6\u95f4\u6233"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u5f53\u524d\u65f6\u95f4\u6233"}),(0,i.jsx)("button",{onClick:()=>{const e=Math.floor(Date.now()/1e3);a(e.toString()),c(new Date(1e3*e).toLocaleString("zh-CN"))},className:"btn-success w-full",children:"\u83b7\u53d6\u5f53\u524d\u65f6\u95f4\u6233"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u8f6c\u6362\u7ed3\u679c"}),(0,i.jsx)("div",{className:"result-area h-32",children:l||"\u8f6c\u6362\u7ed3\u679c\u5c06\u5728\u8fd9\u91cc\u663e\u793a"})]})]})}),"calculator"===t&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"\u5e38\u7528\u65f6\u95f4\u6362\u7b97\u8868"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{children:"1\u5206\u949f = 60\u79d2"}),(0,i.jsx)("div",{children:"1\u5c0f\u65f6 = 60\u5206\u949f = 3,600\u79d2"}),(0,i.jsx)("div",{children:"1\u5929 = 24\u5c0f\u65f6 = 1,440\u5206\u949f"}),(0,i.jsx)("div",{children:"1\u5468 = 7\u5929 = 168\u5c0f\u65f6"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{children:"1\u6708 \u2248 30\u5929 = 720\u5c0f\u65f6"}),(0,i.jsx)("div",{children:"1\u5e74 = 365\u5929 = 8,760\u5c0f\u65f6"}),(0,i.jsx)("div",{children:"1\u5e74 = 52\u5468 = 12\u4e2a\u6708"}),(0,i.jsx)("div",{children:"1\u95f0\u5e74 = 366\u5929 = 8,784\u5c0f\u65f6"})]})]})]})}),"date-calc"===t&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u65e5\u671f\u52a0\u51cf\u8ba1\u7b97"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("input",{type:"date",className:"input-field",placeholder:"\u8d77\u59cb\u65e5\u671f"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)("select",{className:"select-field",children:[(0,i.jsx)("option",{value:"add",children:"\u52a0\u4e0a"}),(0,i.jsx)("option",{value:"subtract",children:"\u51cf\u53bb"})]}),(0,i.jsx)("input",{type:"number",className:"input-field",placeholder:"\u6570\u91cf"}),(0,i.jsxs)("select",{className:"select-field",children:[(0,i.jsx)("option",{value:"days",children:"\u5929"}),(0,i.jsx)("option",{value:"weeks",children:"\u5468"}),(0,i.jsx)("option",{value:"months",children:"\u6708"}),(0,i.jsx)("option",{value:"years",children:"\u5e74"})]})]}),(0,i.jsx)("button",{className:"btn-primary w-full",children:"\u8ba1\u7b97"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u65e5\u671f\u95f4\u9694\u8ba1\u7b97"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("input",{type:"date",className:"input-field",placeholder:"\u5f00\u59cb\u65e5\u671f"}),(0,i.jsx)("input",{type:"date",className:"input-field",placeholder:"\u7ed3\u675f\u65e5\u671f"}),(0,i.jsx)("button",{className:"btn-primary w-full",children:"\u8ba1\u7b97\u95f4\u9694"})]})]})]})})]})},v=()=>{const[t,n]=(0,e.useState)(null),[r,a]=(0,e.useState)({fileName:"",format:"",size:"",fileSize:""}),[s,o]=(0,e.useState)({outputFormat:"JPEG",quality:90,compressionMode:"\u8d28\u91cf\u4f18\u5148",targetSize:"",targetUnit:"KB",enableResize:!1,width:"",height:"",keepRatio:!0}),l=e=>{const t=e.target.files[0];if(t){n(t);const e=new Image;e.onload=()=>{a({fileName:t.name,format:t.type,size:"".concat(e.width," \xd7 ").concat(e.height),fileSize:c(t.size)})},e.src=URL.createObjectURL(t)}},c=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u56fe\u7247\u5904\u7406\u5de5\u5177"}),(0,i.jsxs)("div",{className:"space-y-6",children:[t?(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"\u539f\u59cb\u56fe\u7247"}),(0,i.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,i.jsx)("img",{src:URL.createObjectURL(t),alt:"\u9884\u89c8",className:"max-w-full h-auto max-h-64 mx-auto rounded"})}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u6587\u4ef6\u540d\uff1a"}),r.fileName]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u683c\u5f0f\uff1a"}),r.format]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u5c3a\u5bf8\uff1a"}),r.size]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u6587\u4ef6\u5927\u5c0f\uff1a"}),r.fileSize]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"\u8f6c\u6362\u8bbe\u7f6e"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u8f93\u51fa\u683c\u5f0f\uff1a"}),(0,i.jsxs)("select",{value:s.outputFormat,onChange:e=>o(t=>d(d({},t),{},{outputFormat:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"JPEG",children:"JPEG"}),(0,i.jsx)("option",{value:"PNG",children:"PNG"}),(0,i.jsx)("option",{value:"WEBP",children:"WEBP"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["\u56fe\u7247\u8d28\u91cf\uff1a",s.quality,"%"]}),(0,i.jsx)("input",{type:"range",min:"1",max:"100",value:s.quality,onChange:e=>o(t=>d(d({},t),{},{quality:parseInt(e.target.value)})),className:"w-full"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u538b\u7f29\u6a21\u5f0f\uff1a"}),(0,i.jsxs)("select",{value:s.compressionMode,onChange:e=>o(t=>d(d({},t),{},{compressionMode:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"\u8d28\u91cf\u4f18\u5148",children:"\u8d28\u91cf\u4f18\u5148"}),(0,i.jsx)("option",{value:"\u4f53\u79ef\u4f18\u5148",children:"\u4f53\u79ef\u4f18\u5148"}),(0,i.jsx)("option",{value:"\u5e73\u8861\u6a21\u5f0f",children:"\u5e73\u8861\u6a21\u5f0f"}),(0,i.jsx)("option",{value:"\u81ea\u5b9a\u4e49\u76ee\u6807",children:"\u81ea\u5b9a\u4e49\u76ee\u6807"})]})]}),"\u81ea\u5b9a\u4e49\u76ee\u6807"===s.compressionMode&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u76ee\u6807\u6587\u4ef6\u5927\u5c0f\uff1a"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("input",{type:"number",value:s.targetSize,onChange:e=>o(t=>d(d({},t),{},{targetSize:e.target.value})),className:"input-field flex-1",placeholder:"\u76ee\u6807\u5927\u5c0f"}),(0,i.jsxs)("select",{value:s.targetUnit,onChange:e=>o(t=>d(d({},t),{},{targetUnit:e.target.value})),className:"select-field w-20",children:[(0,i.jsx)("option",{value:"KB",children:"KB"}),(0,i.jsx)("option",{value:"MB",children:"MB"})]})]})]}),(0,i.jsx)("div",{children:(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:s.enableResize,onChange:e=>o(t=>d(d({},t),{},{enableResize:e.target.checked})),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"\u542f\u7528\u5c3a\u5bf8\u8c03\u6574"})]})}),s.enableResize&&(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,i.jsx)("input",{type:"number",value:s.width,onChange:e=>o(t=>d(d({},t),{},{width:e.target.value})),className:"input-field",placeholder:"\u5bbd\u5ea6"}),(0,i.jsx)("span",{children:"\xd7"}),(0,i.jsx)("input",{type:"number",value:s.height,onChange:e=>o(t=>d(d({},t),{},{height:e.target.value})),className:"input-field",placeholder:"\u9ad8\u5ea6"})]}),(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:s.keepRatio,onChange:e=>o(t=>d(d({},t),{},{keepRatio:e.target.checked})),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"\u4fdd\u6301\u6bd4\u4f8b"})]})]}),(0,i.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,i.jsx)("button",{onClick:()=>{t&&alert("\u56fe\u7247\u8f6c\u6362\u529f\u80fd\u9700\u8981\u540e\u7aef\u652f\u6301\uff0c\u8fd9\u91cc\u4ec5\u4e3a\u6f14\u793a\u754c\u9762")},className:"btn-primary",children:"\u8f6c\u6362\u56fe\u7247"}),(0,i.jsx)("button",{onClick:()=>{n(null),a({fileName:"",format:"",size:"",fileSize:""})},className:"btn-secondary",children:"\u91cd\u65b0\u9009\u62e9"})]})]})]})]}):(0,i.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-blue-400 transition-colors",onDrop:e=>{e.preventDefault();const t=e.dataTransfer.files[0];t&&t.type.startsWith("image/")&&(n(t),l({target:{files:[t]}}))},onDragOver:e=>{e.preventDefault()},children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:"\ud83d\udcf7"}),(0,i.jsx)("div",{className:"text-lg text-gray-600 mb-2",children:"\u70b9\u51fb\u6216\u62d6\u62fd\u56fe\u7247\u5230\u6b64\u5904\u4e0a\u4f20"}),(0,i.jsx)("div",{className:"text-sm text-gray-500 mb-4",children:"\u652f\u6301 JPG\u3001PNG\u3001GIF\u3001BMP\u3001WEBP \u683c\u5f0f"}),(0,i.jsx)("input",{type:"file",accept:"image/*",onChange:l,className:"hidden",id:"image-upload"}),(0,i.jsx)("label",{htmlFor:"image-upload",className:"btn-primary cursor-pointer",children:"\ud83d\udcc1 \u9009\u62e9\u56fe\u7247"})]}),t&&(0,i.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-4",children:"\u8f6c\u6362\u540e\u56fe\u7247"}),(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 text-center",children:[(0,i.jsx)("div",{className:"text-gray-500 mb-4",children:"\u8f6c\u6362\u540e\u7684\u56fe\u7247\u5c06\u5728\u8fd9\u91cc\u663e\u793a"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u683c\u5f0f\uff1a"}),(0,i.jsx)("span",{children:s.outputFormat})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u5c3a\u5bf8\uff1a"}),(0,i.jsx)("span",{children:"\u5f85\u8f6c\u6362"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u6587\u4ef6\u5927\u5c0f\uff1a"}),(0,i.jsx)("span",{children:"\u5f85\u8f6c\u6362"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"\u538b\u7f29\u7387\uff1a"}),(0,i.jsx)("span",{children:"\u5f85\u8f6c\u6362"})]})]}),(0,i.jsx)("button",{className:"btn-success mt-4",disabled:!0,children:"\u4e0b\u8f7d\u56fe\u7247"})]})]})]})]})};var y=n(111),b=n.n(y);function x(e){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(e)}function w(e,t){return e&t}function S(e,t){return e|t}function k(e,t){return e^t}function N(e,t){return e&~t}function j(e){if(0==e)return-1;var t=0;return 0==(65535&e)&&(e>>=16,t+=16),0==(255&e)&&(e>>=8,t+=8),0==(15&e)&&(e>>=4,t+=4),0==(3&e)&&(e>>=2,t+=2),0==(1&e)&&++t,t}function E(e){for(var t=0;0!=e;)e&=e-1,++t;return t}var _,C="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function T(e){var t,n,r="";for(t=0;t+3<=e.length;t+=3)n=parseInt(e.substring(t,t+3),16),r+=C.charAt(n>>6)+C.charAt(63&n);for(t+1==e.length?(n=parseInt(e.substring(t,t+1),16),r+=C.charAt(n<<2)):t+2==e.length&&(n=parseInt(e.substring(t,t+2),16),r+=C.charAt(n>>2)+C.charAt((3&n)<<4));(3&r.length)>0;)r+="=";return r}function B(e){var t,n="",r=0,i=0;for(t=0;t<e.length&&"="!=e.charAt(t);++t){var a=C.indexOf(e.charAt(t));a<0||(0==r?(n+=x(a>>2),i=3&a,r=1):1==r?(n+=x(i<<2|a>>4),i=15&a,r=2):2==r?(n+=x(i),n+=x(a>>2),i=3&a,r=3):(n+=x(i<<2|a>>4),n+=x(15&a),r=0))}return 1==r&&(n+=x(i<<2)),n}var D,R=function(e){var t;if(void 0===_){var n="0123456789ABCDEF",r=" \f\n\r\t\xa0\u2028\u2029";for(_={},t=0;t<16;++t)_[n.charAt(t)]=t;for(n=n.toLowerCase(),t=10;t<16;++t)_[n.charAt(t)]=t;for(t=0;t<8;++t)_[r.charAt(t)]=-1}var i=[],a=0,s=0;for(t=0;t<e.length;++t){var o=e.charAt(t);if("="==o)break;if(-1!=(o=_[o])){if(void 0===o)throw new Error("Illegal character at offset "+t);a|=o,++s>=2?(i[i.length]=a,a=0,s=0):a<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return i},P={decode:function(e){var t;if(void 0===D){var n="= \f\n\r\t\xa0\u2028\u2029";for(D=Object.create(null),t=0;t<64;++t)D["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(D["-"]=62,D._=63,t=0;t<9;++t)D[n.charAt(t)]=-1}var r=[],i=0,a=0;for(t=0;t<e.length;++t){var s=e.charAt(t);if("="==s)break;if(-1!=(s=D[s])){if(void 0===s)throw new Error("Illegal character at offset "+t);i|=s,++a>=4?(r[r.length]=i>>16,r[r.length]=i>>8&255,r[r.length]=255&i,i=0,a=0):i<<=6}}switch(a){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=i>>10;break;case 3:r[r.length]=i>>16,r[r.length]=i>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=P.re.exec(e);if(t)if(t[1])e=t[1];else{if(!t[2])throw new Error("RegExp out of sync");e=t[2]}return P.decode(e)}},O=1e13,A=function(){function e(e){this.buf=[+e||0]}return e.prototype.mulAdd=function(e,t){var n,r,i=this.buf,a=i.length;for(n=0;n<a;++n)(r=i[n]*e+t)<O?t=0:r-=(t=0|r/O)*O,i[n]=r;t>0&&(i[n]=t)},e.prototype.sub=function(e){var t,n,r=this.buf,i=r.length;for(t=0;t<i;++t)(n=r[t]-e)<0?(n+=O,e=1):e=0,r[t]=n;for(;0===r[r.length-1];)r.pop()},e.prototype.toString=function(e){if(10!=(e||10))throw new Error("only base 10 is supported");for(var t=this.buf,n=t[t.length-1].toString(),r=t.length-2;r>=0;--r)n+=(O+t[r]).toString().substring(1);return n},e.prototype.valueOf=function(){for(var e=this.buf,t=0,n=e.length-1;n>=0;--n)t=t*O+e[n];return t},e.prototype.simplify=function(){var e=this.buf;return 1==e.length?e[0]:this},e}(),z=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,L=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function M(e,t){return e.length>t&&(e=e.substring(0,t)+"\u2026"),e}var I,F=function(){function e(t,n){this.hexDigits="0123456789ABCDEF",t instanceof e?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=n)}return e.prototype.get=function(e){if(void 0===e&&(e=this.pos++),e>=this.enc.length)throw new Error("Requesting byte offset ".concat(e," on a stream of length ").concat(this.enc.length));return"string"===typeof this.enc?this.enc.charCodeAt(e):this.enc[e]},e.prototype.hexByte=function(e){return this.hexDigits.charAt(e>>4&15)+this.hexDigits.charAt(15&e)},e.prototype.hexDump=function(e,t,n){for(var r="",i=e;i<t;++i)if(r+=this.hexByte(this.get(i)),!0!==n)switch(15&i){case 7:r+="  ";break;case 15:r+="\n";break;default:r+=" "}return r},e.prototype.isASCII=function(e,t){for(var n=e;n<t;++n){var r=this.get(n);if(r<32||r>176)return!1}return!0},e.prototype.parseStringISO=function(e,t){for(var n="",r=e;r<t;++r)n+=String.fromCharCode(this.get(r));return n},e.prototype.parseStringUTF=function(e,t){for(var n="",r=e;r<t;){var i=this.get(r++);n+=i<128?String.fromCharCode(i):i>191&&i<224?String.fromCharCode((31&i)<<6|63&this.get(r++)):String.fromCharCode((15&i)<<12|(63&this.get(r++))<<6|63&this.get(r++))}return n},e.prototype.parseStringBMP=function(e,t){for(var n,r,i="",a=e;a<t;)n=this.get(a++),r=this.get(a++),i+=String.fromCharCode(n<<8|r);return i},e.prototype.parseTime=function(e,t,n){var r=this.parseStringISO(e,t),i=(n?z:L).exec(r);return i?(n&&(i[1]=+i[1],i[1]+=+i[1]<70?2e3:1900),r=i[1]+"-"+i[2]+"-"+i[3]+" "+i[4],i[5]&&(r+=":"+i[5],i[6]&&(r+=":"+i[6],i[7]&&(r+="."+i[7]))),i[8]&&(r+=" UTC","Z"!=i[8]&&(r+=i[8],i[9]&&(r+=":"+i[9]))),r):"Unrecognized time: "+r},e.prototype.parseInteger=function(e,t){for(var n,r=this.get(e),i=r>127,a=i?255:0,s="";r==a&&++e<t;)r=this.get(e);if(0===(n=t-e))return i?-1:0;if(n>4){for(s=r,n<<=3;0==(128&(+s^a));)s=+s<<1,--n;s="("+n+" bit)\n"}i&&(r-=256);for(var o=new A(r),l=e+1;l<t;++l)o.mulAdd(256,this.get(l));return s+o.toString()},e.prototype.parseBitString=function(e,t,n){for(var r=this.get(e),i="("+((t-e-1<<3)-r)+" bit)\n",a="",s=e+1;s<t;++s){for(var o=this.get(s),l=s==t-1?r:0,c=7;c>=l;--c)a+=o>>c&1?"1":"0";if(a.length>n)return i+M(a,n)}return i+a},e.prototype.parseOctetString=function(e,t,n){if(this.isASCII(e,t))return M(this.parseStringISO(e,t),n);var r=t-e,i="("+r+" byte)\n";r>(n/=2)&&(t=e+n);for(var a=e;a<t;++a)i+=this.hexByte(this.get(a));return r>n&&(i+="\u2026"),i},e.prototype.parseOID=function(e,t,n){for(var r="",i=new A,a=0,s=e;s<t;++s){var o=this.get(s);if(i.mulAdd(128,127&o),a+=7,!(128&o)){if(""===r)if((i=i.simplify())instanceof A)i.sub(80),r="2."+i.toString();else{var l=i<80?i<40?0:1:2;r=l+"."+(i-40*l)}else r+="."+i.toString();if(r.length>n)return M(r,n);i=new A,a=0}}return a>0&&(r+=".incomplete"),r},e}(),V=function(){function e(e,t,n,r,i){if(!(r instanceof H))throw new Error("Invalid tag value.");this.stream=e,this.header=t,this.length=n,this.tag=r,this.sub=i}return e.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},e.prototype.content=function(e){if(void 0===this.tag)return null;void 0===e&&(e=1/0);var t=this.posContent(),n=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+n,e);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(t)?"false":"true";case 2:return this.stream.parseInteger(t,t+n);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(t,t+n,e);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(t,t+n,e);case 6:return this.stream.parseOID(t,t+n,e);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return M(this.stream.parseStringUTF(t,t+n),e);case 18:case 19:case 20:case 21:case 22:case 26:return M(this.stream.parseStringISO(t,t+n),e);case 30:return M(this.stream.parseStringBMP(t,t+n),e);case 23:case 24:return this.stream.parseTime(t,t+n,23==this.tag.tagNumber)}return null},e.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},e.prototype.toPrettyString=function(e){void 0===e&&(e="");var t=e+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(t+="+"),t+=this.length,this.tag.tagConstructed?t+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(t+=" (encapsulates)"),t+="\n",null!==this.sub){e+="  ";for(var n=0,r=this.sub.length;n<r;++n)t+=this.sub[n].toPrettyString(e)}return t},e.prototype.posStart=function(){return this.stream.pos},e.prototype.posContent=function(){return this.stream.pos+this.header},e.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},e.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},e.decodeLength=function(e){var t=e.get(),n=127&t;if(n==t)return n;if(n>6)throw new Error("Length over 48 bits not supported at position "+(e.pos-1));if(0===n)return null;t=0;for(var r=0;r<n;++r)t=256*t+e.get();return t},e.prototype.getHexStringValue=function(){var e=this.toHexString(),t=2*this.header,n=2*this.length;return e.substr(t,n)},e.decode=function(t){var n;n=t instanceof F?t:new F(t,0);var r=new F(n),i=new H(n),a=e.decodeLength(n),s=n.pos,o=s-r.pos,l=null,c=function(){var t=[];if(null!==a){for(var r=s+a;n.pos<r;)t[t.length]=e.decode(n);if(n.pos!=r)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var i=e.decode(n);if(i.tag.isEOC())break;t[t.length]=i}a=s-n.pos}catch(o){throw new Error("Exception while decoding undefined length content: "+o)}return t};if(i.tagConstructed)l=c();else if(i.isUniversal()&&(3==i.tagNumber||4==i.tagNumber))try{if(3==i.tagNumber&&0!=n.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");l=c();for(var u=0;u<l.length;++u)if(l[u].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(d){l=null}if(null===l){if(null===a)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);n.pos=s+Math.abs(a)}return new e(r,o,a,i,l)},e}(),H=function(){function e(e){var t=e.get();if(this.tagClass=t>>6,this.tagConstructed=0!==(32&t),this.tagNumber=31&t,31==this.tagNumber){var n=new A;do{t=e.get(),n.mulAdd(128,127&t)}while(128&t);this.tagNumber=n.simplify()}}return e.prototype.isUniversal=function(){return 0===this.tagClass},e.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},e}(),U=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],q=(1<<26)/U[U.length-1],W=function(){function e(e,t,n){null!=e&&("number"==typeof e?this.fromNumber(e,t,n):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}return e.prototype.toString=function(e){if(this.s<0)return"-"+this.negate().toString(e);var t;if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var n,r=(1<<t)-1,i=!1,a="",s=this.t,o=this.DB-s*this.DB%t;if(s-- >0)for(o<this.DB&&(n=this[s]>>o)>0&&(i=!0,a=x(n));s>=0;)o<t?(n=(this[s]&(1<<o)-1)<<t-o,n|=this[--s]>>(o+=this.DB-t)):(n=this[s]>>(o-=t)&r,o<=0&&(o+=this.DB,--s)),n>0&&(i=!0),i&&(a+=x(n));return i?a:"0"},e.prototype.negate=function(){var t=G();return e.ZERO.subTo(this,t),t},e.prototype.abs=function(){return this.s<0?this.negate():this},e.prototype.compareTo=function(e){var t=this.s-e.s;if(0!=t)return t;var n=this.t;if(0!=(t=n-e.t))return this.s<0?-t:t;for(;--n>=0;)if(0!=(t=this[n]-e[n]))return t;return 0},e.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+ie(this[this.t-1]^this.s&this.DM)},e.prototype.mod=function(t){var n=G();return this.abs().divRemTo(t,null,n),this.s<0&&n.compareTo(e.ZERO)>0&&t.subTo(n,n),n},e.prototype.modPowInt=function(e,t){var n;return n=e<256||t.isEven()?new $(t):new Q(t),this.exp(e,n)},e.prototype.clone=function(){var e=G();return this.copyTo(e),e},e.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},e.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},e.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},e.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},e.prototype.toByteArray=function(){var e=this.t,t=[];t[0]=this.s;var n,r=this.DB-e*this.DB%8,i=0;if(e-- >0)for(r<this.DB&&(n=this[e]>>r)!=(this.s&this.DM)>>r&&(t[i++]=n|this.s<<this.DB-r);e>=0;)r<8?(n=(this[e]&(1<<r)-1)<<8-r,n|=this[--e]>>(r+=this.DB-8)):(n=this[e]>>(r-=8)&255,r<=0&&(r+=this.DB,--e)),0!=(128&n)&&(n|=-256),0==i&&(128&this.s)!=(128&n)&&++i,(i>0||n!=this.s)&&(t[i++]=n);return t},e.prototype.equals=function(e){return 0==this.compareTo(e)},e.prototype.min=function(e){return this.compareTo(e)<0?this:e},e.prototype.max=function(e){return this.compareTo(e)>0?this:e},e.prototype.and=function(e){var t=G();return this.bitwiseTo(e,w,t),t},e.prototype.or=function(e){var t=G();return this.bitwiseTo(e,S,t),t},e.prototype.xor=function(e){var t=G();return this.bitwiseTo(e,k,t),t},e.prototype.andNot=function(e){var t=G();return this.bitwiseTo(e,N,t),t},e.prototype.not=function(){for(var e=G(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e},e.prototype.shiftLeft=function(e){var t=G();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t},e.prototype.shiftRight=function(e){var t=G();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t},e.prototype.getLowestSetBit=function(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+j(this[e]);return this.s<0?this.t*this.DB:-1},e.prototype.bitCount=function(){for(var e=0,t=this.s&this.DM,n=0;n<this.t;++n)e+=E(this[n]^t);return e},e.prototype.testBit=function(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:0!=(this[t]&1<<e%this.DB)},e.prototype.setBit=function(e){return this.changeBit(e,S)},e.prototype.clearBit=function(e){return this.changeBit(e,N)},e.prototype.flipBit=function(e){return this.changeBit(e,k)},e.prototype.add=function(e){var t=G();return this.addTo(e,t),t},e.prototype.subtract=function(e){var t=G();return this.subTo(e,t),t},e.prototype.multiply=function(e){var t=G();return this.multiplyTo(e,t),t},e.prototype.divide=function(e){var t=G();return this.divRemTo(e,t,null),t},e.prototype.remainder=function(e){var t=G();return this.divRemTo(e,null,t),t},e.prototype.divideAndRemainder=function(e){var t=G(),n=G();return this.divRemTo(e,t,n),[t,n]},e.prototype.modPow=function(e,t){var n,r,i=e.bitLength(),a=re(1);if(i<=0)return a;n=i<18?1:i<48?3:i<144?4:i<768?5:6,r=i<8?new $(t):t.isEven()?new J(t):new Q(t);var s=[],o=3,l=n-1,c=(1<<n)-1;if(s[1]=r.convert(this),n>1){var u=G();for(r.sqrTo(s[1],u);o<=c;)s[o]=G(),r.mulTo(u,s[o-2],s[o]),o+=2}var d,f,h=e.t-1,p=!0,m=G();for(i=ie(e[h])-1;h>=0;){for(i>=l?d=e[h]>>i-l&c:(d=(e[h]&(1<<i+1)-1)<<l-i,h>0&&(d|=e[h-1]>>this.DB+i-l)),o=n;0==(1&d);)d>>=1,--o;if((i-=o)<0&&(i+=this.DB,--h),p)s[d].copyTo(a),p=!1;else{for(;o>1;)r.sqrTo(a,m),r.sqrTo(m,a),o-=2;o>0?r.sqrTo(a,m):(f=a,a=m,m=f),r.mulTo(m,s[d],a)}for(;h>=0&&0==(e[h]&1<<i);)r.sqrTo(a,m),f=a,a=m,m=f,--i<0&&(i=this.DB-1,--h)}return r.revert(a)},e.prototype.modInverse=function(t){var n=t.isEven();if(this.isEven()&&n||0==t.signum())return e.ZERO;for(var r=t.clone(),i=this.clone(),a=re(1),s=re(0),o=re(0),l=re(1);0!=r.signum();){for(;r.isEven();)r.rShiftTo(1,r),n?(a.isEven()&&s.isEven()||(a.addTo(this,a),s.subTo(t,s)),a.rShiftTo(1,a)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),n?(o.isEven()&&l.isEven()||(o.addTo(this,o),l.subTo(t,l)),o.rShiftTo(1,o)):l.isEven()||l.subTo(t,l),l.rShiftTo(1,l);r.compareTo(i)>=0?(r.subTo(i,r),n&&a.subTo(o,a),s.subTo(l,s)):(i.subTo(r,i),n&&o.subTo(a,o),l.subTo(s,l))}return 0!=i.compareTo(e.ONE)?e.ZERO:l.compareTo(t)>=0?l.subtract(t):l.signum()<0?(l.addTo(t,l),l.signum()<0?l.add(t):l):l},e.prototype.pow=function(e){return this.exp(e,new K)},e.prototype.gcd=function(e){var t=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(t.compareTo(n)<0){var r=t;t=n,n=r}var i=t.getLowestSetBit(),a=n.getLowestSetBit();if(a<0)return t;for(i<a&&(a=i),a>0&&(t.rShiftTo(a,t),n.rShiftTo(a,n));t.signum()>0;)(i=t.getLowestSetBit())>0&&t.rShiftTo(i,t),(i=n.getLowestSetBit())>0&&n.rShiftTo(i,n),t.compareTo(n)>=0?(t.subTo(n,t),t.rShiftTo(1,t)):(n.subTo(t,n),n.rShiftTo(1,n));return a>0&&n.lShiftTo(a,n),n},e.prototype.isProbablePrime=function(e){var t,n=this.abs();if(1==n.t&&n[0]<=U[U.length-1]){for(t=0;t<U.length;++t)if(n[0]==U[t])return!0;return!1}if(n.isEven())return!1;for(t=1;t<U.length;){for(var r=U[t],i=t+1;i<U.length&&r<q;)r*=U[i++];for(r=n.modInt(r);t<i;)if(r%U[t++]==0)return!1}return n.millerRabin(e)},e.prototype.copyTo=function(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s},e.prototype.fromInt=function(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0},e.prototype.fromString=function(t,n){var r;if(16==n)r=4;else if(8==n)r=3;else if(256==n)r=8;else if(2==n)r=1;else if(32==n)r=5;else{if(4!=n)return void this.fromRadix(t,n);r=2}this.t=0,this.s=0;for(var i=t.length,a=!1,s=0;--i>=0;){var o=8==r?255&+t[i]:ne(t,i);o<0?"-"==t.charAt(i)&&(a=!0):(a=!1,0==s?this[this.t++]=o:s+r>this.DB?(this[this.t-1]|=(o&(1<<this.DB-s)-1)<<s,this[this.t++]=o>>this.DB-s):this[this.t-1]|=o<<s,(s+=r)>=this.DB&&(s-=this.DB))}8==r&&0!=(128&+t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),a&&e.ZERO.subTo(this,this)},e.prototype.clamp=function(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t},e.prototype.dlShiftTo=function(e,t){var n;for(n=this.t-1;n>=0;--n)t[n+e]=this[n];for(n=e-1;n>=0;--n)t[n]=0;t.t=this.t+e,t.s=this.s},e.prototype.drShiftTo=function(e,t){for(var n=e;n<this.t;++n)t[n-e]=this[n];t.t=Math.max(this.t-e,0),t.s=this.s},e.prototype.lShiftTo=function(e,t){for(var n=e%this.DB,r=this.DB-n,i=(1<<r)-1,a=Math.floor(e/this.DB),s=this.s<<n&this.DM,o=this.t-1;o>=0;--o)t[o+a+1]=this[o]>>r|s,s=(this[o]&i)<<n;for(o=a-1;o>=0;--o)t[o]=0;t[a]=s,t.t=this.t+a+1,t.s=this.s,t.clamp()},e.prototype.rShiftTo=function(e,t){t.s=this.s;var n=Math.floor(e/this.DB);if(n>=this.t)t.t=0;else{var r=e%this.DB,i=this.DB-r,a=(1<<r)-1;t[0]=this[n]>>r;for(var s=n+1;s<this.t;++s)t[s-n-1]|=(this[s]&a)<<i,t[s-n]=this[s]>>r;r>0&&(t[this.t-n-1]|=(this.s&a)<<i),t.t=this.t-n,t.clamp()}},e.prototype.subTo=function(e,t){for(var n=0,r=0,i=Math.min(e.t,this.t);n<i;)r+=this[n]-e[n],t[n++]=r&this.DM,r>>=this.DB;if(e.t<this.t){for(r-=e.s;n<this.t;)r+=this[n],t[n++]=r&this.DM,r>>=this.DB;r+=this.s}else{for(r+=this.s;n<e.t;)r-=e[n],t[n++]=r&this.DM,r>>=this.DB;r-=e.s}t.s=r<0?-1:0,r<-1?t[n++]=this.DV+r:r>0&&(t[n++]=r),t.t=n,t.clamp()},e.prototype.multiplyTo=function(t,n){var r=this.abs(),i=t.abs(),a=r.t;for(n.t=a+i.t;--a>=0;)n[a]=0;for(a=0;a<i.t;++a)n[a+r.t]=r.am(0,i[a],n,a,0,r.t);n.s=0,n.clamp(),this.s!=t.s&&e.ZERO.subTo(n,n)},e.prototype.squareTo=function(e){for(var t=this.abs(),n=e.t=2*t.t;--n>=0;)e[n]=0;for(n=0;n<t.t-1;++n){var r=t.am(n,t[n],e,2*n,0,1);(e[n+t.t]+=t.am(n+1,2*t[n],e,2*n+1,r,t.t-n-1))>=t.DV&&(e[n+t.t]-=t.DV,e[n+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(n,t[n],e,2*n,0,1)),e.s=0,e.clamp()},e.prototype.divRemTo=function(t,n,r){var i=t.abs();if(!(i.t<=0)){var a=this.abs();if(a.t<i.t)return null!=n&&n.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=G());var s=G(),o=this.s,l=t.s,c=this.DB-ie(i[i.t-1]);c>0?(i.lShiftTo(c,s),a.lShiftTo(c,r)):(i.copyTo(s),a.copyTo(r));var u=s.t,d=s[u-1];if(0!=d){var f=d*(1<<this.F1)+(u>1?s[u-2]>>this.F2:0),h=this.FV/f,p=(1<<this.F1)/f,m=1<<this.F2,g=r.t,v=g-u,y=null==n?G():n;for(s.dlShiftTo(v,y),r.compareTo(y)>=0&&(r[r.t++]=1,r.subTo(y,r)),e.ONE.dlShiftTo(u,y),y.subTo(s,s);s.t<u;)s[s.t++]=0;for(;--v>=0;){var b=r[--g]==d?this.DM:Math.floor(r[g]*h+(r[g-1]+m)*p);if((r[g]+=s.am(0,b,r,v,0,u))<b)for(s.dlShiftTo(v,y),r.subTo(y,r);r[g]<--b;)r.subTo(y,r)}null!=n&&(r.drShiftTo(u,n),o!=l&&e.ZERO.subTo(n,n)),r.t=u,r.clamp(),c>0&&r.rShiftTo(c,r),o<0&&e.ZERO.subTo(r,r)}}},e.prototype.invDigit=function(){if(this.t<1)return 0;var e=this[0];if(0==(1&e))return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t},e.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},e.prototype.exp=function(t,n){if(t>4294967295||t<1)return e.ONE;var r=G(),i=G(),a=n.convert(this),s=ie(t)-1;for(a.copyTo(r);--s>=0;)if(n.sqrTo(r,i),(t&1<<s)>0)n.mulTo(i,a,r);else{var o=r;r=i,i=o}return n.revert(r)},e.prototype.chunkSize=function(e){return Math.floor(Math.LN2*this.DB/Math.log(e))},e.prototype.toRadix=function(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),n=Math.pow(e,t),r=re(n),i=G(),a=G(),s="";for(this.divRemTo(r,i,a);i.signum()>0;)s=(n+a.intValue()).toString(e).substr(1)+s,i.divRemTo(r,i,a);return a.intValue().toString(e)+s},e.prototype.fromRadix=function(t,n){this.fromInt(0),null==n&&(n=10);for(var r=this.chunkSize(n),i=Math.pow(n,r),a=!1,s=0,o=0,l=0;l<t.length;++l){var c=ne(t,l);c<0?"-"==t.charAt(l)&&0==this.signum()&&(a=!0):(o=n*o+c,++s>=r&&(this.dMultiply(i),this.dAddOffset(o,0),s=0,o=0))}s>0&&(this.dMultiply(Math.pow(n,s)),this.dAddOffset(o,0)),a&&e.ZERO.subTo(this,this)},e.prototype.fromNumber=function(t,n,r){if("number"==typeof n)if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),S,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(n);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(e.ONE.shiftLeft(t-1),this);else{var i=[],a=7&t;i.length=1+(t>>3),n.nextBytes(i),a>0?i[0]&=(1<<a)-1:i[0]=0,this.fromString(i,256)}},e.prototype.bitwiseTo=function(e,t,n){var r,i,a=Math.min(e.t,this.t);for(r=0;r<a;++r)n[r]=t(this[r],e[r]);if(e.t<this.t){for(i=e.s&this.DM,r=a;r<this.t;++r)n[r]=t(this[r],i);n.t=this.t}else{for(i=this.s&this.DM,r=a;r<e.t;++r)n[r]=t(i,e[r]);n.t=e.t}n.s=t(this.s,e.s),n.clamp()},e.prototype.changeBit=function(t,n){var r=e.ONE.shiftLeft(t);return this.bitwiseTo(r,n,r),r},e.prototype.addTo=function(e,t){for(var n=0,r=0,i=Math.min(e.t,this.t);n<i;)r+=this[n]+e[n],t[n++]=r&this.DM,r>>=this.DB;if(e.t<this.t){for(r+=e.s;n<this.t;)r+=this[n],t[n++]=r&this.DM,r>>=this.DB;r+=this.s}else{for(r+=this.s;n<e.t;)r+=e[n],t[n++]=r&this.DM,r>>=this.DB;r+=e.s}t.s=r<0?-1:0,r>0?t[n++]=r:r<-1&&(t[n++]=this.DV+r),t.t=n,t.clamp()},e.prototype.dMultiply=function(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()},e.prototype.dAddOffset=function(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}},e.prototype.multiplyLowerTo=function(e,t,n){var r=Math.min(this.t+e.t,t);for(n.s=0,n.t=r;r>0;)n[--r]=0;for(var i=n.t-this.t;r<i;++r)n[r+this.t]=this.am(0,e[r],n,r,0,this.t);for(i=Math.min(e.t,t);r<i;++r)this.am(0,e[r],n,r,0,t-r);n.clamp()},e.prototype.multiplyUpperTo=function(e,t,n){--t;var r=n.t=this.t+e.t-t;for(n.s=0;--r>=0;)n[r]=0;for(r=Math.max(t-this.t,0);r<e.t;++r)n[this.t+r-t]=this.am(t-r,e[r],n,0,0,this.t+r-t);n.clamp(),n.drShiftTo(1,n)},e.prototype.modInt=function(e){if(e<=0)return 0;var t=this.DV%e,n=this.s<0?e-1:0;if(this.t>0)if(0==t)n=this[0]%e;else for(var r=this.t-1;r>=0;--r)n=(t*n+this[r])%e;return n},e.prototype.millerRabin=function(t){var n=this.subtract(e.ONE),r=n.getLowestSetBit();if(r<=0)return!1;var i=n.shiftRight(r);(t=t+1>>1)>U.length&&(t=U.length);for(var a=G(),s=0;s<t;++s){a.fromInt(U[Math.floor(Math.random()*U.length)]);var o=a.modPow(i,this);if(0!=o.compareTo(e.ONE)&&0!=o.compareTo(n)){for(var l=1;l++<r&&0!=o.compareTo(n);)if(0==(o=o.modPowInt(2,this)).compareTo(e.ONE))return!1;if(0!=o.compareTo(n))return!1}}return!0},e.prototype.square=function(){var e=G();return this.squareTo(e),e},e.prototype.gcda=function(e,t){var n=this.s<0?this.negate():this.clone(),r=e.s<0?e.negate():e.clone();if(n.compareTo(r)<0){var i=n;n=r,r=i}var a=n.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)t(n);else{a<s&&(s=a),s>0&&(n.rShiftTo(s,n),r.rShiftTo(s,r));var o=function(){(a=n.getLowestSetBit())>0&&n.rShiftTo(a,n),(a=r.getLowestSetBit())>0&&r.rShiftTo(a,r),n.compareTo(r)>=0?(n.subTo(r,n),n.rShiftTo(1,n)):(r.subTo(n,r),r.rShiftTo(1,r)),n.signum()>0?setTimeout(o,0):(s>0&&r.lShiftTo(s,r),setTimeout(function(){t(r)},0))};setTimeout(o,10)}},e.prototype.fromNumberAsync=function(t,n,r,i){if("number"==typeof n)if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),S,this),this.isEven()&&this.dAddOffset(1,0);var a=this,s=function(){a.dAddOffset(2,0),a.bitLength()>t&&a.subTo(e.ONE.shiftLeft(t-1),a),a.isProbablePrime(n)?setTimeout(function(){i()},0):setTimeout(s,0)};setTimeout(s,0)}else{var o=[],l=7&t;o.length=1+(t>>3),n.nextBytes(o),l>0?o[0]&=(1<<l)-1:o[0]=0,this.fromString(o,256)}},e}(),K=function(){function e(){}return e.prototype.convert=function(e){return e},e.prototype.revert=function(e){return e},e.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n)},e.prototype.sqrTo=function(e,t){e.squareTo(t)},e}(),$=function(){function e(e){this.m=e}return e.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},e.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),Q=function(){function e(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}return e.prototype.convert=function(e){var t=G();return e.abs().dlShiftTo(this.m.t,t),t.divRemTo(this.m,null,t),e.s<0&&t.compareTo(W.ZERO)>0&&this.m.subTo(t,t),t},e.prototype.revert=function(e){var t=G();return e.copyTo(t),this.reduce(t),t},e.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var n=32767&e[t],r=n*this.mpl+((n*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(e[n=t+this.m.t]+=this.m.am(0,r,e,t,0,this.m.t);e[n]>=e.DV;)e[n]-=e.DV,e[++n]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}(),J=function(){function e(e){this.m=e,this.r2=G(),this.q3=G(),W.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e)}return e.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(e.compareTo(this.m)<0)return e;var t=G();return e.copyTo(t),this.reduce(t),t},e.prototype.revert=function(e){return e},e.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},e.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},e.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},e}();function G(){return new W(null)}function X(e,t){return new W(e,t)}var Z="undefined"!==typeof navigator;Z&&"Microsoft Internet Explorer"==navigator.appName?(W.prototype.am=function(e,t,n,r,i,a){for(var s=32767&t,o=t>>15;--a>=0;){var l=32767&this[e],c=this[e++]>>15,u=o*l+c*s;i=((l=s*l+((32767&u)<<15)+n[r]+(1073741823&i))>>>30)+(u>>>15)+o*c+(i>>>30),n[r++]=1073741823&l}return i},I=30):Z&&"Netscape"!=navigator.appName?(W.prototype.am=function(e,t,n,r,i,a){for(;--a>=0;){var s=t*this[e++]+n[r]+i;i=Math.floor(s/67108864),n[r++]=67108863&s}return i},I=26):(W.prototype.am=function(e,t,n,r,i,a){for(var s=16383&t,o=t>>14;--a>=0;){var l=16383&this[e],c=this[e++]>>14,u=o*l+c*s;i=((l=s*l+((16383&u)<<14)+n[r]+i)>>28)+(u>>14)+o*c,n[r++]=268435455&l}return i},I=28),W.prototype.DB=I,W.prototype.DM=(1<<I)-1,W.prototype.DV=1<<I;W.prototype.FV=Math.pow(2,52),W.prototype.F1=52-I,W.prototype.F2=2*I-52;var Y,ee,te=[];for(Y="0".charCodeAt(0),ee=0;ee<=9;++ee)te[Y++]=ee;for(Y="a".charCodeAt(0),ee=10;ee<36;++ee)te[Y++]=ee;for(Y="A".charCodeAt(0),ee=10;ee<36;++ee)te[Y++]=ee;function ne(e,t){var n=te[e.charCodeAt(t)];return null==n?-1:n}function re(e){var t=G();return t.fromInt(e),t}function ie(e){var t,n=1;return 0!=(t=e>>>16)&&(e=t,n+=16),0!=(t=e>>8)&&(e=t,n+=8),0!=(t=e>>4)&&(e=t,n+=4),0!=(t=e>>2)&&(e=t,n+=2),0!=(t=e>>1)&&(e=t,n+=1),n}W.ZERO=re(0),W.ONE=re(1);var ae=function(){function e(){this.i=0,this.j=0,this.S=[]}return e.prototype.init=function(e){var t,n,r;for(t=0;t<256;++t)this.S[t]=t;for(n=0,t=0;t<256;++t)n=n+this.S[t]+e[t%e.length]&255,r=this.S[t],this.S[t]=this.S[n],this.S[n]=r;this.i=0,this.j=0},e.prototype.next=function(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]},e}();var se,oe,le=null;if(null==le){le=[],oe=0;var ce=void 0;if("undefined"!==typeof window&&window.crypto&&window.crypto.getRandomValues){var ue=new Uint32Array(256);for(window.crypto.getRandomValues(ue),ce=0;ce<ue.length;++ce)le[oe++]=255&ue[ce]}var de=0,fe=function(e){if((de=de||0)>=256||oe>=256)window.removeEventListener?window.removeEventListener("mousemove",fe,!1):window.detachEvent&&window.detachEvent("onmousemove",fe);else try{var t=e.x+e.y;le[oe++]=255&t,de+=1}catch(n){}};"undefined"!==typeof window&&(window.addEventListener?window.addEventListener("mousemove",fe,!1):window.attachEvent&&window.attachEvent("onmousemove",fe))}function he(){if(null==se){for(se=new ae;oe<256;){var e=Math.floor(65536*Math.random());le[oe++]=255&e}for(se.init(le),oe=0;oe<le.length;++oe)le[oe]=0;oe=0}return se.next()}var pe=function(){function e(){}return e.prototype.nextBytes=function(e){for(var t=0;t<e.length;++t)e[t]=he()},e}();var me=function(){function e(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return e.prototype.doPublic=function(e){return e.modPowInt(this.e,this.n)},e.prototype.doPrivate=function(e){if(null==this.p||null==this.q)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),n=e.mod(this.q).modPow(this.dmq1,this.q);t.compareTo(n)<0;)t=t.add(this.p);return t.subtract(n).multiply(this.coeff).mod(this.p).multiply(this.q).add(n)},e.prototype.setPublic=function(e,t){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=X(e,16),this.e=parseInt(t,16)):console.error("Invalid RSA public key")},e.prototype.encrypt=function(e){var t=this.n.bitLength()+7>>3,n=function(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var n=[],r=e.length-1;r>=0&&t>0;){var i=e.charCodeAt(r--);i<128?n[--t]=i:i>127&&i<2048?(n[--t]=63&i|128,n[--t]=i>>6|192):(n[--t]=63&i|128,n[--t]=i>>6&63|128,n[--t]=i>>12|224)}n[--t]=0;for(var a=new pe,s=[];t>2;){for(s[0]=0;0==s[0];)a.nextBytes(s);n[--t]=s[0]}return n[--t]=2,n[--t]=0,new W(n)}(e,t);if(null==n)return null;var r=this.doPublic(n);if(null==r)return null;for(var i=r.toString(16),a=i.length,s=0;s<2*t-a;s++)i="0"+i;return i},e.prototype.setPrivate=function(e,t,n){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=X(e,16),this.e=parseInt(t,16),this.d=X(n,16)):console.error("Invalid RSA private key")},e.prototype.setPrivateEx=function(e,t,n,r,i,a,s,o){null!=e&&null!=t&&e.length>0&&t.length>0?(this.n=X(e,16),this.e=parseInt(t,16),this.d=X(n,16),this.p=X(r,16),this.q=X(i,16),this.dmp1=X(a,16),this.dmq1=X(s,16),this.coeff=X(o,16)):console.error("Invalid RSA private key")},e.prototype.generate=function(e,t){var n=new pe,r=e>>1;this.e=parseInt(t,16);for(var i=new W(t,16);;){for(;this.p=new W(e-r,1,n),0!=this.p.subtract(W.ONE).gcd(i).compareTo(W.ONE)||!this.p.isProbablePrime(10););for(;this.q=new W(r,1,n),0!=this.q.subtract(W.ONE).gcd(i).compareTo(W.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var a=this.p;this.p=this.q,this.q=a}var s=this.p.subtract(W.ONE),o=this.q.subtract(W.ONE),l=s.multiply(o);if(0==l.gcd(i).compareTo(W.ONE)){this.n=this.p.multiply(this.q),this.d=i.modInverse(l),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(o),this.coeff=this.q.modInverse(this.p);break}}},e.prototype.decrypt=function(e){var t=X(e,16),n=this.doPrivate(t);return null==n?null:function(e,t){var n=e.toByteArray(),r=0;for(;r<n.length&&0==n[r];)++r;if(n.length-r!=t-1||2!=n[r])return null;++r;for(;0!=n[r];)if(++r>=n.length)return null;var i="";for(;++r<n.length;){var a=255&n[r];a<128?i+=String.fromCharCode(a):a>191&&a<224?(i+=String.fromCharCode((31&a)<<6|63&n[r+1]),++r):(i+=String.fromCharCode((15&a)<<12|(63&n[r+1])<<6|63&n[r+2]),r+=2)}return i}(n,this.n.bitLength()+7>>3)},e.prototype.generateAsync=function(e,t,n){var r=new pe,i=e>>1;this.e=parseInt(t,16);var a=new W(t,16),s=this,o=function(){var t=function(){if(s.p.compareTo(s.q)<=0){var e=s.p;s.p=s.q,s.q=e}var t=s.p.subtract(W.ONE),r=s.q.subtract(W.ONE),i=t.multiply(r);0==i.gcd(a).compareTo(W.ONE)?(s.n=s.p.multiply(s.q),s.d=a.modInverse(i),s.dmp1=s.d.mod(t),s.dmq1=s.d.mod(r),s.coeff=s.q.modInverse(s.p),setTimeout(function(){n()},0)):setTimeout(o,0)},l=function(){s.q=G(),s.q.fromNumberAsync(i,1,r,function(){s.q.subtract(W.ONE).gcda(a,function(e){0==e.compareTo(W.ONE)&&s.q.isProbablePrime(10)?setTimeout(t,0):setTimeout(l,0)})})},c=function(){s.p=G(),s.p.fromNumberAsync(e-i,1,r,function(){s.p.subtract(W.ONE).gcda(a,function(e){0==e.compareTo(W.ONE)&&s.p.isProbablePrime(10)?setTimeout(l,0):setTimeout(c,0)})})};setTimeout(c,0)};setTimeout(o,0)},e.prototype.sign=function(e,t,n){var r=function(e,t){if(t<e.length+22)return console.error("Message too long for RSA"),null;for(var n=t-e.length-6,r="",i=0;i<n;i+=2)r+="ff";return X("0001"+r+"00"+e,16)}((ge[n]||"")+t(e).toString(),this.n.bitLength()/4);if(null==r)return null;var i=this.doPrivate(r);if(null==i)return null;var a=i.toString(16);return 0==(1&a.length)?a:"0"+a},e.prototype.verify=function(e,t,n){var r=X(t,16),i=this.doPublic(r);return null==i?null:function(e){for(var t in ge)if(ge.hasOwnProperty(t)){var n=ge[t],r=n.length;if(e.substr(0,r)==n)return e.substr(r)}return e}(i.toString(16).replace(/^1f+00/,""))==n(e).toString()},e}();var ge={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var ve={};ve.lang={extend:function(e,t,n){if(!t||!e)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var r=function(){};if(r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e,e.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),n){var i;for(i in n)e.prototype[i]=n[i];var a=function(){},s=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(a=function(e,t){for(i=0;i<s.length;i+=1){var n=s[i],r=t[n];"function"===typeof r&&r!=Object.prototype[n]&&(e[n]=r)}})}catch(o){}a(e.prototype,n)}}};var ye={};"undefined"!=typeof ye.asn1&&ye.asn1||(ye.asn1={}),ye.asn1.ASN1Util=new function(){this.integerToByteHex=function(e){var t=e.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(e){var t=e.toString(16);if("-"!=t.substr(0,1))t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var n=t.substr(1).length;n%2==1?n+=1:t.match(/^[0-7]/)||(n+=2);for(var r="",i=0;i<n;i++)r+="f";t=new W(r,16).xor(e).add(W.ONE).toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(e,t){return hextopem(e,t)},this.newObject=function(e){var t=ye.asn1,n=t.DERBoolean,r=t.DERInteger,i=t.DERBitString,a=t.DEROctetString,s=t.DERNull,o=t.DERObjectIdentifier,l=t.DEREnumerated,c=t.DERUTF8String,u=t.DERNumericString,d=t.DERPrintableString,f=t.DERTeletexString,h=t.DERIA5String,p=t.DERUTCTime,m=t.DERGeneralizedTime,g=t.DERSequence,v=t.DERSet,y=t.DERTaggedObject,b=t.ASN1Util.newObject,x=Object.keys(e);if(1!=x.length)throw"key of param shall be only one.";var w=x[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+w+":"))throw"undefined key: "+w;if("bool"==w)return new n(e[w]);if("int"==w)return new r(e[w]);if("bitstr"==w)return new i(e[w]);if("octstr"==w)return new a(e[w]);if("null"==w)return new s(e[w]);if("oid"==w)return new o(e[w]);if("enum"==w)return new l(e[w]);if("utf8str"==w)return new c(e[w]);if("numstr"==w)return new u(e[w]);if("prnstr"==w)return new d(e[w]);if("telstr"==w)return new f(e[w]);if("ia5str"==w)return new h(e[w]);if("utctime"==w)return new p(e[w]);if("gentime"==w)return new m(e[w]);if("seq"==w){for(var S=e[w],k=[],N=0;N<S.length;N++){var j=b(S[N]);k.push(j)}return new g({array:k})}if("set"==w){for(S=e[w],k=[],N=0;N<S.length;N++){j=b(S[N]);k.push(j)}return new v({array:k})}if("tag"==w){var E=e[w];if("[object Array]"===Object.prototype.toString.call(E)&&3==E.length){var _=b(E[2]);return new y({tag:E[0],explicit:E[1],obj:_})}var C={};if(void 0!==E.explicit&&(C.explicit=E.explicit),void 0!==E.tag&&(C.tag=E.tag),void 0===E.obj)throw"obj shall be specified for 'tag'.";return C.obj=b(E.obj),new y(C)}},this.jsonToASN1HEX=function(e){return this.newObject(e).getEncodedHex()}},ye.asn1.ASN1Util.oidHexToInt=function(e){for(var t="",n=parseInt(e.substr(0,2),16),r=(t=Math.floor(n/40)+"."+n%40,""),i=2;i<e.length;i+=2){var a=("00000000"+parseInt(e.substr(i,2),16).toString(2)).slice(-8);if(r+=a.substr(1,7),"0"==a.substr(0,1))t=t+"."+new W(r,2).toString(10),r=""}return t},ye.asn1.ASN1Util.oidIntToHex=function(e){var t=function(e){var t=e.toString(16);return 1==t.length&&(t="0"+t),t},n=function(e){var n="",r=new W(e,10).toString(2),i=7-r.length%7;7==i&&(i=0);for(var a="",s=0;s<i;s++)a+="0";r=a+r;for(s=0;s<r.length-1;s+=7){var o=r.substr(s,7);s!=r.length-7&&(o="1"+o),n+=t(parseInt(o,2))}return n};if(!e.match(/^[0-9.]+$/))throw"malformed oid string: "+e;var r="",i=e.split("."),a=40*parseInt(i[0])+parseInt(i[1]);r+=t(a),i.splice(0,2);for(var s=0;s<i.length;s++)r+=n(i[s]);return r},ye.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if("undefined"==typeof this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var e=this.hV.length/2,t=e.toString(16);if(t.length%2==1&&(t="0"+t),e<128)return t;var n=t.length/2;if(n>15)throw"ASN.1 length too long to represent by 8x: n = "+e.toString(16);return(128+n).toString(16)+t},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},ye.asn1.DERAbstractString=function(e){ye.asn1.DERAbstractString.superclass.constructor.call(this);this.getString=function(){return this.s},this.setString=function(e){this.hTLV=null,this.isModified=!0,this.s=e,this.hV=stohex(this.s)},this.setStringHex=function(e){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=e},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof e&&("string"==typeof e?this.setString(e):"undefined"!=typeof e.str?this.setString(e.str):"undefined"!=typeof e.hex&&this.setStringHex(e.hex))},ve.lang.extend(ye.asn1.DERAbstractString,ye.asn1.ASN1Object),ye.asn1.DERAbstractTime=function(e){ye.asn1.DERAbstractTime.superclass.constructor.call(this);this.localDateToUTC=function(e){return utc=e.getTime()+6e4*e.getTimezoneOffset(),new Date(utc)},this.formatDate=function(e,t,n){var r=this.zeroPadding,i=this.localDateToUTC(e),a=String(i.getFullYear());"utc"==t&&(a=a.substr(2,2));var s=a+r(String(i.getMonth()+1),2)+r(String(i.getDate()),2)+r(String(i.getHours()),2)+r(String(i.getMinutes()),2)+r(String(i.getSeconds()),2);if(!0===n){var o=i.getMilliseconds();if(0!=o){var l=r(String(o),3);s=s+"."+(l=l.replace(/[0]+$/,""))}}return s+"Z"},this.zeroPadding=function(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e},this.getString=function(){return this.s},this.setString=function(e){this.hTLV=null,this.isModified=!0,this.s=e,this.hV=stohex(e)},this.setByDateValue=function(e,t,n,r,i,a){var s=new Date(Date.UTC(e,t-1,n,r,i,a,0));this.setByDate(s)},this.getFreshValueHex=function(){return this.hV}},ve.lang.extend(ye.asn1.DERAbstractTime,ye.asn1.ASN1Object),ye.asn1.DERAbstractStructured=function(e){ye.asn1.DERAbstractString.superclass.constructor.call(this);this.setByASN1ObjectArray=function(e){this.hTLV=null,this.isModified=!0,this.asn1Array=e},this.appendASN1Object=function(e){this.hTLV=null,this.isModified=!0,this.asn1Array.push(e)},this.asn1Array=new Array,"undefined"!=typeof e&&"undefined"!=typeof e.array&&(this.asn1Array=e.array)},ve.lang.extend(ye.asn1.DERAbstractStructured,ye.asn1.ASN1Object),ye.asn1.DERBoolean=function(){ye.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},ve.lang.extend(ye.asn1.DERBoolean,ye.asn1.ASN1Object),ye.asn1.DERInteger=function(e){ye.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(e){this.hTLV=null,this.isModified=!0,this.hV=ye.asn1.ASN1Util.bigIntToMinTwosComplementsHex(e)},this.setByInteger=function(e){var t=new W(String(e),10);this.setByBigInteger(t)},this.setValueHex=function(e){this.hV=e},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof e&&("undefined"!=typeof e.bigint?this.setByBigInteger(e.bigint):"undefined"!=typeof e.int?this.setByInteger(e.int):"number"==typeof e?this.setByInteger(e):"undefined"!=typeof e.hex&&this.setValueHex(e.hex))},ve.lang.extend(ye.asn1.DERInteger,ye.asn1.ASN1Object),ye.asn1.DERBitString=function(e){if(void 0!==e&&"undefined"!==typeof e.obj){var t=ye.asn1.ASN1Util.newObject(e.obj);e.hex="00"+t.getEncodedHex()}ye.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,t){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+t},this.setByBinaryString=function(e){var t=8-(e=e.replace(/0+$/,"")).length%8;8==t&&(t=0);for(var n=0;n<=t;n++)e+="0";var r="";for(n=0;n<e.length-1;n+=8){var i=e.substr(n,8),a=parseInt(i,2).toString(16);1==a.length&&(a="0"+a),r+=a}this.hTLV=null,this.isModified=!0,this.hV="0"+t+r},this.setByBooleanArray=function(e){for(var t="",n=0;n<e.length;n++)1==e[n]?t+="1":t+="0";this.setByBinaryString(t)},this.newFalseArray=function(e){for(var t=new Array(e),n=0;n<e;n++)t[n]=!1;return t},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof e&&("string"==typeof e&&e.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(e):"undefined"!=typeof e.hex?this.setHexValueIncludingUnusedBits(e.hex):"undefined"!=typeof e.bin?this.setByBinaryString(e.bin):"undefined"!=typeof e.array&&this.setByBooleanArray(e.array))},ve.lang.extend(ye.asn1.DERBitString,ye.asn1.ASN1Object),ye.asn1.DEROctetString=function(e){if(void 0!==e&&"undefined"!==typeof e.obj){var t=ye.asn1.ASN1Util.newObject(e.obj);e.hex=t.getEncodedHex()}ye.asn1.DEROctetString.superclass.constructor.call(this,e),this.hT="04"},ve.lang.extend(ye.asn1.DEROctetString,ye.asn1.DERAbstractString),ye.asn1.DERNull=function(){ye.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},ve.lang.extend(ye.asn1.DERNull,ye.asn1.ASN1Object),ye.asn1.DERObjectIdentifier=function(e){var t=function(e){var t=e.toString(16);return 1==t.length&&(t="0"+t),t},n=function(e){var n="",r=new W(e,10).toString(2),i=7-r.length%7;7==i&&(i=0);for(var a="",s=0;s<i;s++)a+="0";r=a+r;for(s=0;s<r.length-1;s+=7){var o=r.substr(s,7);s!=r.length-7&&(o="1"+o),n+=t(parseInt(o,2))}return n};ye.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(e){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=e},this.setValueOidString=function(e){if(!e.match(/^[0-9.]+$/))throw"malformed oid string: "+e;var r="",i=e.split("."),a=40*parseInt(i[0])+parseInt(i[1]);r+=t(a),i.splice(0,2);for(var s=0;s<i.length;s++)r+=n(i[s]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=r},this.setValueName=function(e){var t=ye.asn1.x509.OID.name2oid(e);if(""===t)throw"DERObjectIdentifier oidName undefined: "+e;this.setValueOidString(t)},this.getFreshValueHex=function(){return this.hV},void 0!==e&&("string"===typeof e?e.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(e):this.setValueName(e):void 0!==e.oid?this.setValueOidString(e.oid):void 0!==e.hex?this.setValueHex(e.hex):void 0!==e.name&&this.setValueName(e.name))},ve.lang.extend(ye.asn1.DERObjectIdentifier,ye.asn1.ASN1Object),ye.asn1.DEREnumerated=function(e){ye.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(e){this.hTLV=null,this.isModified=!0,this.hV=ye.asn1.ASN1Util.bigIntToMinTwosComplementsHex(e)},this.setByInteger=function(e){var t=new W(String(e),10);this.setByBigInteger(t)},this.setValueHex=function(e){this.hV=e},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof e&&("undefined"!=typeof e.int?this.setByInteger(e.int):"number"==typeof e?this.setByInteger(e):"undefined"!=typeof e.hex&&this.setValueHex(e.hex))},ve.lang.extend(ye.asn1.DEREnumerated,ye.asn1.ASN1Object),ye.asn1.DERUTF8String=function(e){ye.asn1.DERUTF8String.superclass.constructor.call(this,e),this.hT="0c"},ve.lang.extend(ye.asn1.DERUTF8String,ye.asn1.DERAbstractString),ye.asn1.DERNumericString=function(e){ye.asn1.DERNumericString.superclass.constructor.call(this,e),this.hT="12"},ve.lang.extend(ye.asn1.DERNumericString,ye.asn1.DERAbstractString),ye.asn1.DERPrintableString=function(e){ye.asn1.DERPrintableString.superclass.constructor.call(this,e),this.hT="13"},ve.lang.extend(ye.asn1.DERPrintableString,ye.asn1.DERAbstractString),ye.asn1.DERTeletexString=function(e){ye.asn1.DERTeletexString.superclass.constructor.call(this,e),this.hT="14"},ve.lang.extend(ye.asn1.DERTeletexString,ye.asn1.DERAbstractString),ye.asn1.DERIA5String=function(e){ye.asn1.DERIA5String.superclass.constructor.call(this,e),this.hT="16"},ve.lang.extend(ye.asn1.DERIA5String,ye.asn1.DERAbstractString),ye.asn1.DERUTCTime=function(e){ye.asn1.DERUTCTime.superclass.constructor.call(this,e),this.hT="17",this.setByDate=function(e){this.hTLV=null,this.isModified=!0,this.date=e,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return"undefined"==typeof this.date&&"undefined"==typeof this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==e&&(void 0!==e.str?this.setString(e.str):"string"==typeof e&&e.match(/^[0-9]{12}Z$/)?this.setString(e):void 0!==e.hex?this.setStringHex(e.hex):void 0!==e.date&&this.setByDate(e.date))},ve.lang.extend(ye.asn1.DERUTCTime,ye.asn1.DERAbstractTime),ye.asn1.DERGeneralizedTime=function(e){ye.asn1.DERGeneralizedTime.superclass.constructor.call(this,e),this.hT="18",this.withMillis=!1,this.setByDate=function(e){this.hTLV=null,this.isModified=!0,this.date=e,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==e&&(void 0!==e.str?this.setString(e.str):"string"==typeof e&&e.match(/^[0-9]{14}Z$/)?this.setString(e):void 0!==e.hex?this.setStringHex(e.hex):void 0!==e.date&&this.setByDate(e.date),!0===e.millis&&(this.withMillis=!0))},ve.lang.extend(ye.asn1.DERGeneralizedTime,ye.asn1.DERAbstractTime),ye.asn1.DERSequence=function(e){ye.asn1.DERSequence.superclass.constructor.call(this,e),this.hT="30",this.getFreshValueHex=function(){for(var e="",t=0;t<this.asn1Array.length;t++){e+=this.asn1Array[t].getEncodedHex()}return this.hV=e,this.hV}},ve.lang.extend(ye.asn1.DERSequence,ye.asn1.DERAbstractStructured),ye.asn1.DERSet=function(e){ye.asn1.DERSet.superclass.constructor.call(this,e),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var e=new Array,t=0;t<this.asn1Array.length;t++){var n=this.asn1Array[t];e.push(n.getEncodedHex())}return 1==this.sortFlag&&e.sort(),this.hV=e.join(""),this.hV},"undefined"!=typeof e&&"undefined"!=typeof e.sortflag&&0==e.sortflag&&(this.sortFlag=!1)},ve.lang.extend(ye.asn1.DERSet,ye.asn1.DERAbstractStructured),ye.asn1.DERTaggedObject=function(e){ye.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(e,t,n){this.hT=t,this.isExplicit=e,this.asn1Object=n,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=n.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,t),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},"undefined"!=typeof e&&("undefined"!=typeof e.tag&&(this.hT=e.tag),"undefined"!=typeof e.explicit&&(this.isExplicit=e.explicit),"undefined"!=typeof e.obj&&(this.asn1Object=e.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},ve.lang.extend(ye.asn1.DERTaggedObject,ye.asn1.ASN1Object);var be,xe=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),we=function(e){function t(n){var r=e.call(this)||this;return n&&("string"===typeof n?r.parseKey(n):(t.hasPrivateKeyProperty(n)||t.hasPublicKeyProperty(n))&&r.parsePropertiesFrom(n)),r}return xe(t,e),t.prototype.parseKey=function(e){try{var t=0,n=0,r=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?R(e):P.unarmor(e),i=V.decode(r);if(3===i.sub.length&&(i=i.sub[2].sub[0]),9===i.sub.length){t=i.sub[1].getHexStringValue(),this.n=X(t,16),n=i.sub[2].getHexStringValue(),this.e=parseInt(n,16);var a=i.sub[3].getHexStringValue();this.d=X(a,16);var s=i.sub[4].getHexStringValue();this.p=X(s,16);var o=i.sub[5].getHexStringValue();this.q=X(o,16);var l=i.sub[6].getHexStringValue();this.dmp1=X(l,16);var c=i.sub[7].getHexStringValue();this.dmq1=X(c,16);var u=i.sub[8].getHexStringValue();this.coeff=X(u,16)}else{if(2!==i.sub.length)return!1;if(i.sub[0].sub){var d=i.sub[1].sub[0];t=d.sub[0].getHexStringValue(),this.n=X(t,16),n=d.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else t=i.sub[0].getHexStringValue(),this.n=X(t,16),n=i.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch(f){return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new ye.asn1.DERInteger({int:0}),new ye.asn1.DERInteger({bigint:this.n}),new ye.asn1.DERInteger({int:this.e}),new ye.asn1.DERInteger({bigint:this.d}),new ye.asn1.DERInteger({bigint:this.p}),new ye.asn1.DERInteger({bigint:this.q}),new ye.asn1.DERInteger({bigint:this.dmp1}),new ye.asn1.DERInteger({bigint:this.dmq1}),new ye.asn1.DERInteger({bigint:this.coeff})]};return new ye.asn1.DERSequence(e).getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return T(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new ye.asn1.DERSequence({array:[new ye.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new ye.asn1.DERNull]}),t=new ye.asn1.DERSequence({array:[new ye.asn1.DERInteger({bigint:this.n}),new ye.asn1.DERInteger({int:this.e})]}),n=new ye.asn1.DERBitString({hex:"00"+t.getEncodedHex()});return new ye.asn1.DERSequence({array:[e,n]}).getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return T(this.getPublicBaseKey())},t.wordwrap=function(e,t){if(!e)return e;var n="(.{1,"+(t=t||64)+"})( +|$\n?)|(.{1,"+t+"})";return e.match(RegExp(n,"g")).join("\n")},t.prototype.getPrivateKey=function(){var e="-----BEGIN RSA PRIVATE KEY-----\n";return e+=t.wordwrap(this.getPrivateBaseKeyB64())+"\n",e+="-----END RSA PRIVATE KEY-----"},t.prototype.getPublicKey=function(){var e="-----BEGIN PUBLIC KEY-----\n";return e+=t.wordwrap(this.getPublicBaseKeyB64())+"\n",e+="-----END PUBLIC KEY-----"},t.hasPublicKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}(me),Se="undefined"!==typeof process?null===(be={NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0})||void 0===be?void 0:be.npm_package_version:void 0;const ke=function(){function e(e){void 0===e&&(e={}),e=e||{},this.default_key_size=e.default_key_size?parseInt(e.default_key_size,10):1024,this.default_public_exponent=e.default_public_exponent||"010001",this.log=e.log||!1,this.key=null}return e.prototype.setKey=function(e){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new we(e)},e.prototype.setPrivateKey=function(e){this.setKey(e)},e.prototype.setPublicKey=function(e){this.setKey(e)},e.prototype.decrypt=function(e){try{return this.getKey().decrypt(B(e))}catch(t){return!1}},e.prototype.encrypt=function(e){try{return T(this.getKey().encrypt(e))}catch(t){return!1}},e.prototype.sign=function(e,t,n){try{return T(this.getKey().sign(e,t,n))}catch(r){return!1}},e.prototype.verify=function(e,t,n){try{return this.getKey().verify(e,B(t),n)}catch(r){return!1}},e.prototype.getKey=function(e){if(!this.key){if(this.key=new we,e&&"[object Function]"==={}.toString.call(e))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,e);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},e.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},e.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},e.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},e.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},e.version=Se,e}(),Ne=()=>{const[t,n]=(0,e.useState)("base64"),[r,a]=(0,e.useState)(""),[s,o]=(0,e.useState)(""),[l,c]=(0,e.useState)(""),u=()=>{try{const e=btoa(unescape(encodeURIComponent(r)));o(e)}catch(e){o("\u7f16\u7801\u5931\u8d25\uff1a"+e.message)}},d=()=>{try{const e=decodeURIComponent(escape(atob(r)));o(e)}catch(e){o("\u89e3\u7801\u5931\u8d25\uff1a"+e.message)}},f=()=>{try{const e=encodeURIComponent(r);o(e)}catch(e){o("\u7f16\u7801\u5931\u8d25\uff1a"+e.message)}},h=()=>{try{const e=decodeURIComponent(r);o(e)}catch(e){o("\u89e3\u7801\u5931\u8d25\uff1a"+e.message)}},p=()=>{const e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},t=r.replace(/[&<>"'\/]/g,t=>e[t]);o(t)},m=()=>{const e={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'","&#x2F;":"/"},t=r.replace(/&amp;|&lt;|&gt;|&quot;|&#39;|&#x2F;/g,t=>e[t]);o(t)},g=()=>{const e=r.split("").map(e=>{const t=e.charCodeAt(0);return t>127?"\\u"+t.toString(16).padStart(4,"0"):e}).join("");o(e)},v=()=>{try{const e=r.replace(/\\u[\dA-F]{4}/gi,e=>String.fromCharCode(parseInt(e.replace(/\\u/g,""),16)));o(e)}catch(e){o("\u89e3\u7801\u5931\u8d25\uff1a"+e.message)}},y=()=>{const e=r.split("").map(e=>e.charCodeAt(0).toString(16).padStart(2,"0")).join(" ");o(e)},x=()=>{try{const e=r.split(" ").map(e=>String.fromCharCode(parseInt(e,16))).join("");o(e)}catch(e){o("\u89e3\u7801\u5931\u8d25\uff1a"+e.message)}},w=()=>{try{const e=b().MD5(r).toString();o(e)}catch(e){o("MD5\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},S=()=>{try{const e=b().SHA1(r).toString();o(e)}catch(e){o("SHA1\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},k=()=>{try{const e=b().SHA256(r).toString();o(e)}catch(e){o("SHA256\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},N=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165\u52a0\u5bc6\u5bc6\u94a5");const e=b().AES.encrypt(r,l).toString();o(e)}catch(e){o("AES\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},j=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165\u89e3\u5bc6\u5bc6\u94a5");const e=b().AES.decrypt(r,l).toString(b().enc.Utf8);if(!e)return void o("\u89e3\u5bc6\u5931\u8d25\uff1a\u5bc6\u94a5\u9519\u8bef\u6216\u6570\u636e\u683c\u5f0f\u4e0d\u6b63\u786e");o(e)}catch(e){o("AES\u89e3\u5bc6\u5931\u8d25\uff1a"+e.message)}},E=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165\u52a0\u5bc6\u5bc6\u94a5");const e=b().DES.encrypt(r,l).toString();o(e)}catch(e){o("DES\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},_=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165\u89e3\u5bc6\u5bc6\u94a5");const e=b().DES.decrypt(r,l).toString(b().enc.Utf8);if(!e)return void o("\u89e3\u5bc6\u5931\u8d25\uff1a\u5bc6\u94a5\u9519\u8bef\u6216\u6570\u636e\u683c\u5f0f\u4e0d\u6b63\u786e");o(e)}catch(e){o("DES\u89e3\u5bc6\u5931\u8d25\uff1a"+e.message)}},C=()=>{try{const e=new ke({default_key_size:2048}),t=e.getPublicKey(),n=e.getPrivateKey();o("\u516c\u94a5\uff1a\n".concat(t,"\n\n\u79c1\u94a5\uff1a\n").concat(n))}catch(e){o("RSA\u5bc6\u94a5\u751f\u6210\u5931\u8d25\uff1a"+e.message)}},T=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165RSA\u516c\u94a5");const e=new ke;e.setPublicKey(l);const t=e.encrypt(r);if(!t)return void o("RSA\u52a0\u5bc6\u5931\u8d25\uff1a\u516c\u94a5\u683c\u5f0f\u9519\u8bef\u6216\u6587\u672c\u8fc7\u957f");o(t)}catch(e){o("RSA\u52a0\u5bc6\u5931\u8d25\uff1a"+e.message)}},B=()=>{try{if(!l)return void o("\u8bf7\u8f93\u5165RSA\u79c1\u94a5");const e=new ke;e.setPrivateKey(l);const t=e.decrypt(r);if(!t)return void o("RSA\u89e3\u5bc6\u5931\u8d25\uff1a\u79c1\u94a5\u9519\u8bef\u6216\u6570\u636e\u683c\u5f0f\u4e0d\u6b63\u786e");o(t)}catch(e){o("RSA\u89e3\u5bc6\u5931\u8d25\uff1a"+e.message)}},D=()=>{a(""),o(""),c("")};return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u7f16\u7801/\u52a0\u5bc6\u5de5\u5177"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6 border-b border-gray-200",children:[{id:"base64",name:"Base64\u7f16\u7801"},{id:"url",name:"URL\u7f16\u7801"},{id:"html",name:"HTML\u7f16\u7801"},{id:"unicode",name:"Unicode\u7f16\u7801"},{id:"hex",name:"\u5341\u516d\u8fdb\u5236\u7f16\u7801"},{id:"hash",name:"\u54c8\u5e0c\u7b97\u6cd5"},{id:"aes",name:"AES\u52a0\u89e3\u5bc6"},{id:"des",name:"DES\u52a0\u89e3\u5bc6"},{id:"rsa",name:"RSA\u52a0\u89e3\u5bc6"}].map(e=>(0,i.jsx)("button",{onClick:()=>n(e.id),className:"tab-button ".concat(t===e.id?"active":"inactive"),children:e.name},e.id))}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-3",children:"\u8f93\u5165\u6587\u672c"}),(0,i.jsx)("textarea",{value:r,onChange:e=>a(e.target.value),className:"textarea-field h-32",placeholder:"\u8f93\u5165\u8981\u5904\u7406\u7684\u6587\u672c..."})]}),(0,i.jsx)("div",{children:(()=>{switch(t){case"base64":return(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:u,className:"btn-primary",children:"Base64\u7f16\u7801"}),(0,i.jsx)("button",{onClick:d,className:"btn-primary",children:"Base64\u89e3\u7801"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]});case"url":return(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:f,className:"btn-primary",children:"URL\u7f16\u7801"}),(0,i.jsx)("button",{onClick:h,className:"btn-primary",children:"URL\u89e3\u7801"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]});case"html":return(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:p,className:"btn-primary",children:"HTML\u7f16\u7801"}),(0,i.jsx)("button",{onClick:m,className:"btn-primary",children:"HTML\u89e3\u7801"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]});case"unicode":return(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:g,className:"btn-primary",children:"Unicode\u7f16\u7801"}),(0,i.jsx)("button",{onClick:v,className:"btn-primary",children:"Unicode\u89e3\u7801"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]});case"hex":return(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:y,className:"btn-primary",children:"\u5341\u516d\u8fdb\u5236\u7f16\u7801"}),(0,i.jsx)("button",{onClick:x,className:"btn-primary",children:"\u5341\u516d\u8fdb\u5236\u89e3\u7801"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]});case"hash":return(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex gap-3 flex-wrap",children:[(0,i.jsx)("button",{onClick:w,className:"btn-primary",children:"MD5\u54c8\u5e0c"}),(0,i.jsx)("button",{onClick:S,className:"btn-primary",children:"SHA1\u54c8\u5e0c"}),(0,i.jsx)("button",{onClick:k,className:"btn-primary",children:"SHA256\u54c8\u5e0c"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:(0,i.jsx)("p",{children:"\ud83d\udca1 \u63d0\u793a\uff1a\u54c8\u5e0c\u7b97\u6cd5\u662f\u5355\u5411\u7684\uff0c\u65e0\u6cd5\u89e3\u5bc6\u3002\u5e38\u7528\u4e8e\u5bc6\u7801\u5b58\u50a8\u3001\u6570\u636e\u5b8c\u6574\u6027\u9a8c\u8bc1\u7b49\u3002"})})]});case"des":return(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5bc6\u94a5\uff1a"}),(0,i.jsx)("input",{type:"text",value:l,onChange:e=>c(e.target.value),className:"input-field",placeholder:"\u8f93\u5165DES\u5bc6\u94a5"})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:E,className:"btn-primary",children:"DES\u52a0\u5bc6"}),(0,i.jsx)("button",{onClick:_,className:"btn-primary",children:"DES\u89e3\u5bc6"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:(0,i.jsx)("p",{children:"\ud83d\udca1 \u63d0\u793a\uff1aDES\u662f\u5bf9\u79f0\u52a0\u5bc6\u7b97\u6cd5\uff0c\u52a0\u5bc6\u548c\u89e3\u5bc6\u4f7f\u7528\u76f8\u540c\u7684\u5bc6\u94a5\u3002"})})]});case"aes":return(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5bc6\u94a5\uff1a"}),(0,i.jsx)("input",{type:"text",value:l,onChange:e=>c(e.target.value),className:"input-field",placeholder:"\u8f93\u5165AES\u5bc6\u94a5"})]}),(0,i.jsxs)("div",{className:"flex gap-3",children:[(0,i.jsx)("button",{onClick:N,className:"btn-primary",children:"AES\u52a0\u5bc6"}),(0,i.jsx)("button",{onClick:j,className:"btn-primary",children:"AES\u89e3\u5bc6"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:(0,i.jsx)("p",{children:"\ud83d\udca1 \u63d0\u793a\uff1aAES\u662f\u9ad8\u7ea7\u52a0\u5bc6\u6807\u51c6\uff0c\u652f\u6301128/192/256\u4f4d\u5bc6\u94a5\uff0c\u5b89\u5168\u6027\u9ad8\u3002"})})]});case"rsa":return(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"RSA\u5bc6\u94a5\uff1a"}),(0,i.jsx)("textarea",{value:l,onChange:e=>c(e.target.value),className:"textarea-field h-32",placeholder:"\u8f93\u5165RSA\u516c\u94a5\uff08\u7528\u4e8e\u52a0\u5bc6\uff09\u6216\u79c1\u94a5\uff08\u7528\u4e8e\u89e3\u5bc6\uff09"})]}),(0,i.jsxs)("div",{className:"flex gap-3 flex-wrap",children:[(0,i.jsx)("button",{onClick:T,className:"btn-primary",children:"RSA\u52a0\u5bc6"}),(0,i.jsx)("button",{onClick:B,className:"btn-primary",children:"RSA\u89e3\u5bc6"}),(0,i.jsx)("button",{onClick:C,className:"btn-success",children:"\u751f\u6210\u5bc6\u94a5\u5bf9"}),(0,i.jsx)("button",{onClick:D,className:"btn-secondary",children:"\u6e05\u7a7a"})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("p",{children:"\ud83d\udca1 \u63d0\u793a\uff1a"}),(0,i.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[(0,i.jsx)("li",{children:"RSA\u52a0\u5bc6\u4f7f\u7528\u516c\u94a5\uff0c\u89e3\u5bc6\u4f7f\u7528\u79c1\u94a5"}),(0,i.jsx)("li",{children:'\u5148\u70b9\u51fb"\u751f\u6210\u5bc6\u94a5\u5bf9"\u83b7\u53d6\u516c\u94a5\u548c\u79c1\u94a5'}),(0,i.jsx)("li",{children:"\u590d\u5236\u516c\u94a5\u5230\u5bc6\u94a5\u6846\u8fdb\u884c\u52a0\u5bc6\uff0c\u590d\u5236\u79c1\u94a5\u5230\u5bc6\u94a5\u6846\u8fdb\u884c\u89e3\u5bc6"}),(0,i.jsx)("li",{children:"RSA\u9002\u5408\u52a0\u5bc6\u5c11\u91cf\u6570\u636e\uff0c\u5927\u91cf\u6570\u636e\u5efa\u8bae\u4f7f\u7528AES"})]})]})]});default:return null}})()}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800",children:"\u8f93\u51fa\u7ed3\u679c"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(s)},className:"btn-secondary",disabled:!s,children:"\u590d\u5236\u7ed3\u679c"}),(0,i.jsx)("button",{onClick:()=>{const e=new Blob([s],{type:"text/plain"}),n=URL.createObjectURL(e),r=document.createElement("a");r.href=n,r.download="".concat(t,"_result.txt"),r.click(),URL.revokeObjectURL(n)},className:"btn-success",disabled:!s,children:"\u4e0b\u8f7d\u7ed3\u679c"})]})]}),(0,i.jsx)("textarea",{value:s,readOnly:!0,className:"result-area h-32",placeholder:"\u5904\u7406\u7ed3\u679c\u5c06\u5728\u8fd9\u91cc\u663e\u793a..."})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"\u6ce8\u610f\u4e8b\u9879\uff1a"}),(0,i.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,i.jsx)("li",{children:"\u2022 Base64\u3001URL\u3001HTML\u3001Unicode\u3001\u5341\u516d\u8fdb\u5236\u7f16\u7801\u4e3a\u5ba2\u6237\u7aef\u5b9e\u73b0"}),(0,i.jsx)("li",{children:"\u2022 MD5\u3001DES\u3001AES\u3001RSA\u7b49\u52a0\u5bc6\u7b97\u6cd5\u9700\u8981\u4e13\u4e1a\u7684\u52a0\u5bc6\u5e93\u652f\u6301"}),(0,i.jsx)("li",{children:"\u2022 \u751f\u4ea7\u73af\u5883\u4e2d\u8bf7\u4f7f\u7528\u7ecf\u8fc7\u5b89\u5168\u5ba1\u8ba1\u7684\u52a0\u5bc6\u5e93"}),(0,i.jsx)("li",{children:"\u2022 \u654f\u611f\u6570\u636e\u8bf7\u52ff\u5728\u5ba2\u6237\u7aef\u8fdb\u884c\u52a0\u5bc6\u5904\u7406"})]})]})]})]})},je=()=>{const[t,n]=(0,e.useState)(""),[r,a]=(0,e.useState)([]),[s,o]=(0,e.useState)({questionCount:10,questionType:"all",includeAnswers:!0,randomOrder:!0}),[l,c]=(0,e.useState)("generator");return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u968f\u673a\u9898\u76ee\u751f\u6210\u5de5\u5177"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6 border-b border-gray-200",children:[{id:"generator",name:"\u9898\u76ee\u751f\u6210"},{id:"format",name:"\u683c\u5f0f\u8bf4\u660e"},{id:"preview",name:"\u9898\u76ee\u9884\u89c8"}].map(e=>(0,i.jsx)("button",{onClick:()=>c(e.id),className:"tab-button ".concat(l===e.id?"active":"inactive"),children:e.name},e.id))}),"generator"===l&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)("label",{className:"text-lg font-medium text-gray-800",children:"\u9898\u76ee\u5e93\u5185\u5bb9\uff1a"}),(0,i.jsx)("button",{onClick:()=>n("# \u5355\u9009\u9898\n1. JavaScript\u662f\u4e00\u79cd\u4ec0\u4e48\u7c7b\u578b\u7684\u8bed\u8a00\uff1f\nA. \u7f16\u8bd1\u578b\u8bed\u8a00\nB. \u89e3\u91ca\u578b\u8bed\u8a00\nC. \u6c47\u7f16\u8bed\u8a00\nD. \u673a\u5668\u8bed\u8a00\n\u7b54\u6848\uff1aB\n\n2. \u4ee5\u4e0b\u54ea\u4e2a\u4e0d\u662fJavaScript\u7684\u6570\u636e\u7c7b\u578b\uff1f\nA. string\nB. number\nC. char\nD. boolean\n\u7b54\u6848\uff1aC\n\n# \u591a\u9009\u9898\n3. \u4ee5\u4e0b\u54ea\u4e9b\u662f\u524d\u7aef\u6846\u67b6\uff1f\uff08\u591a\u9009\uff09\nA. React\nB. Vue\nC. Angular\nD. Django\n\u7b54\u6848\uff1aABC\n\n# \u5224\u65ad\u9898\n4. JavaScript\u662f\u533a\u5206\u5927\u5c0f\u5199\u7684\u8bed\u8a00\u3002\n\u7b54\u6848\uff1a\u6b63\u786e\n\n5. HTML\u662f\u4e00\u79cd\u7f16\u7a0b\u8bed\u8a00\u3002\n\u7b54\u6848\uff1a\u9519\u8bef\n\n# \u586b\u7a7a\u9898\n6. JavaScript\u4e2d\u58f0\u660e\u53d8\u91cf\u4f7f\u7528\u5173\u952e\u5b57____\u6216____\u3002\n\u7b54\u6848\uff1avar|let\n\n7. CSS\u7684\u5168\u79f0\u662f____\u3002\n\u7b54\u6848\uff1aCascading Style Sheets\n\n# \u7b80\u7b54\u9898\n8. \u8bf7\u7b80\u8ff0JavaScript\u4e2d\u95ed\u5305\u7684\u6982\u5ff5\u3002\n\u7b54\u6848\uff1a\u95ed\u5305\u662f\u6307\u6709\u6743\u8bbf\u95ee\u53e6\u4e00\u4e2a\u51fd\u6570\u4f5c\u7528\u57df\u4e2d\u53d8\u91cf\u7684\u51fd\u6570\u3002\n\n# \u7f16\u7a0b\u9898\n9. \u7f16\u5199\u4e00\u4e2a\u51fd\u6570\uff0c\u8ba1\u7b97\u6570\u7ec4\u4e2d\u6240\u6709\u5143\u7d20\u7684\u548c\u3002\n\u7b54\u6848\uff1a\nfunction sum(arr) {\n  return arr.reduce((total, num) => total + num, 0);\n}"),className:"text-sm btn-secondary",children:"\u52a0\u8f7d\u793a\u4f8b"})]}),(0,i.jsx)("textarea",{value:t,onChange:e=>n(e.target.value),className:"textarea-field h-96",placeholder:"\u8bf7\u6309\u7167\u683c\u5f0f\u8f93\u5165\u9898\u76ee\u5185\u5bb9\uff0c\u6216\u70b9\u51fb'\u52a0\u8f7d\u793a\u4f8b'\u67e5\u770b\u683c\u5f0f..."})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800",children:"\u751f\u6210\u8bbe\u7f6e"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u9898\u76ee\u6570\u91cf\uff1a"}),(0,i.jsx)("input",{type:"number",value:s.questionCount,onChange:e=>o(t=>d(d({},t),{},{questionCount:parseInt(e.target.value)||10})),className:"input-field",min:"1",max:"100"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u9898\u76ee\u7c7b\u578b\uff1a"}),(0,i.jsxs)("select",{value:s.questionType,onChange:e=>o(t=>d(d({},t),{},{questionType:e.target.value})),className:"select-field",children:[(0,i.jsx)("option",{value:"all",children:"\u5168\u90e8\u7c7b\u578b"}),(0,i.jsx)("option",{value:"\u5355\u9009\u9898",children:"\u5355\u9009\u9898"}),(0,i.jsx)("option",{value:"\u591a\u9009\u9898",children:"\u591a\u9009\u9898"}),(0,i.jsx)("option",{value:"\u5224\u65ad\u9898",children:"\u5224\u65ad\u9898"}),(0,i.jsx)("option",{value:"\u586b\u7a7a\u9898",children:"\u586b\u7a7a\u9898"}),(0,i.jsx)("option",{value:"\u7b80\u7b54\u9898",children:"\u7b80\u7b54\u9898"}),(0,i.jsx)("option",{value:"\u7f16\u7a0b\u9898",children:"\u7f16\u7a0b\u9898"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:s.includeAnswers,onChange:e=>o(t=>d(d({},t),{},{includeAnswers:e.target.checked})),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm",children:"\u5305\u542b\u7b54\u6848"})]}),(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:s.randomOrder,onChange:e=>o(t=>d(d({},t),{},{randomOrder:e.target.checked})),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm",children:"\u968f\u673a\u987a\u5e8f"})]})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-3 pt-4",children:[(0,i.jsx)("button",{onClick:()=>{if(!t.trim())return void alert("\u8bf7\u5148\u8f93\u5165\u9898\u76ee\u5e93\u5185\u5bb9");const e=(e=>{const t=[],n=e.split("\n").filter(e=>e.trim());let r="",i=null;for(let a=0;a<n.length;a++){const e=n[a].trim();e.startsWith("#")?r=e.replace("#","").trim():/^\d+\./.test(e)?(i&&t.push(i),i={id:t.length+1,type:r,question:e.replace(/^\d+\.\s*/,""),options:[],answer:"",explanation:""}):/^[A-Z]\./.test(e)&&i?i.options.push(e):e.startsWith("\u7b54\u6848\uff1a")&&i?i.answer=e.replace("\u7b54\u6848\uff1a",""):e.startsWith("\u89e3\u6790\uff1a")&&i?i.explanation=e.replace("\u89e3\u6790\uff1a",""):!i||e.startsWith("\u7b54\u6848\uff1a")||e.startsWith("\u89e3\u6790\uff1a")||(i.question+="\n"+e)}return i&&t.push(i),t})(t);if(0===e.length)return void alert("\u672a\u80fd\u89e3\u6790\u5230\u6709\u6548\u9898\u76ee\uff0c\u8bf7\u68c0\u67e5\u683c\u5f0f");let n=e;if("all"!==s.questionType&&(n=e.filter(e=>e.type.includes(s.questionType))),0===n.length)return void alert("\u6ca1\u6709\u627e\u5230\u6307\u5b9a\u7c7b\u578b\u7684\u9898\u76ee");let r=[...n];s.randomOrder&&(r=r.sort(()=>Math.random()-.5)),r=r.slice(0,Math.min(s.questionCount,r.length)),a(r)},className:"btn-primary w-full",children:"\u751f\u6210\u968f\u673a\u9898\u76ee"}),(0,i.jsx)("button",{onClick:()=>{if(0===r.length)return void alert("\u8bf7\u5148\u751f\u6210\u9898\u76ee");let e="";r.forEach((t,n)=>{e+="".concat(n+1,". ").concat(t.question,"\n"),t.options.length>0&&t.options.forEach(t=>{e+="".concat(t,"\n")}),s.includeAnswers&&(e+="\u7b54\u6848\uff1a".concat(t.answer,"\n"),t.explanation&&(e+="\u89e3\u6790\uff1a".concat(t.explanation,"\n"))),e+="\n"}),navigator.clipboard.writeText(e),alert("\u9898\u76ee\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f")},className:"btn-secondary w-full",disabled:0===r.length,children:"\u590d\u5236\u9898\u76ee"}),(0,i.jsx)("button",{onClick:()=>{if(0===r.length)return void alert("\u8bf7\u5148\u751f\u6210\u9898\u76ee");let e="\u968f\u673a\u751f\u6210\u7684\u9898\u76ee\n";e+="==================\n\n",r.forEach((t,n)=>{e+="".concat(n+1,". ").concat(t.question,"\n"),t.options.length>0&&t.options.forEach(t=>{e+="".concat(t,"\n")}),s.includeAnswers&&(e+="\u7b54\u6848\uff1a".concat(t.answer,"\n"),t.explanation&&(e+="\u89e3\u6790\uff1a".concat(t.explanation,"\n"))),e+="\n"});const t=new Blob([e],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download="\u968f\u673a\u9898\u76ee_".concat((new Date).toISOString().slice(0,10),".txt"),i.click(),URL.revokeObjectURL(n)},className:"btn-success w-full",disabled:0===r.length,children:"\u5bfc\u51fa\u9898\u76ee"})]}),r.length>0&&(0,i.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-blue-800 mb-2",children:"\u751f\u6210\u7edf\u8ba1"}),(0,i.jsxs)("div",{className:"text-xs text-blue-600 space-y-1",children:[(0,i.jsxs)("div",{children:["\u5df2\u751f\u6210\u9898\u76ee\uff1a",r.length," \u9053"]}),(0,i.jsxs)("div",{children:["\u9898\u76ee\u7c7b\u578b\uff1a",[...new Set(r.map(e=>e.type))].join("\u3001")]})]})]})]})]})}),"format"===l&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-yellow-800 mb-4",children:"\u9898\u76ee\u6587\u672c\u5bfc\u5165\u683c\u5f0f\u89c4\u8303"}),(0,i.jsxs)("div",{className:"space-y-4 text-sm text-yellow-700",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"\u57fa\u672c\u683c\u5f0f\uff1a"}),(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,i.jsx)("li",{children:"\u4f7f\u7528 # \u6807\u8bb0\u9898\u76ee\u7c7b\u578b\uff08\u5982\uff1a# \u5355\u9009\u9898\uff09"}),(0,i.jsx)("li",{children:"\u9898\u76ee\u4ee5\u6570\u5b57\u5f00\u5934\uff08\u5982\uff1a1. \u9898\u76ee\u5185\u5bb9\uff09"}),(0,i.jsx)("li",{children:"\u9009\u9879\u4ee5\u5b57\u6bcd\u5f00\u5934\uff08\u5982\uff1aA. \u9009\u9879\u5185\u5bb9\uff09"}),(0,i.jsx)("li",{children:'\u7b54\u6848\u4ee5"\u7b54\u6848\uff1a"\u5f00\u5934'}),(0,i.jsx)("li",{children:'\u89e3\u6790\u4ee5"\u89e3\u6790\uff1a"\u5f00\u5934\uff08\u53ef\u9009\uff09'})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"\u652f\u6301\u7684\u9898\u76ee\u7c7b\u578b\uff1a"}),(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u5355\u9009\u9898"}),"\uff1a\u5305\u542bA\u3001B\u3001C\u3001D\u9009\u9879\uff0c\u7b54\u6848\u4e3a\u5355\u4e2a\u5b57\u6bcd"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u591a\u9009\u9898"}),"\uff1a\u5305\u542b\u591a\u4e2a\u9009\u9879\uff0c\u7b54\u6848\u4e3a\u591a\u4e2a\u5b57\u6bcd\u7ec4\u5408"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u5224\u65ad\u9898"}),'\uff1a\u7b54\u6848\u4e3a"\u6b63\u786e"\u6216"\u9519\u8bef"']}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u586b\u7a7a\u9898"}),"\uff1a\u7b54\u6848\u53ef\u7528|\u5206\u9694\u591a\u4e2a\u53ef\u80fd\u7b54\u6848"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u7b80\u7b54\u9898"}),"\uff1a\u5f00\u653e\u6027\u9898\u76ee\uff0c\u7b54\u6848\u4e3a\u6587\u672c\u63cf\u8ff0"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"\u7f16\u7a0b\u9898"}),"\uff1a\u4ee3\u7801\u7c7b\u9898\u76ee\uff0c\u7b54\u6848\u53ef\u5305\u542b\u4ee3\u7801\u5757"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"\u6ce8\u610f\u4e8b\u9879\uff1a"}),(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,i.jsx)("li",{children:"\u6bcf\u4e2a\u9898\u76ee\u4e4b\u95f4\u7528\u7a7a\u884c\u5206\u9694"}),(0,i.jsx)("li",{children:"\u9898\u76ee\u5185\u5bb9\u53ef\u4ee5\u8de8\u591a\u884c"}),(0,i.jsx)("li",{children:"\u9009\u9879\u548c\u7b54\u6848\u5fc5\u987b\u7d27\u8ddf\u5728\u9898\u76ee\u540e\u9762"}),(0,i.jsx)("li",{children:"\u652f\u6301\u4e2d\u82f1\u6587\u6df7\u5408\u5185\u5bb9"})]})]})]})]}),(0,i.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"\u793a\u4f8b\u683c\u5f0f\uff1a"}),(0,i.jsx)("pre",{className:"text-xs text-gray-600 whitespace-pre-wrap",children:"# \u5355\u9009\u9898\n1. JavaScript\u662f\u4ec0\u4e48\u7c7b\u578b\u7684\u8bed\u8a00\uff1f\nA. \u7f16\u8bd1\u578b\u8bed\u8a00\nB. \u89e3\u91ca\u578b\u8bed\u8a00\nC. \u6c47\u7f16\u8bed\u8a00\nD. \u673a\u5668\u8bed\u8a00\n\u7b54\u6848\uff1aB\n\u89e3\u6790\uff1aJavaScript\u662f\u4e00\u79cd\u89e3\u91ca\u578b\u7684\u811a\u672c\u8bed\u8a00\n\n# \u591a\u9009\u9898\n2. \u4ee5\u4e0b\u54ea\u4e9b\u662f\u524d\u7aef\u6846\u67b6\uff1f\nA. React\nB. Vue\nC. Angular\nD. Django\n\u7b54\u6848\uff1aABC\n\n# \u5224\u65ad\u9898\n3. HTML\u662f\u4e00\u79cd\u7f16\u7a0b\u8bed\u8a00\u3002\n\u7b54\u6848\uff1a\u9519\u8bef"})]})]}),"preview"===l&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800",children:"\u751f\u6210\u7684\u9898\u76ee\u9884\u89c8"}),0===r.length?(0,i.jsx)("div",{className:"text-center py-12 text-gray-500",children:'\u8bf7\u5148\u5728"\u9898\u76ee\u751f\u6210"\u9875\u9762\u751f\u6210\u9898\u76ee'}):(0,i.jsx)("div",{className:"space-y-6",children:r.map((e,t)=>(0,i.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsx)("span",{className:"text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded",children:e.type}),(0,i.jsxs)("span",{className:"text-sm text-gray-500",children:["\u7b2c ",t+1," \u9898"]})]}),(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsxs)("h4",{className:"font-medium text-gray-800 mb-2",children:[t+1,". ",e.question]})}),e.options.length>0&&(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsx)("div",{className:"space-y-1",children:e.options.map((e,t)=>(0,i.jsx)("div",{className:"text-sm text-gray-700",children:e},t))})}),s.includeAnswers&&(0,i.jsxs)("div",{className:"border-t border-gray-100 pt-3",children:[(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("span",{className:"font-medium text-green-600",children:"\u7b54\u6848\uff1a"}),(0,i.jsx)("span",{className:"text-green-700",children:e.answer})]}),e.explanation&&(0,i.jsxs)("div",{className:"text-sm mt-1",children:[(0,i.jsx)("span",{className:"font-medium text-blue-600",children:"\u89e3\u6790\uff1a"}),(0,i.jsx)("span",{className:"text-blue-700",children:e.explanation})]})]})]},t))})]})]})},Ee=()=>{var t;const[n,r]=(0,e.useState)([]),[a,s]=(0,e.useState)("\u76f4\u9500"),[o,l]=(0,e.useState)("\u7ebf\u4e0a"),[c,u]=(0,e.useState)({platform:"\u76f4\u9500",environment:"\u7ebf\u4e0a",username:"",password:"",staffUsername:"",staffPassword:"",url:"",note:""}),[f,h]=(0,e.useState)("manager"),[p,m]=(0,e.useState)(""),g={"\u76f4\u9500":{environments:["\u7ebf\u4e0a","dep","\u672c\u5730"],urls:{"\u7ebf\u4e0a":"http://i.flw.com/",dep:"http://i.fkw.com.faidev.cc/","\u672c\u5730":"http://i.fff.com/"}},OEM:{environments:["\u7ebf\u4e0a","\u672c\u5730"],urls:{"\u7ebf\u4e0a":"http://adm.webportal.top/","\u672c\u5730":"http://www.app.top/"}},"\u4ee3\u7406\u5546\u5e73\u53f0":{environments:["\u7ebf\u4e0a","\u672c\u5730"],urls:{"\u7ebf\u4e0a":"http://it.faisco.cn","\u672c\u5730":"http://tk.faisco.cn/index"}}},v=(0,e.useMemo)(()=>[{id:1,platform:"\u76f4\u9500",environment:"\u7ebf\u4e0a",username:"dannell",password:"faisco1234",staffUsername:"boss",staffPassword:"faisco1234",url:"http://i.flw.com/",note:"aid: 15938236"},{id:2,platform:"\u76f4\u9500",environment:"\u7ebf\u4e0a",username:"ywinger",password:"faisco1234",staffUsername:"yaowj",staffPassword:"faisco1234",url:"http://i.flw.com/",note:"aid:12199638 \u7070\u5ea6\u8d26\u53f7"},{id:6,platform:"\u76f4\u9500",environment:"dep",username:"huaedu1",password:"faisco1234",staffUsername:"boss",staffPassword:"faisco1234",url:"http://i.fkw.com.faidev.cc/",note:"dep\u73af\u5883\u6d4b\u8bd5\u8d26\u53f7"},{id:7,platform:"\u76f4\u9500",environment:"dep",username:"la19281",password:"faker0507",staffUsername:"",staffPassword:"",url:"http://i.fkw.com.faidev.cc/",note:"\u6d4b\u8bd5\u8d26\u53f7la19281"},{id:3,platform:"OEM",environment:"\u7ebf\u4e0a",username:"testmall6",password:"faisco1234",staffUsername:"fuji",staffPassword:"faisco1234",url:"http://adm.webportal.top/",note:"aid: ********"},{id:4,platform:"\u4ee3\u7406\u5546\u5e73\u53f0",environment:"\u7ebf\u4e0a",username:"faidev",password:"fai6508",staffUsername:"boss",staffPassword:"",url:"http://it.faisco.cn",note:"aid: 1591627 \u7070\u5ea6\u8d26\u53f7"}],[]),y=(0,e.useCallback)(e=>e.map(e=>{var t;return d(d({},e),{},{url:(null===(t=g[e.platform])||void 0===t?void 0:t.urls[e.environment])||e.url})}),[g]);(0,e.useEffect)(()=>{const e=localStorage.getItem("accountManager");if(e){const t=JSON.parse(e),n=y(t);r(n),localStorage.setItem("accountManager",JSON.stringify(n))}else{const e=y(v);r(e),localStorage.setItem("accountManager",JSON.stringify(e))}},[v,y]);const b=e=>{localStorage.setItem("accountManager",JSON.stringify(e))},x=async e=>{try{console.log("\ud83d\ude80 \u5f00\u59cb\u6267\u884c\u524d\u7aef\u81ea\u52a8\u767b\u5f55...",e);const t="".concat(e.url,"?autoLogin=true&username=").concat(encodeURIComponent(e.username),"&password=").concat(encodeURIComponent(e.password)),n=window.open(t,"_blank","width=1200,height=800");if(!n)return void alert("\u65e0\u6cd5\u6253\u5f00\u65b0\u7a97\u53e3\uff0c\u8bf7\u68c0\u67e5\u6d4f\u89c8\u5668\u5f39\u7a97\u8bbe\u7f6e");console.log("\u2705 \u767b\u5f55\u9875\u9762\u5df2\u6253\u5f00\uff0c\u5982\u679c\u5b89\u88c5\u4e86\u6d4f\u89c8\u5668\u6269\u5c55\uff0c\u5c06\u81ea\u52a8\u6267\u884c\u767b\u5f55"),setTimeout(()=>{try{const r="\n(function() {\n  console.log('\ud83d\ude80 \u5f00\u59cb\u81ea\u52a8\u767b\u5f55...');\n\n  // \u4eceURL\u53c2\u6570\u83b7\u53d6\u767b\u5f55\u4fe1\u606f\n  const urlParams = new URLSearchParams(window.location.search);\n  const username = urlParams.get('username') || '".concat(e.username,"';\n  const password = urlParams.get('password') || '").concat(e.password,"';\n\n  function sleep(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  async function autoLogin() {\n    try {\n      await sleep(2000);\n\n      // \u67e5\u627e\u7528\u6237\u540d\u8f93\u5165\u6846\n      let usernameInput = document.querySelector('input[type=\"text\"]') ||\n                         document.querySelector('input[name=\"username\"]') ||\n                         document.querySelector('input[placeholder*=\"\u7528\u6237\u540d\"]') ||\n                         document.querySelector('input[placeholder*=\"\u8d26\u53f7\"]');\n\n      // \u67e5\u627e\u5bc6\u7801\u8f93\u5165\u6846\n      let passwordInput = document.querySelector('input[type=\"password\"]');\n\n      if (!usernameInput || !passwordInput) {\n        console.error('\u672a\u627e\u5230\u767b\u5f55\u8f93\u5165\u6846');\n        alert('\u672a\u627e\u5230\u767b\u5f55\u8f93\u5165\u6846\uff0c\u8bf7\u624b\u52a8\u767b\u5f55');\n        return;\n      }\n\n      // \u586b\u5199\u7528\u6237\u540d\n      usernameInput.focus();\n      usernameInput.value = username;\n      usernameInput.dispatchEvent(new Event('input', { bubbles: true }));\n      usernameInput.dispatchEvent(new Event('change', { bubbles: true }));\n      await sleep(500);\n\n      // \u586b\u5199\u5bc6\u7801\n      passwordInput.focus();\n      passwordInput.value = password;\n      passwordInput.dispatchEvent(new Event('input', { bubbles: true }));\n      passwordInput.dispatchEvent(new Event('change', { bubbles: true }));\n      await sleep(500);\n\n      // \u67e5\u627e\u767b\u5f55\u6309\u94ae\n      let loginButton = document.querySelector('#login_button') ||\n                       document.querySelector('.login_button') ||\n                       document.querySelector('div[onclick*=\"login\"]') ||\n                       document.querySelector('button[type=\"submit\"]');\n\n      if (!loginButton) {\n        const allElements = document.querySelectorAll('div, button, a, span');\n        for (const element of allElements) {\n          const text = element.textContent?.trim() || '';\n          if ((text === '\u767b\u5f55' || text === '\u767b \u5f55' || text === 'Login') &&\n              element.offsetParent !== null && !element.disabled) {\n            loginButton = element;\n            break;\n          }\n        }\n      }\n\n      if (loginButton) {\n        await sleep(1000);\n        loginButton.click();\n        console.log('\u2705 \u81ea\u52a8\u767b\u5f55\u5b8c\u6210');\n\n        // \u663e\u793a\u6210\u529f\u63d0\u793a\n        const notification = document.createElement('div');\n        notification.style.cssText = 'position:fixed;top:20px;right:20px;background:#4CAF50;color:white;padding:15px;border-radius:5px;z-index:10000;box-shadow:0 4px 6px rgba(0,0,0,0.1);';\n        notification.textContent = '\u2705 \u81ea\u52a8\u767b\u5f55\u5b8c\u6210\uff01';\n        document.body.appendChild(notification);\n        setTimeout(() => notification.remove(), 3000);\n      } else {\n        console.error('\u672a\u627e\u5230\u767b\u5f55\u6309\u94ae');\n        alert('\u8d26\u53f7\u5bc6\u7801\u5df2\u586b\u5165\uff0c\u8bf7\u624b\u52a8\u70b9\u51fb\u767b\u5f55\u6309\u94ae');\n      }\n    } catch (error) {\n      console.error('\u81ea\u52a8\u767b\u5f55\u5931\u8d25:', error);\n      alert('\u81ea\u52a8\u767b\u5f55\u5931\u8d25: ' + error.message);\n    }\n  }\n\n  // \u68c0\u67e5\u662f\u5426\u9700\u8981\u81ea\u52a8\u767b\u5f55\n  const urlParams = new URLSearchParams(window.location.search);\n  if (urlParams.get('autoLogin') === 'true') {\n    autoLogin();\n  }\n})();");try{const e=n.document.createElement("script");e.textContent=r,n.document.head.appendChild(e),console.log("\u2705 \u81ea\u52a8\u767b\u5f55\u811a\u672c\u5df2\u6ce8\u5165")}catch(t){console.warn("\u65e0\u6cd5\u76f4\u63a5\u6ce8\u5165\u811a\u672c\uff0c\u53ef\u80fd\u662f\u8de8\u57df\u9650\u5236");const e='\n\ud83d\ude80 \u81ea\u52a8\u767b\u5f55\u811a\u672c\n\n\u7531\u4e8e\u6d4f\u89c8\u5668\u8de8\u57df\u9650\u5236\uff0c\u8bf7\u6309\u4ee5\u4e0b\u6b65\u9aa4\u624b\u52a8\u6267\u884c\uff1a\n\n1. \u5728\u65b0\u6253\u5f00\u7684\u767b\u5f55\u9875\u9762\u6309 F12 \u6253\u5f00\u5f00\u53d1\u8005\u5de5\u5177\n2. \u5207\u6362\u5230 "Console" \u6807\u7b7e\u9875\n3. \u7c98\u8d34\u4ee5\u4e0b\u811a\u672c\u5e76\u6309\u56de\u8f66\u6267\u884c\uff1a\n\n'.concat(r,"\n\n\u6216\u8005\u5b89\u88c5\u6211\u4eec\u63d0\u4f9b\u7684\u6d4f\u89c8\u5668\u6269\u5c55\u6765\u81ea\u52a8\u6267\u884c\uff01\n            ");navigator.clipboard?navigator.clipboard.writeText(r).then(()=>{alert("\u811a\u672c\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f\uff01\n\n"+e)}).catch(()=>{alert(e)}):alert(e)}}catch(r){console.error("\u811a\u672c\u6ce8\u5165\u5931\u8d25:",r)}},3e3)}catch(t){console.error("\u524d\u7aef\u81ea\u52a8\u767b\u5f55\u51fa\u9519:",t),alert("\u524d\u7aef\u81ea\u52a8\u767b\u5f55\u5931\u8d25: "+t.message)}},w=async e=>{e.url?await(async e=>{if(e.url)try{console.log("\ud83d\ude80 \u5f00\u59cb\u6267\u884cSelenium\u81ea\u52a8\u767b\u5f55...",e);const n=document.createElement("div");let r;n.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        background: #2196F3;\n        color: white;\n        padding: 15px 20px;\n        border-radius: 5px;\n        z-index: 10000;\n        box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n      ",n.textContent="\ud83d\ude80 \u6b63\u5728\u542f\u52a8Selenium\u6d4f\u89c8\u5668...",document.body.appendChild(n);const i=["/selenium-login","http://localhost:5001/selenium-login","http://127.0.0.1:5001/selenium-login"];let a;for(const o of i)try{if(r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:e.url,username:e.username,password:e.password})}),r.ok)break}catch(t){a=t,console.warn("\u7aef\u70b9 ".concat(o," \u5931\u8d25:"),t.message);continue}if(!r)throw a||new Error("\u6240\u6709Selenium\u670d\u52a1\u7aef\u70b9\u90fd\u65e0\u6cd5\u8fde\u63a5");if(!r.ok){const e=await r.text();throw new Error("HTTP ".concat(r.status,": ").concat(e))}const s=await r.json();if(n.parentNode&&n.parentNode.removeChild(n),!s.success)throw new Error(s.message);{const e=document.createElement("div");e.style.cssText="\n          position: fixed;\n          top: 20px;\n          right: 20px;\n          background: #4CAF50;\n          color: white;\n          padding: 15px 20px;\n          border-radius: 5px;\n          z-index: 10000;\n          box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n        ",e.textContent="\u2705 Selenium\u81ea\u52a8\u767b\u5f55\u6210\u529f\uff01",document.body.appendChild(e),setTimeout(()=>{e.parentNode&&e.parentNode.removeChild(e)},3e3),console.log("\u2705 Selenium\u81ea\u52a8\u767b\u5f55\u6210\u529f")}}catch(t){console.error("Selenium\u81ea\u52a8\u767b\u5f55\u5931\u8d25:",t);const n=document.createElement("div");n.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        background: #f44336;\n        color: white;\n        padding: 15px 20px;\n        border-radius: 5px;\n        z-index: 10000;\n        box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n      ",n.textContent="\u274c Selenium\u767b\u5f55\u5931\u8d25: ".concat(t.message),document.body.appendChild(n),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n)},5e3),console.log("\ud83d\udd04 Selenium\u5931\u8d25\uff0c\u56de\u9000\u5230\u524d\u7aef\u81ea\u52a8\u767b\u5f55\u65b9\u6848..."),x(e)}else alert("\u8be5\u8d26\u53f7\u6ca1\u6709\u914d\u7f6e\u7f51\u5740")})(e):alert("\u8be5\u8d26\u53f7\u6ca1\u6709\u914d\u7f6e\u7f51\u5740")},S=n.filter(e=>{const t="\u5168\u90e8"===a||e.platform===a,n="\u5168\u90e8"===o||e.environment===o,r=!p||e.username.toLowerCase().includes(p.toLowerCase())||e.platform.toLowerCase().includes(p.toLowerCase())||e.note.toLowerCase().includes(p.toLowerCase());return t&&n&&r});return(0,i.jsxs)("div",{className:"tool-section",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:"\u5e38\u7528\u8d26\u53f7\u767b\u5f55\u7ba1\u7406"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6 border-b border-gray-200",children:[{id:"manager",name:"\u8d26\u53f7\u7ba1\u7406"},{id:"add",name:"\u6dfb\u52a0\u8d26\u53f7"},{id:"settings",name:"\u8bbe\u7f6e"}].map(e=>(0,i.jsx)("button",{onClick:()=>h(e.id),className:"tab-button ".concat(f===e.id?"active":"inactive"),children:e.name},e.id))}),"manager"===f&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"\u5e73\u53f0\uff1a"}),(0,i.jsxs)("select",{value:a,onChange:e=>s(e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"\u5168\u90e8",children:"\u5168\u90e8\u5e73\u53f0"}),Object.keys(g).map(e=>(0,i.jsx)("option",{value:e,children:e},e))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"\u73af\u5883\uff1a"}),(0,i.jsxs)("select",{value:o,onChange:e=>l(e.target.value),className:"select-field",children:[(0,i.jsx)("option",{value:"\u5168\u90e8",children:"\u5168\u90e8\u73af\u5883"}),(0,i.jsx)("option",{value:"\u7ebf\u4e0a",children:"\u7ebf\u4e0a"}),(0,i.jsx)("option",{value:"dep",children:"dep"}),(0,i.jsx)("option",{value:"\u672c\u5730",children:"\u672c\u5730"}),(0,i.jsx)("option",{value:"\u5f00\u53d1\u73af\u5883",children:"\u5f00\u53d1\u73af\u5883"}),(0,i.jsx)("option",{value:"\u6d4b\u8bd5\u73af\u5883",children:"\u6d4b\u8bd5\u73af\u5883"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"\u641c\u7d22\uff1a"}),(0,i.jsx)("input",{type:"text",value:p,onChange:e=>m(e.target.value),className:"input-field",placeholder:"\u641c\u7d22\u7528\u6237\u540d\u3001\u5e73\u53f0\u6216\u5907\u6ce8..."})]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:["\u5171\u627e\u5230 ",S.length," \u4e2a\u8d26\u53f7"]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[S.map(e=>(0,i.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-6 gap-4 items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-800",children:e.platform}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:e.environment})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"\u7528\u6237\u540d"}),(0,i.jsx)("div",{className:"font-medium",children:e.username})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"\u5bc6\u7801"}),(0,i.jsx)("div",{className:"font-medium text-xs",children:e.password})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"\u5458\u5de5\u8d26\u53f7"}),(0,i.jsx)("div",{className:"font-medium",children:e.staffUsername||"-"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"\u5907\u6ce8"}),(0,i.jsx)("div",{className:"text-sm text-gray-700",children:e.note||"-"})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsx)("button",{onClick:()=>w(e),className:"btn-primary text-xs",title:"\u81ea\u52a8\u586b\u5199\u8d26\u53f7\u5bc6\u7801\u5e76\u767b\u5f55",children:"\u81ea\u52a8\u767b\u5f55"}),(0,i.jsx)("button",{onClick:()=>(e=>{const t="\u5e73\u53f0: ".concat(e.platform,"\n\u73af\u5883: ").concat(e.environment,"\n\u7528\u6237\u540d: ").concat(e.username,"\n\u5bc6\u7801: ").concat(e.password,"\n\u5458\u5de5\u8d26\u53f7: ").concat(e.staffUsername,"\n\u5458\u5de5\u5bc6\u7801: ").concat(e.staffPassword,"\n\u7f51\u5740: ").concat(e.url,"\n\u5907\u6ce8: ").concat(e.note);navigator.clipboard.writeText(t),alert("\u8d26\u53f7\u4fe1\u606f\u5df2\u590d\u5236\u5230\u526a\u8d34\u677f")})(e),className:"btn-secondary text-xs",children:"\u590d\u5236\u4fe1\u606f"})]})]})},e.id)),0===S.length&&(0,i.jsx)("div",{className:"text-center py-12 text-gray-500",children:"\u6ca1\u6709\u627e\u5230\u5339\u914d\u7684\u8d26\u53f7"})]})]}),"add"===f&&(0,i.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800",children:"\u6dfb\u52a0\u65b0\u8d26\u53f7"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5e73\u53f0\uff1a"}),(0,i.jsx)("select",{value:c.platform,onChange:e=>u(t=>{var n;return d(d({},t),{},{platform:e.target.value,url:(null===(n=g[e.target.value])||void 0===n?void 0:n.urls[t.environment])||""})}),className:"select-field",children:Object.keys(g).map(e=>(0,i.jsx)("option",{value:e,children:e},e))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u73af\u5883\uff1a"}),(0,i.jsx)("select",{value:c.environment,onChange:e=>u(t=>{var n;return d(d({},t),{},{environment:e.target.value,url:(null===(n=g[t.platform])||void 0===n?void 0:n.urls[e.target.value])||""})}),className:"select-field",children:null===(t=g[c.platform])||void 0===t?void 0:t.environments.map(e=>(0,i.jsx)("option",{value:e,children:e},e))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u7528\u6237\u540d\uff1a*"}),(0,i.jsx)("input",{type:"text",value:c.username,onChange:e=>u(t=>d(d({},t),{},{username:e.target.value})),className:"input-field",placeholder:"\u8bf7\u8f93\u5165\u7528\u6237\u540d"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5bc6\u7801\uff1a*"}),(0,i.jsx)("input",{type:"password",value:c.password,onChange:e=>u(t=>d(d({},t),{},{password:e.target.value})),className:"input-field",placeholder:"\u8bf7\u8f93\u5165\u5bc6\u7801"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5458\u5de5\u8d26\u53f7\uff1a"}),(0,i.jsx)("input",{type:"text",value:c.staffUsername,onChange:e=>u(t=>d(d({},t),{},{staffUsername:e.target.value})),className:"input-field",placeholder:"\u8bf7\u8f93\u5165\u5458\u5de5\u8d26\u53f7"})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5458\u5de5\u5bc6\u7801\uff1a"}),(0,i.jsx)("input",{type:"password",value:c.staffPassword,onChange:e=>u(t=>d(d({},t),{},{staffPassword:e.target.value})),className:"input-field",placeholder:"\u8bf7\u8f93\u5165\u5458\u5de5\u5bc6\u7801"})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u7f51\u5740\uff1a"}),(0,i.jsx)("input",{type:"url",value:c.url,onChange:e=>u(t=>d(d({},t),{},{url:e.target.value})),className:"input-field",placeholder:"\u81ea\u52a8\u586b\u5145\u6216\u624b\u52a8\u8f93\u5165\u7f51\u5740"})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\u5907\u6ce8\uff1a"}),(0,i.jsx)("textarea",{value:c.note,onChange:e=>u(t=>d(d({},t),{},{note:e.target.value})),className:"textarea-field h-20",placeholder:"\u8bf7\u8f93\u5165\u5907\u6ce8\u4fe1\u606f\uff0c\u5982aid\u7b49"})]})]}),(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)("button",{onClick:()=>{var e;if(!c.username||!c.password)return void alert("\u8bf7\u586b\u5199\u7528\u6237\u540d\u548c\u5bc6\u7801");const t=d(d({},c),{},{id:Date.now(),url:(null===(e=g[c.platform])||void 0===e?void 0:e.urls[c.environment])||""}),i=[...n,t];r(i),b(i),u({platform:"\u76f4\u9500",environment:"\u7ebf\u4e0a",username:"",password:"",staffUsername:"",staffPassword:"",url:"",note:""}),alert("\u8d26\u53f7\u6dfb\u52a0\u6210\u529f")},className:"btn-primary px-8",children:"\u6dfb\u52a0\u8d26\u53f7"})})]}),"settings"===f&&(0,i.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-800",children:"\u6570\u636e\u7ba1\u7406"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"\u6570\u636e\u5bfc\u51fa"}),(0,i.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:"\u5bfc\u51fa\u6240\u6709\u8d26\u53f7\u6570\u636e\u4e3aJSON\u6587\u4ef6\uff0c\u53ef\u7528\u4e8e\u5907\u4efd\u6216\u8fc1\u79fb\u3002"}),(0,i.jsx)("button",{onClick:()=>{const e=JSON.stringify(n,null,2),t=new Blob([e],{type:"application/json"}),r=URL.createObjectURL(t),i=document.createElement("a");i.href=r,i.download="\u8d26\u53f7\u6570\u636e_".concat((new Date).toISOString().slice(0,10),".json"),i.click(),URL.revokeObjectURL(r)},className:"btn-secondary",children:"\u5bfc\u51fa\u8d26\u53f7\u6570\u636e"})]}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\u6570\u636e\u5bfc\u5165"}),(0,i.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"\u4eceJSON\u6587\u4ef6\u5bfc\u5165\u8d26\u53f7\u6570\u636e\uff0c\u5c06\u8986\u76d6\u5f53\u524d\u6240\u6709\u6570\u636e\u3002"}),(0,i.jsx)("input",{type:"file",accept:".json",onChange:e=>{const t=e.target.files[0];if(t){const e=new FileReader;e.onload=e=>{try{const t=JSON.parse(e.target.result);r(t),b(t),alert("\u8d26\u53f7\u6570\u636e\u5bfc\u5165\u6210\u529f")}catch(t){alert("\u5bfc\u5165\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u6587\u4ef6\u683c\u5f0f")}},e.readAsText(t)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"})]}),(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-medium text-red-800 mb-2",children:"\u91cd\u7f6e\u6570\u636e"}),(0,i.jsx)("p",{className:"text-sm text-red-700 mb-3",children:"\u6e05\u9664\u6240\u6709\u8d26\u53f7\u6570\u636e\u5e76\u6062\u590d\u9ed8\u8ba4\u8d26\u53f7\uff0c\u6b64\u64cd\u4f5c\u4e0d\u53ef\u64a4\u9500\u3002"}),(0,i.jsx)("button",{onClick:()=>{window.confirm("\u786e\u5b9a\u8981\u91cd\u7f6e\u6240\u6709\u6570\u636e\u5417\uff1f\u6b64\u64cd\u4f5c\u4e0d\u53ef\u64a4\u9500\uff01")&&(r(v),b(v),alert("\u6570\u636e\u5df2\u91cd\u7f6e"))},className:"btn-danger",children:"\u91cd\u7f6e\u4e3a\u9ed8\u8ba4\u6570\u636e"})]})]}),(0,i.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,i.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"\u4f7f\u7528\u8bf4\u660e"}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("p",{children:"\u2022 \u8d26\u53f7\u6570\u636e\u4fdd\u5b58\u5728\u6d4f\u89c8\u5668\u672c\u5730\u5b58\u50a8\u4e2d"}),(0,i.jsx)("p",{children:"\u2022 \u652f\u6301\u6309\u5e73\u53f0\u3001\u73af\u5883\u3001A/B\u5206\u7ec4\u7b5b\u9009\u8d26\u53f7"}),(0,i.jsx)("p",{children:"\u2022 \u5feb\u901f\u767b\u5f55\u529f\u80fd\u4f1a\u5728\u65b0\u6807\u7b7e\u9875\u6253\u5f00\u5bf9\u5e94\u7f51\u5740"}),(0,i.jsx)("p",{children:"\u2022 \u590d\u5236\u529f\u80fd\u4f1a\u5c06\u8d26\u53f7\u4fe1\u606f\u590d\u5236\u5230\u526a\u8d34\u677f"}),(0,i.jsx)("p",{children:"\u2022 \u5efa\u8bae\u5b9a\u671f\u5bfc\u51fa\u6570\u636e\u8fdb\u884c\u5907\u4efd"})]})]})]})]})};const _e=function(){var t;const[n,o]=(0,e.useState)("\u8d26\u53f7\u7ba1\u7406"),l=[{id:"account-manager",name:"\u8d26\u53f7\u7ba1\u7406",component:Ee},{id:"time-tools",name:"\u65f6\u95f4\u5904\u7406",component:g},{id:"encoding-tools",name:"\u7f16\u7801/\u52a0\u5bc6",component:Ne},{id:"image-processor",name:"\u6587\u4ef6\u5904\u7406",component:v},{id:"text-counter",name:"\u5b57\u6570\u7edf\u8ba1",component:h},{id:"data-generator",name:"\u6570\u636e\u751f\u6210",component:f},{id:"json-compare",name:"JSON\u6570\u636e\u5bf9\u6bd4",component:m},{id:"random-questions",name:"\u968f\u673a\u9898\u76ee",component:je},{id:"regex-tester",name:"\u6b63\u5219\u6d4b\u8bd5",component:p}],c=null===(t=l.find(e=>e.name===n))||void 0===t?void 0:t.component;return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)(r,{}),(0,i.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"km\u6d4b\u8bd5\u5de5\u5177\u5408\u96c6"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"\u4e00\u7ad9\u5f0f\u6570\u636e\u751f\u6210\u548c\u5904\u7406\u5de5\u5177\u96c6"})]}),(0,i.jsx)(a,{categories:l,activeCategory:n,onCategoryChange:o}),(0,i.jsx)(s,{children:c&&e.createElement(c)})]})]})};t.createRoot(document.getElementById("root")).render((0,i.jsx)(e.StrictMode,{children:(0,i.jsx)(_e,{})}))})()})();
//# sourceMappingURL=main.5876a3f8.js.map