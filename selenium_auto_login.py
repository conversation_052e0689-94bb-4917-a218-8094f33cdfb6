# -*- coding: utf-8 -*-
"""
基于Selenium的自动登录服务
参考Login.py的实现方式，启动独立浏览器进行自动登录
"""

import os
import time
from flask import Flask, request, jsonify
from flask_cors import CORS
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

app = Flask(__name__)
# 配置CORS，允许所有来源的请求
CORS(app, resources={
    r"/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})

class SeleniumAutoLogin:
    def __init__(self):
        self.driver = None
        
    def setup_browser(self):
        """设置Chrome浏览器（参考Login.py的实现）"""
        try:
            chrome_options = Options()
            # 参考Login.py的配置
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # 禁用密码保存提示
            prefs = {
                "profile.cookie_controls_mode": 1,
                "credentials_enable_service": False, 
                "profile.password_manager_enabled": False
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            
            # 执行脚本隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
        except Exception as e:
            print(f"❌ 浏览器启动失败: {e}")
            return False
    
    def find_element_smart(self, selectors, timeout=10):
        """智能查找元素，尝试多种选择器"""
        wait = WebDriverWait(self.driver, timeout)
        
        for selector_type, selector_value in selectors:
            try:
                if selector_type == "xpath":
                    element = wait.until(EC.presence_of_element_located((By.XPATH, selector_value)))
                elif selector_type == "css":
                    element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector_value)))
                elif selector_type == "id":
                    element = wait.until(EC.presence_of_element_located((By.ID, selector_value)))
                elif selector_type == "name":
                    element = wait.until(EC.presence_of_element_located((By.NAME, selector_value)))
                elif selector_type == "class":
                    element = wait.until(EC.presence_of_element_located((By.CLASS_NAME, selector_value)))
                
                # 检查元素是否可见和可交互
                if element.is_displayed() and element.is_enabled():
                    print(f"✅ 找到元素: {selector_type}={selector_value}")
                    return element
            except (TimeoutException, NoSuchElementException):
                continue
        
        return None
    
    def auto_login(self, url, username, password):
        """执行自动登录"""
        try:
            print(f"🚀 开始自动登录: {url}")
            
            # 启动浏览器
            if not self.setup_browser():
                return {"success": False, "message": "浏览器启动失败"}
            
            # 访问登录页面
            self.driver.get(url)
            print(f"📄 页面加载完成: {url}")
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找用户名输入框
            username_selectors = [
                ("css", 'input[type="text"]'),
                ("name", "username"),
                ("name", "user"),
                ("css", 'input[placeholder*="用户名"]'),
                ("css", 'input[placeholder*="账号"]'),
                ("css", 'input[placeholder*="手机"]'),
                ("css", 'input[placeholder*="邮箱"]'),
                ("id", "username"),
                ("id", "user"),
                ("class", "username"),
            ]
            
            username_input = self.find_element_smart(username_selectors)
            if not username_input:
                return {"success": False, "message": "未找到用户名输入框"}
            
            # 查找密码输入框
            password_selectors = [
                ("css", 'input[type="password"]'),
                ("name", "password"),
                ("name", "pwd"),
                ("id", "password"),
                ("id", "pwd"),
                ("class", "password"),
            ]
            
            password_input = self.find_element_smart(password_selectors)
            if not password_input:
                return {"success": False, "message": "未找到密码输入框"}
            
            # 填写用户名
            print(f"📝 填写用户名: {username}")
            username_input.clear()
            username_input.send_keys(username)
            time.sleep(0.5)
            
            # 填写密码
            print("📝 填写密码...")
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(0.5)
            
            # 查找登录按钮
            login_selectors = [
                ("id", "login_button"),
                ("class", "login_button"),
                ("css", 'button[type="submit"]'),
                ("css", 'input[type="submit"]'),
                ("css", 'div[onclick*="login"]'),
                ("css", 'a[onclick*="login"]'),
                ("xpath", "//button[contains(text(), '登录')]"),
                ("xpath", "//div[contains(text(), '登录')]"),
                ("xpath", "//a[contains(text(), '登录')]"),
                ("xpath", "//span[contains(text(), '登录')]"),
                ("xpath", "//button[contains(text(), '登 录')]"),
                ("xpath", "//div[contains(text(), '登 录')]"),
                ("xpath", "//button[contains(text(), 'Login')]"),
                ("xpath", "//div[contains(text(), 'Login')]"),
            ]
            
            login_button = self.find_element_smart(login_selectors)
            if not login_button:
                return {"success": False, "message": "账号密码已填入，但未找到登录按钮，请手动点击登录"}
            
            # 点击登录按钮
            print("🔘 点击登录按钮...")
            time.sleep(1)
            login_button.click()
            
            print("✅ 自动登录完成！")
            return {"success": True, "message": "自动登录成功"}
            
        except Exception as e:
            print(f"❌ 自动登录失败: {e}")
            return {"success": False, "message": f"自动登录失败: {str(e)}"}
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                print("🔒 浏览器已关闭")
            except:
                pass
            self.driver = None

# 全局实例
auto_login_service = SeleniumAutoLogin()

@app.route('/selenium-login', methods=['POST'])
def selenium_login():
    """Selenium自动登录接口"""
    try:
        data = request.get_json()
        url = data.get('url')
        username = data.get('username')
        password = data.get('password')
        
        if not all([url, username, password]):
            return jsonify({"success": False, "message": "缺少必要参数"})
        
        # 执行自动登录
        result = auto_login_service.auto_login(url, username, password)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "message": f"服务错误: {str(e)}"})

@app.route('/close-selenium', methods=['POST'])
def close_selenium():
    """关闭Selenium浏览器"""
    try:
        auto_login_service.close_browser()
        return jsonify({"success": True, "message": "浏览器已关闭"})
    except Exception as e:
        return jsonify({"success": False, "message": f"关闭失败: {str(e)}"})

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({"status": "ok", "service": "Selenium Auto Login"})

if __name__ == '__main__':
    print("🚀 启动Selenium自动登录服务...")
    print("📋 API接口:")
    print("   POST /selenium-login - 执行Selenium自动登录")
    print("   POST /close-selenium - 关闭浏览器")
    print("   GET  /health - 健康检查")
    print("🌐 服务地址: http://localhost:5001")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
