// 🚀 凡科登录页面专用自动登录脚本
// 请在登录页面控制台直接执行此脚本

(function() {
    console.log('🚀 开始执行凡科登录脚本...');
    
    // 账号配置 - 请根据需要修改
    const account = {
        username: 'la19281',
        password: 'faker0507'
    };
    
    console.log('📋 使用账号:', account.username);
    
    // 工具函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 模拟真实用户输入
    async function simulateTyping(element, text) {
        if (!element) {
            console.error('❌ 元素不存在');
            return false;
        }
        
        console.log(`📝 开始输入: ${text}`);
        
        // 聚焦并清空
        element.focus();
        element.click();
        await sleep(100);
        
        // 清空现有内容
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
        await sleep(200);
        
        // 逐字符输入
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            
            // 触发各种事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new KeyboardEvent('keydown', { 
                key: text[i], 
                bubbles: true 
            }));
            element.dispatchEvent(new KeyboardEvent('keyup', { 
                key: text[i], 
                bubbles: true 
            }));
            
            await sleep(150); // 模拟真实输入速度
        }
        
        // 触发完成事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✅ 输入完成: ${element.value}`);
        await sleep(300);
        return true;
    }
    
    // 查找并点击元素
    async function clickElement(element, description) {
        if (!element) {
            console.error(`❌ 未找到${description}`);
            return false;
        }
        
        console.log(`🔘 点击${description}...`);
        element.click();
        await sleep(500);
        console.log(`✅ ${description}已点击`);
        return true;
    }
    
    // 主要登录流程
    async function performLogin() {
        try {
            console.log('⏳ 等待页面加载...');
            await sleep(2000);
            
            // 1. 确保在密码登录模式
            console.log('🔍 检查登录模式...');
            const passwordTab = document.querySelector('div:contains("密码登录")') || 
                               document.querySelector('[class*="tab"]:contains("密码")') ||
                               document.querySelector('span:contains("密码登录")');
            
            if (passwordTab) {
                await clickElement(passwordTab, '密码登录标签');
            }
            
            // 2. 查找用户名输入框
            console.log('🔍 查找用户名输入框...');
            const usernameSelectors = [
                'input[placeholder*="用户名"]',
                'input[placeholder*="手机号"]',
                'input[placeholder*="账号"]',
                'input[name="username"]',
                'input[name="account"]',
                'input[name="mobile"]',
                'input[type="text"]',
                'input[type="tel"]'
            ];
            
            let usernameInput = null;
            for (const selector of usernameSelectors) {
                usernameInput = document.querySelector(selector);
                if (usernameInput && usernameInput.offsetParent !== null) {
                    console.log(`✅ 找到用户名输入框: ${selector}`);
                    break;
                }
            }
            
            // 3. 查找密码输入框
            console.log('🔍 查找密码输入框...');
            const passwordInput = document.querySelector('input[type="password"]');
            
            // 4. 查找登录按钮
            console.log('🔍 查找登录按钮...');
            const loginButtonSelectors = [
                'button[type="submit"]',
                'button:contains("登录")',
                'button:contains("登 录")',
                '.login-btn',
                '.btn-login',
                'input[type="submit"]',
                'button'
            ];
            
            let loginButton = null;
            for (const selector of loginButtonSelectors) {
                const buttons = document.querySelectorAll(selector);
                for (const btn of buttons) {
                    if (btn.textContent.includes('登录') || 
                        btn.textContent.includes('登 录') ||
                        btn.type === 'submit') {
                        loginButton = btn;
                        console.log(`✅ 找到登录按钮: ${btn.textContent}`);
                        break;
                    }
                }
                if (loginButton) break;
            }
            
            // 检查必要元素
            if (!usernameInput) {
                console.error('❌ 未找到用户名输入框');
                return false;
            }
            
            if (!passwordInput) {
                console.error('❌ 未找到密码输入框');
                return false;
            }
            
            if (!loginButton) {
                console.error('❌ 未找到登录按钮');
                return false;
            }
            
            console.log('✅ 所有必要元素已找到');
            
            // 5. 输入用户名
            await simulateTyping(usernameInput, account.username);
            
            // 6. 输入密码
            await simulateTyping(passwordInput, account.password);
            
            // 7. 点击登录按钮
            await sleep(1000);
            await clickElement(loginButton, '登录按钮');
            
            // 8. 等待登录结果
            console.log('⏳ 等待登录结果...');
            await sleep(3000);
            
            // 9. 检查是否有错误提示
            const errorSelectors = [
                '.error',
                '.alert',
                '.warning',
                '[class*="error"]',
                '[class*="alert"]',
                '[class*="tip"]'
            ];
            
            let hasError = false;
            for (const selector of errorSelectors) {
                const errorElement = document.querySelector(selector);
                if (errorElement && errorElement.offsetParent !== null && errorElement.textContent.trim()) {
                    console.warn('⚠️ 发现错误提示:', errorElement.textContent);
                    hasError = true;
                }
            }
            
            if (!hasError) {
                console.log('🎉 登录流程执行完成！');
                
                // 10. 尝试关闭可能的弹窗
                await sleep(2000);
                const closeSelectors = [
                    '.close',
                    '.btn-close',
                    '.modal-close',
                    'button:contains("关闭")',
                    'button:contains("确定")',
                    'button:contains("知道了")'
                ];
                
                for (const selector of closeSelectors) {
                    const closeBtn = document.querySelector(selector);
                    if (closeBtn && closeBtn.offsetParent !== null) {
                        closeBtn.click();
                        console.log('✅ 已关闭弹窗');
                        await sleep(500);
                    }
                }
            }
            
            return !hasError;
            
        } catch (error) {
            console.error('❌ 登录过程中出错:', error);
            return false;
        }
    }
    
    // 开始执行
    console.log('🎯 开始自动登录流程...');
    performLogin().then(success => {
        if (success) {
            console.log('🎉 自动登录成功！');
        } else {
            console.log('❌ 自动登录失败，请检查账号密码或手动登录');
        }
    });
    
})();

// 使用说明：
// 1. 确保已在登录页面：http://i.fkw.com.faidev.cc/
// 2. 按F12打开开发者工具
// 3. 切换到Console标签
// 4. 复制粘贴整个脚本
// 5. 按回车执行
// 6. 观察控制台日志
