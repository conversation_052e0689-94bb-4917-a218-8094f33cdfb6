#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Selenium自动登录功能
"""

import requests
import json

def test_selenium_login():
    """测试Selenium登录"""
    url = "http://localhost:5001/selenium-login"
    
    data = {
        "url": "http://i.fkw.com.faidev.cc/",
        "username": "sixianchan", 
        "password": "9321gogogo"
    }
    
    try:
        print("🚀 开始测试Selenium自动登录...")
        print(f"📋 请求数据: {data}")
        
        response = requests.post(url, json=data, timeout=60)
        result = response.json()
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应内容: {result}")
        
        if result.get('success'):
            print("✅ Selenium自动登录测试成功！")
        else:
            print(f"❌ Selenium自动登录测试失败: {result.get('message')}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Selenium服务，请确保服务已启动")
    except requests.exceptions.Timeout:
        print("❌ 请求超时，登录可能需要更长时间")
    except Exception as e:
        print(f"❌ 测试出错: {e}")

def test_health():
    """测试健康检查"""
    try:
        response = requests.get("http://localhost:5001/health")
        result = response.json()
        print(f"🏥 健康检查: {result}")
        return True
    except:
        print("❌ Selenium服务未启动")
        return False

if __name__ == "__main__":
    print("🔍 测试Selenium自动登录服务")
    print("=" * 50)
    
    # 先测试健康检查
    if test_health():
        # 再测试登录功能
        test_selenium_login()
    else:
        print("请先启动Selenium服务: python selenium_auto_login.py")
