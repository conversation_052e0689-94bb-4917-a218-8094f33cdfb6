#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动登录服务
基于Selenium WebDriver实现真正的自动化登录
"""

import time
import json
from flask import Flask, request, jsonify
from flask_cors import CORS
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class AutoLoginService:
    def __init__(self):
        self.driver = None
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            
            # 禁用密码保存提示
            prefs = {
                "profile.cookie_controls_mode": 1,
                "credentials_enable_service": False, 
                "profile.password_manager_enabled": False
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.maximize_window()
            
            # 执行脚本隐藏webdriver特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return True
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def perform_login(self, url, username, password):
        """执行自动登录"""
        try:
            print(f"🚀 开始自动登录: {username}")
            
            # 打开登录页面
            print(f"🔗 访问登录页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找用户名输入框
            print("🔍 查找用户名输入框...")
            username_selectors = [
                'input[type="text"]',
                'input[name="username"]',
                'input[placeholder*="用户名"]',
                'input[placeholder*="账号"]',
                '#username',
                '.username'
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"✅ 找到用户名输入框: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not username_input:
                return {"success": False, "message": "未找到用户名输入框"}
            
            # 查找密码输入框
            print("🔍 查找密码输入框...")
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                '#password',
                '.password'
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"✅ 找到密码输入框: {selector}")
                    break
                except TimeoutException:
                    continue
            
            if not password_input:
                return {"success": False, "message": "未找到密码输入框"}
            
            # 填写用户名
            print(f"📝 填写用户名: {username}")
            username_input.clear()
            username_input.send_keys(username)
            time.sleep(0.5)
            
            # 填写密码
            print("📝 填写密码...")
            password_input.clear()
            password_input.send_keys(password)
            time.sleep(0.5)
            
            # 查找登录按钮
            print("🔍 查找登录按钮...")
            login_selectors = [
                '#login_button',
                '.login_button',
                'div[onclick*="login"]',
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("登录")',
                'div:contains("登录")',
                'a:contains("登录")'
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    if ':contains(' in selector:
                        # 使用XPath查找包含文本的元素
                        text = selector.split(':contains("')[1].split('")')[0]
                        xpath = f"//*[contains(text(), '{text}')]"
                        login_button = self.driver.find_element(By.XPATH, xpath)
                    else:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"✅ 找到登录按钮: {selector}")
                    break
                except NoSuchElementException:
                    continue
            
            # 如果还没找到，尝试通过文本查找
            if not login_button:
                try:
                    login_button = self.driver.find_element(By.XPATH, "//*[text()='登录' or text()='登 录' or text()='Login']")
                    print("✅ 通过文本找到登录按钮")
                except NoSuchElementException:
                    return {"success": False, "message": "未找到登录按钮"}
            
            # 点击登录按钮
            print("🔘 点击登录按钮...")
            try:
                login_button.click()
                print("✅ 登录按钮点击成功")
            except Exception as e:
                # 尝试JavaScript点击
                self.driver.execute_script("arguments[0].click();", login_button)
                print("✅ 通过JavaScript点击登录按钮成功")
            
            # 等待登录完成
            time.sleep(3)
            
            # 检查是否登录成功（可以根据页面特征判断）
            current_url = self.driver.current_url
            print(f"🎉 登录流程完成，当前页面: {current_url}")
            
            return {
                "success": True, 
                "message": "自动登录完成",
                "current_url": current_url
            }
            
        except Exception as e:
            print(f"❌ 自动登录失败: {e}")
            return {"success": False, "message": f"登录失败: {str(e)}"}
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            self.driver = None

# 全局服务实例
login_service = AutoLoginService()

@app.route('/auto-login', methods=['POST'])
def auto_login():
    """自动登录API接口"""
    try:
        data = request.get_json()
        url = data.get('url')
        username = data.get('username')
        password = data.get('password')
        
        if not all([url, username, password]):
            return jsonify({
                "success": False,
                "message": "缺少必要参数: url, username, password"
            })
        
        print(f"🚀 收到自动登录请求: {username} -> {url}")
        
        # 设置浏览器驱动
        if not login_service.setup_driver():
            return jsonify({
                "success": False,
                "message": "浏览器驱动设置失败，请确保已安装Chrome浏览器和ChromeDriver"
            })
        
        # 执行自动登录
        result = login_service.perform_login(url, username, password)
        
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ API处理失败: {e}")
        return jsonify({
            "success": False,
            "message": f"服务器错误: {str(e)}"
        })

@app.route('/close-browser', methods=['POST'])
def close_browser():
    """关闭浏览器API接口"""
    try:
        login_service.close_driver()
        return jsonify({"success": True, "message": "浏览器已关闭"})
    except Exception as e:
        return jsonify({"success": False, "message": f"关闭失败: {str(e)}"})

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "ok", "message": "自动登录服务运行正常"})

if __name__ == '__main__':
    print("🚀 启动自动登录服务...")
    print("📋 API接口:")
    print("   POST /auto-login - 执行自动登录")
    print("   POST /close-browser - 关闭浏览器")
    print("   GET  /health - 健康检查")
    print("🌐 服务地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
