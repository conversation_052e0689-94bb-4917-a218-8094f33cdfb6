@echo off
chcp 65001 >nul
title km测试工具合集
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    km测试工具合集                            ║
echo ║                   正在启动应用...                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Node.js是否安装
echo [1/3] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js环境正常

REM 检查依赖
echo [2/3] 检查项目依赖...
if not exist "node_modules" (
    echo 正在安装依赖，请稍候...
    npm install
)
echo ✅ 项目依赖正常

REM 构建并启动应用
echo [3/3] 启动应用...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║  应用启动成功！                                              ║
echo ║  地址: http://localhost:3000                                 ║
echo ║  浏览器将自动打开，如未打开请手动访问上述地址                ║
echo ║                                                              ║
echo ║  按 Ctrl+C 可以停止应用                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 启动应用
npm run start-app
