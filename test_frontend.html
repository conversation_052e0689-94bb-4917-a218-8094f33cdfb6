<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Selenium自动登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .button {
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>🚀 Selenium自动登录测试</h1>
    
    <div>
        <button class="button" onclick="testHealth()">测试健康检查</button>
        <button class="button" onclick="testLogin()">测试自动登录</button>
        <button class="button" onclick="closeBrowser()">关闭浏览器</button>
    </div>
    
    <div id="result"></div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        async function testHealth() {
            try {
                showResult('🔍 正在检查Selenium服务状态...', 'info');
                
                const response = await fetch('http://localhost:5001/health');
                const result = await response.json();
                
                showResult(`✅ 健康检查成功！\n${JSON.stringify(result, null, 2)}`, 'success');
            } catch (error) {
                showResult(`❌ 健康检查失败: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                showResult('🚀 正在执行Selenium自动登录...', 'info');
                
                const response = await fetch('http://localhost:5001/selenium-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: 'http://i.fkw.com.faidev.cc/',
                        username: 'sixianchan',
                        password: '9321gogogo'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult(`✅ 自动登录成功！\n${result.message}`, 'success');
                } else {
                    showResult(`❌ 自动登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 请求失败: ${error.message}\n\n可能的原因：\n1. Selenium服务未启动\n2. 网络连接问题\n3. CORS策略限制`, 'error');
            }
        }

        async function closeBrowser() {
            try {
                showResult('🔒 正在关闭浏览器...', 'info');
                
                const response = await fetch('http://localhost:5001/close-selenium', {
                    method: 'POST'
                });

                const result = await response.json();
                showResult(`✅ ${result.message}`, 'success');
            } catch (error) {
                showResult(`❌ 关闭浏览器失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
