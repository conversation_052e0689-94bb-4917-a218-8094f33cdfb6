{"name": "km-test-tools", "version": "1.0.0", "private": true, "main": "public/electron.js", "homepage": "./", "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "crypto-js": "^4.2.0", "electron-is-dev": "^3.0.1", "express": "^5.1.0", "jsencrypt": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "node server.js", "start-app": "npm run build && npm run serve", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2"}, "proxy": "http://localhost:5001", "build": {"appId": "com.km.test-tools", "productName": "km测试工具合集", "directories": {"output": "dist"}, "files": ["build/**/*", "node_modules/**/*", "public/electron.js"], "win": {"target": "nsis", "icon": "public/favicon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}