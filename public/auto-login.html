<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动登录中...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            margin: 1rem 0;
            font-size: 1.1rem;
        }
        .logs {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>🚀 自动登录中...</h2>
        <div class="status" id="status">正在准备登录...</div>
        <div class="logs" id="logs"></div>
    </div>

    <script>
        // 从URL参数获取账号信息
        const urlParams = new URLSearchParams(window.location.search);
        const username = urlParams.get('username') || 'sixianchan';
        const password = urlParams.get('password') || '9321gogogo';
        const targetUrl = urlParams.get('url') || 'http://i.fkw.com.faidev.cc/';

        const statusEl = document.getElementById('status');
        const logsEl = document.getElementById('logs');

        function log(message) {
            console.log(message);
            const time = new Date().toLocaleTimeString();
            logsEl.innerHTML += `<div>[${time}] ${message}</div>`;
            logsEl.scrollTop = logsEl.scrollHeight;
        }

        function updateStatus(message) {
            statusEl.textContent = message;
            log(message);
        }

        async function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async function performAutoLogin() {
            try {
                updateStatus('🔄 正在跳转到登录页面...');
                
                // 等待一下让用户看到状态
                await sleep(1000);
                
                // 跳转到目标页面
                window.location.href = targetUrl;
                
                // 注意：由于页面会跳转，下面的代码实际上不会执行
                // 我们需要在目标页面加载后执行登录脚本
                
            } catch (error) {
                updateStatus('❌ 跳转失败: ' + error.message);
                log('错误详情: ' + error.stack);
            }
        }

        updateStatus('🔄 准备自动登录...');

        // 显示使用说明
        setTimeout(() => {
            updateStatus('📋 请按照以下步骤完成自动登录：');
            log('1. 点击下方的"复制登录脚本"按钮');
            log('2. 在新打开的登录页面按F12打开开发者工具');
            log('3. 切换到Console（控制台）标签');
            log('4. 粘贴脚本并按回车执行');

            // 创建复制按钮和跳转按钮
            const container = document.querySelector('.container');

            // 登录脚本
            const loginScript = `
(function() {
    console.log('🚀 开始自动登录...');
    const username = '${username}';
    const password = '${password}';

    async function sleep(ms) { return new Promise(resolve => setTimeout(resolve, ms)); }

    async function autoLogin() {
        try {
            await sleep(1000);

            // 查找输入框
            const usernameInput = document.querySelector('input[type="text"]') || document.querySelector('input[placeholder*="用户名"]');
            const passwordInput = document.querySelector('input[type="password"]');

            if (!usernameInput || !passwordInput) {
                console.error('❌ 未找到输入框');
                return;
            }

            // 填写账号密码
            console.log('📝 填写账号密码...');
            usernameInput.focus();
            usernameInput.value = username;
            usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
            await sleep(300);

            passwordInput.focus();
            passwordInput.value = password;
            passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
            await sleep(300);

            // 查找登录按钮
            let loginButton = document.querySelector('#login_button') ||
                             document.querySelector('.login_button') ||
                             document.querySelector('div[onclick*="login"]');

            if (!loginButton) {
                const allElements = document.querySelectorAll('div, button, a, span');
                for (const element of allElements) {
                    if (element.textContent.trim() === '登录' || element.textContent.trim() === '登 录') {
                        loginButton = element;
                        break;
                    }
                }
            }

            if (loginButton) {
                console.log('🔘 点击登录按钮...');
                loginButton.click();
                console.log('✅ 登录完成！');
            } else {
                console.warn('⚠️ 未找到登录按钮，请手动点击');
            }
        } catch (error) {
            console.error('❌ 登录失败:', error);
        }
    }

    autoLogin();
})();`;

            // 复制脚本按钮
            const copyButton = document.createElement('button');
            copyButton.textContent = '📋 复制登录脚本';
            copyButton.style.cssText = `
                background: #4CAF50;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 10px;
            `;
            copyButton.onclick = () => {
                navigator.clipboard.writeText(loginScript).then(() => {
                    copyButton.textContent = '✅ 脚本已复制！';
                    setTimeout(() => {
                        copyButton.textContent = '📋 复制登录脚本';
                    }, 2000);
                });
            };

            // 打开登录页面按钮
            const openButton = document.createElement('button');
            openButton.textContent = '🚀 打开登录页面';
            openButton.style.cssText = `
                background: #2196F3;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 10px;
            `;
            openButton.onclick = () => {
                window.open(targetUrl, '_blank', 'width=1200,height=800');
                updateStatus('✅ 登录页面已打开，请在控制台粘贴脚本执行');
            };

            // 显示脚本内容
            const scriptDisplay = document.createElement('textarea');
            scriptDisplay.value = loginScript;
            scriptDisplay.style.cssText = `
                width: 100%;
                height: 200px;
                margin: 10px 0;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
                background: #f5f5f5;
            `;
            scriptDisplay.readOnly = true;

            container.appendChild(copyButton);
            container.appendChild(openButton);
            container.appendChild(scriptDisplay);

        }, 1000);
    </script>
</body>
</html>
