// 🚀 凡科登录自动化脚本 - 直接在登录页面控制台执行
// 基于Login.py的逻辑，适配凡科登录页面

(function() {
    console.log('🚀 开始执行凡科自动登录脚本...');
    
    // 配置账号信息 - 请根据需要修改
    const accounts = {
        'la19281': {
            username: 'la19281',
            password: 'faker0507',
            staffUsername: '',
            staffPassword: '',
            platform: '直销'
        },
        'huaedu1': {
            username: 'huaedu1', 
            password: 'faisco1234',
            staffUsername: 'boss',
            staffPassword: 'faisco1234',
            platform: '直销'
        }
    };
    
    // 默认使用la19281账号，您可以修改这里
    const currentAccount = accounts['la19281'];
    
    console.log('📋 使用账号:', currentAccount.username);
    
    // 工具函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 模拟真实输入
    async function typeText(element, text) {
        if (!element) {
            console.error('❌ 元素不存在');
            return false;
        }
        
        console.log(`📝 开始输入: ${text}`);
        
        // 聚焦元素
        element.focus();
        element.click();
        
        // 清空现有内容
        element.value = '';
        
        // 触发输入事件
        element.dispatchEvent(new Event('focus', { bubbles: true }));
        element.dispatchEvent(new Event('input', { bubbles: true }));
        
        await sleep(200);
        
        // 逐字符输入
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            
            // 触发输入事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('keydown', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
            
            await sleep(100); // 模拟真实输入速度
        }
        
        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✅ 输入完成: ${element.value}`);
        return true;
    }
    
    // 检测登录页面类型
    function detectLoginPageType() {
        // 检查是否为新版登录页面
        const newPageIndicators = [
            'input[placeholder*="请输入用户名"]',
            'input[placeholder*="请输入手机号"]',
            'input[placeholder*="用户名"]',
            '.login-form',
            '[class*="login"]'
        ];
        
        for (const selector of newPageIndicators) {
            if (document.querySelector(selector)) {
                console.log('🔍 检测到新版登录页面');
                return 'new';
            }
        }
        
        console.log('🔍 检测到旧版登录页面');
        return 'old';
    }
    
    // 查找登录元素
    function findLoginElements() {
        console.log('🔍 正在查找登录元素...');
        
        // 用户名输入框
        const usernameSelectors = [
            'input[placeholder*="用户名"]',
            'input[placeholder*="手机号"]', 
            'input[placeholder*="账号"]',
            'input[name="username"]',
            'input[name="account"]',
            'input[name="mobile"]',
            'input[type="text"]',
            '#username',
            '#account',
            '.username',
            '.account'
        ];
        
        // 密码输入框
        const passwordSelectors = [
            'input[type="password"]',
            'input[name="password"]',
            '#password',
            '.password'
        ];
        
        // 员工输入框
        const staffSelectors = [
            'input[placeholder*="员工"]',
            'input[name="staff"]',
            'input[name="employee"]',
            '.staff-input'
        ];
        
        // 员工复选框
        const checkboxSelectors = [
            'input[type="checkbox"]',
            '.checkbox input',
            '[class*="staff"] input[type="checkbox"]',
            '[class*="employee"] input[type="checkbox"]'
        ];
        
        // 登录按钮
        const buttonSelectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("登录")',
            'button:contains("登 录")',
            '.login-btn',
            '.btn-login',
            '#loginBtn',
            'button'
        ];
        
        // 查找元素
        let usernameInput = null;
        let passwordInput = null;
        let staffInput = null;
        let staffCheckbox = null;
        let loginButton = null;
        
        // 查找用户名输入框
        for (const selector of usernameSelectors) {
            usernameInput = document.querySelector(selector);
            if (usernameInput) break;
        }
        
        // 查找密码输入框
        for (const selector of passwordSelectors) {
            passwordInput = document.querySelector(selector);
            if (passwordInput) break;
        }
        
        // 查找员工输入框
        for (const selector of staffSelectors) {
            staffInput = document.querySelector(selector);
            if (staffInput) break;
        }
        
        // 查找员工复选框
        for (const selector of checkboxSelectors) {
            staffCheckbox = document.querySelector(selector);
            if (staffCheckbox) break;
        }
        
        // 查找登录按钮
        for (const selector of buttonSelectors) {
            loginButton = document.querySelector(selector);
            if (loginButton && loginButton.offsetParent !== null) break;
        }
        
        // 如果没找到登录按钮，尝试通过文本查找
        if (!loginButton) {
            const buttons = document.querySelectorAll('button');
            for (const btn of buttons) {
                if (btn.textContent.includes('登录') || btn.textContent.includes('登 录')) {
                    loginButton = btn;
                    break;
                }
            }
        }
        
        console.log('🔍 找到的元素:', {
            username: !!usernameInput,
            password: !!passwordInput,
            staff: !!staffInput,
            checkbox: !!staffCheckbox,
            button: !!loginButton
        });
        
        return {
            usernameInput,
            passwordInput,
            staffInput,
            staffCheckbox,
            loginButton
        };
    }
    
    // 关闭登录后弹窗
    async function closePopups() {
        console.log('🔍 检查并关闭弹窗...');
        
        const closeSelectors = [
            '.close',
            '.btnClose',
            '.modal-close',
            '.popup-close',
            '.dialog-close',
            '[class*="close"]',
            'button:contains("关闭")',
            'button:contains("确定")',
            'button:contains("知道了")'
        ];
        
        for (const selector of closeSelectors) {
            try {
                const closeBtn = document.querySelector(selector);
                if (closeBtn && closeBtn.offsetParent !== null) {
                    closeBtn.click();
                    console.log('✅ 已关闭弹窗');
                    await sleep(500);
                }
            } catch (error) {
                // 忽略错误
            }
        }
    }
    
    // 执行登录
    async function performLogin() {
        try {
            console.log('⏳ 等待页面加载完成...');
            await sleep(2000);
            
            const elements = findLoginElements();
            
            if (!elements.usernameInput) {
                console.error('❌ 未找到用户名输入框');
                return false;
            }
            
            if (!elements.passwordInput) {
                console.error('❌ 未找到密码输入框');
                return false;
            }
            
            if (!elements.loginButton) {
                console.error('❌ 未找到登录按钮');
                return false;
            }
            
            // 处理员工账号复选框（默认取消选中）
            if (elements.staffCheckbox && elements.staffCheckbox.checked) {
                elements.staffCheckbox.click();
                await sleep(300);
                console.log('✅ 已取消员工账号选择');
            }
            
            // 输入用户名
            console.log('📝 输入用户名...');
            await typeText(elements.usernameInput, currentAccount.username);
            await sleep(500);
            
            // 处理员工登录
            if (currentAccount.staffUsername && 
                currentAccount.staffUsername !== '' && 
                currentAccount.staffUsername !== 'boss') {
                
                console.log('👥 处理员工登录...');
                
                // 勾选员工登录
                if (elements.staffCheckbox && !elements.staffCheckbox.checked) {
                    elements.staffCheckbox.click();
                    await sleep(300);
                    console.log('✅ 已选择员工账号登录');
                }
                
                // 输入员工用户名
                if (elements.staffInput) {
                    await typeText(elements.staffInput, currentAccount.staffUsername);
                    await sleep(500);
                }
                
                // 输入员工密码
                await typeText(elements.passwordInput, currentAccount.staffPassword || currentAccount.password);
                await sleep(500);
                
            } else {
                // 普通用户登录
                console.log('👤 普通用户登录...');
                await typeText(elements.passwordInput, currentAccount.password);
                await sleep(500);
            }
            
            // 点击登录按钮
            console.log('🔘 点击登录按钮...');
            await sleep(1000);
            elements.loginButton.click();
            console.log('✅ 登录按钮已点击');
            
            // 等待登录完成
            await sleep(3000);
            
            // 关闭可能的弹窗
            await closePopups();
            
            console.log('🎉 自动登录流程完成！');
            return true;
            
        } catch (error) {
            console.error('❌ 登录过程中出错:', error);
            return false;
        }
    }
    
    // 开始执行
    console.log('🎯 开始执行自动登录...');
    performLogin();
    
})();

// 使用说明：
// 1. 打开登录页面：http://i.fkw.com.faidev.cc/
// 2. 按F12打开开发者工具
// 3. 切换到Console标签
// 4. 复制粘贴这整个脚本
// 5. 按回车执行
// 6. 观察自动登录过程
