<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试加密功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            word-break: break-all;
        }
        button {
            background: #2196F3;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔐 加密算法测试</h1>
    
    <div class="test-section">
        <h3>📝 输入测试文本</h3>
        <input type="text" id="testText" placeholder="输入要测试的文本" value="Hello, 这是测试文本！">
    </div>

    <div class="test-section">
        <h3>🔑 MD5 哈希测试</h3>
        <button onclick="testMD5()">测试 MD5</button>
        <div id="md5Result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔐 AES 加密测试</h3>
        <input type="text" id="aesKey" placeholder="输入AES密钥" value="mySecretKey123">
        <button onclick="testAES()">测试 AES 加密/解密</button>
        <div id="aesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔒 RSA 加密测试</h3>
        <button onclick="testRSA()">测试 RSA 密钥生成和加密/解密</button>
        <div id="rsaResult" class="result"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.3.2/jsencrypt.min.js"></script>
    
    <script>
        function testMD5() {
            const text = document.getElementById('testText').value;
            const hash = CryptoJS.MD5(text).toString();
            document.getElementById('md5Result').innerHTML = `
                <strong>原文:</strong> ${text}<br>
                <strong>MD5:</strong> ${hash}
            `;
        }

        function testAES() {
            const text = document.getElementById('testText').value;
            const key = document.getElementById('aesKey').value;
            
            try {
                // AES 加密
                const encrypted = CryptoJS.AES.encrypt(text, key).toString();
                
                // AES 解密
                const decrypted = CryptoJS.AES.decrypt(encrypted, key);
                const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
                
                document.getElementById('aesResult').innerHTML = `
                    <strong>原文:</strong> ${text}<br>
                    <strong>密钥:</strong> ${key}<br>
                    <strong>加密后:</strong> ${encrypted}<br>
                    <strong>解密后:</strong> ${decryptedText}<br>
                    <strong>验证:</strong> ${text === decryptedText ? '✅ 成功' : '❌ 失败'}
                `;
            } catch (error) {
                document.getElementById('aesResult').innerHTML = `❌ 错误: ${error.message}`;
            }
        }

        function testRSA() {
            const text = document.getElementById('testText').value;
            
            try {
                // 生成 RSA 密钥对
                const encrypt = new JSEncrypt({default_key_size: 1024}); // 使用较小的密钥以便测试
                const publicKey = encrypt.getPublicKey();
                const privateKey = encrypt.getPrivateKey();
                
                // RSA 加密
                const encryptObj = new JSEncrypt();
                encryptObj.setPublicKey(publicKey);
                const encrypted = encryptObj.encrypt(text);
                
                // RSA 解密
                const decryptObj = new JSEncrypt();
                decryptObj.setPrivateKey(privateKey);
                const decrypted = decryptObj.decrypt(encrypted);
                
                document.getElementById('rsaResult').innerHTML = `
                    <strong>原文:</strong> ${text}<br>
                    <strong>公钥:</strong> <textarea readonly rows="4">${publicKey}</textarea><br>
                    <strong>私钥:</strong> <textarea readonly rows="4">${privateKey}</textarea><br>
                    <strong>加密后:</strong> ${encrypted}<br>
                    <strong>解密后:</strong> ${decrypted}<br>
                    <strong>验证:</strong> ${text === decrypted ? '✅ 成功' : '❌ 失败'}
                `;
            } catch (error) {
                document.getElementById('rsaResult').innerHTML = `❌ 错误: ${error.message}`;
            }
        }

        // 页面加载时自动测试 MD5
        window.onload = function() {
            testMD5();
        };
    </script>
</body>
</html>
