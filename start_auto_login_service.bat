@echo off
echo ========================================
echo 🚀 启动自动登录服务
echo ========================================
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 📦 安装依赖包...
pip install -r requirements.txt

echo.
echo 🚀 启动自动登录服务...
echo 服务地址: http://localhost:5000
echo 按 Ctrl+C 停止服务
echo.

python auto_login_service.py

pause
