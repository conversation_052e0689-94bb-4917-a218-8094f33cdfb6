// 🚀 精确凡科登录脚本 - 基于实际页面元素
(function() {
    console.log('🚀 开始执行精确登录脚本...');
    
    // dep环境账号配置
    const account = {
        username: 'sixianchan',
        password: '9321gogogo'
    };
    
    console.log('📋 使用dep环境账号:', account.username);
    
    function sleep(ms) { return new Promise(resolve => setTimeout(resolve, ms)); }
    
    async function simulateTyping(element, text) {
        if (!element) {
            console.error('❌ 元素不存在');
            return false;
        }
        
        console.log(`📝 开始输入: ${text}`);
        
        // 聚焦元素
        element.focus();
        element.click();
        await sleep(100);
        
        // 清空现有内容
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('focus', { bubbles: true }));
        await sleep(200);
        
        // 逐字符输入
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            
            // 触发输入事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('keydown', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
            element.dispatchEvent(new Event('keypress', { bubbles: true }));
            
            await sleep(120); // 模拟真实输入速度
        }
        
        // 触发完成事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✅ 输入完成: ${element.value}`);
        await sleep(300);
        return true;
    }
    
    async function performLogin() {
        try {
            console.log('⏳ 等待页面稳定...');
            await sleep(2000);
            
            // 1. 查找用户名输入框
            console.log('🔍 查找用户名输入框...');
            const usernameInput = document.querySelector('input[type="text"]') || 
                                 document.querySelector('input[placeholder*="用户名"]') ||
                                 document.querySelector('input[placeholder*="手机号"]') ||
                                 document.querySelector('input[name="username"]') ||
                                 document.querySelector('input[name="account"]');
            
            // 2. 查找密码输入框
            console.log('🔍 查找密码输入框...');
            const passwordInput = document.querySelector('input[type="password"]');
            
            // 3. 查找登录按钮 - 使用您提供的精确信息
            console.log('🔍 查找登录按钮...');
            let loginButton = null;
            
            // 方法1: 通过ID查找
            loginButton = document.querySelector('#login_button');
            
            // 方法2: 通过class查找
            if (!loginButton) {
                loginButton = document.querySelector('.login_button');
            }
            
            // 方法3: 通过div包含的onclick属性
            if (!loginButton) {
                const divs = document.querySelectorAll('div[onclick*="login"]');
                for (const div of divs) {
                    if (div.onclick && div.onclick.toString().includes('login')) {
                        loginButton = div;
                        break;
                    }
                }
            }
            
            // 方法4: 通过文本内容查找
            if (!loginButton) {
                const allElements = document.querySelectorAll('div, button, a');
                for (const element of allElements) {
                    if (element.textContent.includes('登录') || 
                        element.textContent.includes('登 录') ||
                        element.textContent.includes('Login')) {
                        loginButton = element;
                        break;
                    }
                }
            }
            
            // 输出查找结果
            console.log('🔍 元素查找结果:', {
                username: !!usernameInput,
                password: !!passwordInput,
                button: !!loginButton,
                buttonType: loginButton ? loginButton.tagName : 'null',
                buttonId: loginButton ? loginButton.id : 'null',
                buttonClass: loginButton ? loginButton.className : 'null',
                buttonText: loginButton ? loginButton.textContent.trim() : 'null'
            });
            
            if (!usernameInput) {
                console.error('❌ 未找到用户名输入框');
                return false;
            }
            
            if (!passwordInput) {
                console.error('❌ 未找到密码输入框');
                return false;
            }
            
            // 4. 输入用户名
            console.log('📝 输入用户名...');
            await simulateTyping(usernameInput, account.username);
            
            // 5. 输入密码
            console.log('📝 输入密码...');
            await simulateTyping(passwordInput, account.password);
            
            // 6. 点击登录按钮
            if (loginButton) {
                console.log('🔘 准备点击登录按钮...');
                await sleep(1000);
                
                // 尝试多种点击方式
                try {
                    // 方式1: 直接点击
                    loginButton.click();
                    console.log('✅ 登录按钮已点击 (方式1: click)');
                } catch (e1) {
                    try {
                        // 方式2: 触发onclick事件
                        if (loginButton.onclick) {
                            loginButton.onclick();
                            console.log('✅ 登录按钮已点击 (方式2: onclick)');
                        }
                    } catch (e2) {
                        try {
                            // 方式3: 模拟鼠标点击事件
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            loginButton.dispatchEvent(clickEvent);
                            console.log('✅ 登录按钮已点击 (方式3: MouseEvent)');
                        } catch (e3) {
                            console.error('❌ 所有点击方式都失败了');
                        }
                    }
                }
                
                // 7. 等待登录结果
                console.log('⏳ 等待登录结果...');
                await sleep(3000);
                
                // 8. 检查是否有错误提示
                const errorElements = document.querySelectorAll('.error, .alert, .warning, [class*="error"], [class*="alert"]');
                let hasError = false;
                
                for (const errorElement of errorElements) {
                    if (errorElement.offsetParent !== null && errorElement.textContent.trim()) {
                        console.warn('⚠️ 发现错误提示:', errorElement.textContent.trim());
                        hasError = true;
                    }
                }
                
                if (!hasError) {
                    console.log('🎉 登录流程执行完成！');
                    
                    // 9. 尝试关闭可能的弹窗
                    await sleep(2000);
                    const closeSelectors = [
                        '.close', '.btn-close', '.modal-close',
                        'button:contains("关闭")', 'button:contains("确定")', 'button:contains("知道了")'
                    ];
                    
                    for (const selector of closeSelectors) {
                        const closeBtn = document.querySelector(selector);
                        if (closeBtn && closeBtn.offsetParent !== null) {
                            closeBtn.click();
                            console.log('✅ 已关闭弹窗');
                            await sleep(500);
                        }
                    }
                }
                
            } else {
                console.warn('⚠️ 未找到登录按钮');
                console.log('💡 账号密码已填入，请手动点击登录按钮');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ 登录过程中出错:', error);
            return false;
        }
    }
    
    // 开始执行
    console.log('🎯 开始dep环境自动登录流程...');
    performLogin().then(success => {
        if (success) {
            console.log('🎉 自动登录脚本执行完成！');
        } else {
            console.log('❌ 自动登录失败，请检查页面元素或手动登录');
        }
    });
    
})();

// 使用说明：
// 1. 确保在凡科登录页面
// 2. 按F12打开开发者工具
// 3. 切换到Console标签
// 4. 复制粘贴整个脚本
// 5. 按回车执行
// 6. 观察自动登录过程
