# km测试工具合集 - 分发说明

## 📦 打包方法

### 方法1：快速打包（推荐）
1. 双击运行 `package-app.bat`
2. 等待打包完成
3. 打包后的文件在 `km-test-tools-dist` 文件夹中

### 方法2：手动打包
```bash
# 1. 构建React应用
npm run build

# 2. 创建分发文件夹
mkdir km-test-tools-dist

# 3. 复制必要文件
copy build km-test-tools-dist/build
copy server.js km-test-tools-dist/
copy package.json km-test-tools-dist/
```

## 🚀 分发和使用

### 分发给客户端
1. 将整个 `km-test-tools-dist` 文件夹打包（zip/rar）
2. 发送给客户端
3. 客户端解压后即可使用

### 客户端使用方法
1. **前提条件**：确保电脑已安装 Node.js
   - 下载地址：https://nodejs.org/
   - 建议安装 LTS 版本

2. **启动应用**：
   - 双击 `启动km测试工具合集.bat`
   - 等待浏览器自动打开
   - 如果浏览器未自动打开，手动访问：http://localhost:3000

3. **停止应用**：
   - 在命令行窗口按 `Ctrl+C`
   - 或直接关闭命令行窗口

## 🔧 技术说明

### 应用架构
- **前端**：React + Tailwind CSS
- **后端**：Node.js HTTP服务器
- **加密库**：crypto-js, jsencrypt
- **自动化**：Selenium WebDriver

### 功能模块
1. **账号管理** - Selenium自动登录
2. **时间处理** - 时间戳转换、格式化
3. **编码/加密** - MD5、SHA、AES、DES、RSA等
4. **文件处理** - 图片处理工具
5. **字数统计** - 文本分析
6. **数据生成** - 随机数据生成
7. **JSON数据对比** - JSON格式化和对比
8. **随机题目** - 题库管理和随机出题
9. **正则测试** - 正则表达式测试

### 端口说明
- 默认端口：3000
- 如果端口被占用，可以修改 `server.js` 中的 `PORT` 变量

## 🛠️ 故障排除

### 常见问题

1. **Node.js未安装**
   - 错误：`'node' 不是内部或外部命令`
   - 解决：安装Node.js并重启电脑

2. **端口被占用**
   - 错误：`Error: listen EADDRINUSE :::3000`
   - 解决：关闭占用3000端口的程序，或修改端口

3. **浏览器未自动打开**
   - 手动访问：http://localhost:3000

4. **加密功能异常**
   - 检查输入格式是否正确
   - 查看浏览器控制台错误信息

### 技术支持
如遇到其他问题，请检查：
1. Node.js版本（建议v16+）
2. 防火墙设置
3. 杀毒软件拦截

## 📋 版本信息
- 版本：1.0.0
- 构建时间：2024年
- 支持系统：Windows（需要Node.js）

## 🔄 更新说明
如需更新应用：
1. 重新打包最新版本
2. 替换客户端的文件夹
3. 重新启动应用
