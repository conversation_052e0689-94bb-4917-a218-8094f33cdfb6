import React, { useState } from 'react';
import Header from './components/Header';
import CategoryNav from './components/CategoryNav';
import ToolSection from './components/ToolSection';

// 导入各个工具组件
import DataGenerator from './components/tools/DataGenerator';
import TextCounter from './components/tools/TextCounter';
import TextCompare from './components/tools/TextCompare';
import RegexTester from './components/tools/RegexTester';
import JsonCompare from './components/tools/JsonCompare';
import TimeTools from './components/tools/TimeTools';
import LogAnalyzer from './components/tools/LogAnalyzer';
import ImageProcessor from './components/tools/ImageProcessor';
import EncodingTools from './components/tools/EncodingTools';
import RandomQuestions from './components/tools/RandomQuestions';
import AccountManager from './components/tools/AccountManager';

function App() {
  const [activeCategory, setActiveCategory] = useState('数据生成工具');

  const categories = [
    { id: 'data-generator', name: '数据生成工具', component: DataGenerator },
    { id: 'text-counter', name: '字数统计', component: TextCounter },
    { id: 'text-compare', name: '文本对比', component: TextCompare },
    { id: 'regex-tester', name: '正则测试', component: RegexTester },
    { id: 'json-compare', name: 'JSON数据对比', component: JsonCompare },
    { id: 'time-tools', name: '时间处理工具', component: TimeTools },
    { id: 'log-analyzer', name: '日志分析工具', component: LogAnalyzer },
    { id: 'image-processor', name: '图片处理工具', component: ImageProcessor },
    { id: 'encoding-tools', name: '编码/加密工具', component: EncodingTools },
    { id: 'random-questions', name: '随机题目', component: RandomQuestions },
    { id: 'account-manager', name: '账号管理', component: AccountManager },
  ];

  const activeComponent = categories.find(cat => cat.name === activeCategory)?.component;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">km测试工具合集</h1>
          <p className="text-xl text-gray-600">一站式数据生成和处理工具集</p>
        </div>
        
        <CategoryNav 
          categories={categories}
          activeCategory={activeCategory}
          onCategoryChange={setActiveCategory}
        />
        
        <ToolSection>
          {activeComponent && React.createElement(activeComponent)}
        </ToolSection>
      </main>
    </div>
  );
}

export default App;
