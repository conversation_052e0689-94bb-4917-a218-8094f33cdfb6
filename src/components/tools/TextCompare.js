import React, { useState } from 'react';

const TextCompare = () => {
  const [textA, setTextA] = useState('');
  const [textB, setTextB] = useState('');
  const [compareOptions, setCompareOptions] = useState({
    ignoreCase: false,
    ignoreWhitespace: false,
    ignoreEmptyLines: false
  });
  const [compareResult, setCompareResult] = useState(null);

  const processText = (text) => {
    let processed = text;
    
    if (compareOptions.ignoreCase) {
      processed = processed.toLowerCase();
    }
    
    if (compareOptions.ignoreWhitespace) {
      processed = processed.replace(/\s+/g, ' ').trim();
    }
    
    if (compareOptions.ignoreEmptyLines) {
      processed = processed.split('\n').filter(line => line.trim() !== '').join('\n');
    }
    
    return processed;
  };

  const compareTexts = () => {
    const processedA = processText(textA);
    const processedB = processText(textB);
    
    const linesA = processedA.split('\n');
    const linesB = processedB.split('\n');
    
    const maxLines = Math.max(linesA.length, linesB.length);
    const differences = [];
    let sameLines = 0;
    let differentLines = 0;
    
    for (let i = 0; i < maxLines; i++) {
      const lineA = linesA[i] || '';
      const lineB = linesB[i] || '';
      
      if (lineA === lineB) {
        sameLines++;
        differences.push({
          lineNumber: i + 1,
          type: 'same',
          lineA,
          lineB
        });
      } else {
        differentLines++;
        differences.push({
          lineNumber: i + 1,
          type: 'different',
          lineA,
          lineB
        });
      }
    }
    
    const similarity = maxLines > 0 ? Math.round((sameLines / maxLines) * 100) : 0;
    
    setCompareResult({
      sameLines,
      differentLines,
      similarity,
      differences,
      statsA: {
        lines: linesA.length,
        characters: processedA.length
      },
      statsB: {
        lines: linesB.length,
        characters: processedB.length
      }
    });
  };

  const clearAll = () => {
    setTextA('');
    setTextB('');
    setCompareResult(null);
  };

  const copyDifferenceReport = () => {
    if (!compareResult) return;
    
    let report = `文本对比报告\n`;
    report += `================\n`;
    report += `相同行数: ${compareResult.sameLines}\n`;
    report += `不同行数: ${compareResult.differentLines}\n`;
    report += `相似度: ${compareResult.similarity}%\n\n`;
    report += `差异详情:\n`;
    
    compareResult.differences.forEach(diff => {
      if (diff.type === 'different') {
        report += `行 ${diff.lineNumber}:\n`;
        report += `  文本A: ${diff.lineA}\n`;
        report += `  文本B: ${diff.lineB}\n\n`;
      }
    });
    
    navigator.clipboard.writeText(report);
  };

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">文本内容对比工具</h2>
      
      {/* 对比选项 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-medium text-gray-700 mb-3">对比选项</h3>
        <div className="flex flex-wrap gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={compareOptions.ignoreCase}
              onChange={(e) => setCompareOptions(prev => ({ ...prev, ignoreCase: e.target.checked }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">忽略大小写</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={compareOptions.ignoreWhitespace}
              onChange={(e) => setCompareOptions(prev => ({ ...prev, ignoreWhitespace: e.target.checked }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">忽略空白字符</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={compareOptions.ignoreEmptyLines}
              onChange={(e) => setCompareOptions(prev => ({ ...prev, ignoreEmptyLines: e.target.checked }))}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">忽略空行</span>
          </label>
        </div>
      </div>
      
      {/* 文本输入区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-gray-800">文本A</h3>
            <div className="text-sm text-gray-500">
              行数: {textA.split('\n').length} 字符数: {textA.length}
            </div>
          </div>
          <textarea
            value={textA}
            onChange={(e) => setTextA(e.target.value)}
            className="textarea-field h-64"
            placeholder="请输入第一个文本..."
          />
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-gray-800">文本B</h3>
            <div className="text-sm text-gray-500">
              行数: {textB.split('\n').length} 字符数: {textB.length}
            </div>
          </div>
          <textarea
            value={textB}
            onChange={(e) => setTextB(e.target.value)}
            className="textarea-field h-64"
            placeholder="请输入第二个文本..."
          />
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex gap-3 mb-6">
        <button onClick={compareTexts} className="btn-primary">
          重新对比
        </button>
        <button onClick={clearAll} className="btn-secondary">
          清空所有
        </button>
        <button 
          onClick={copyDifferenceReport} 
          className="btn-success"
          disabled={!compareResult}
        >
          复制差异报告
        </button>
      </div>
      
      {/* 对比结果 */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">对比结果</h3>
        
        {compareResult ? (
          <div className="space-y-6">
            {/* 统计信息 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-sm text-green-600 font-medium">相同行数</div>
                <div className="text-2xl font-bold text-green-800">{compareResult.sameLines}</div>
              </div>
              
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-sm text-red-600 font-medium">不同行数</div>
                <div className="text-2xl font-bold text-red-800">{compareResult.differentLines}</div>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-sm text-blue-600 font-medium">相似度</div>
                <div className="text-2xl font-bold text-blue-800">{compareResult.similarity}%</div>
              </div>
            </div>
            
            {/* 差异详情 */}
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-3">差异详情</h4>
              <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                {compareResult.differences.map((diff, index) => (
                  <div 
                    key={index}
                    className={`p-3 border-b border-gray-100 ${
                      diff.type === 'same' ? 'bg-green-50' : 'bg-red-50'
                    }`}
                  >
                    <div className="text-xs text-gray-500 mb-1">行 {diff.lineNumber}</div>
                    {diff.type === 'different' ? (
                      <div className="space-y-1">
                        <div className="text-sm">
                          <span className="text-red-600 font-medium">A: </span>
                          <span className="font-mono">{diff.lineA || '(空行)'}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-blue-600 font-medium">B: </span>
                          <span className="font-mono">{diff.lineB || '(空行)'}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="text-sm text-green-700 font-mono">
                        {diff.lineA || '(空行)'}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            输入两个文本进行对比，差异将在这里显示
          </div>
        )}
      </div>
    </div>
  );
};

export default TextCompare;
