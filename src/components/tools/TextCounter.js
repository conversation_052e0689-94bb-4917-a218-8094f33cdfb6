import React, { useState, useEffect } from 'react';

const TextCounter = () => {
  const [text, setText] = useState('');
  const [stats, setStats] = useState({
    charactersWithSpaces: 0,
    charactersWithoutSpaces: 0,
    words: 0,
    lines: 0,
    paragraphs: 0
  });

  useEffect(() => {
    calculateStats(text);
  }, [text]);

  const calculateStats = (inputText) => {
    const charactersWithSpaces = inputText.length;
    const charactersWithoutSpaces = inputText.replace(/\s/g, '').length;
    
    // 计算单词数（支持中英文）
    const words = inputText.trim() === '' ? 0 : 
      inputText.trim().split(/\s+/).filter(word => word.length > 0).length;
    
    // 计算行数
    const lines = inputText === '' ? 0 : inputText.split('\n').length;
    
    // 计算段落数（以空行分隔）
    const paragraphs = inputText.trim() === '' ? 0 : 
      inputText.trim().split(/\n\s*\n/).filter(para => para.trim().length > 0).length;

    setStats({
      charactersWithSpaces,
      charactersWithoutSpaces,
      words,
      lines,
      paragraphs
    });
  };

  const clearText = () => {
    setText('');
  };

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">字数统计工具</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 输入区域 */}
        <div className="lg:col-span-2">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              输入文本：
            </label>
            <textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="textarea-field h-96"
              placeholder="请在此输入需要统计的文本内容..."
            />
          </div>
          
          <div className="flex gap-3">
            <button 
              onClick={clearText}
              className="btn-secondary"
            >
              清空文本
            </button>
          </div>
        </div>
        
        {/* 统计结果区域 */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">统计结果</h3>
          
          <div className="space-y-3">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="text-sm text-blue-600 font-medium">字符数（含空格）</div>
              <div className="text-2xl font-bold text-blue-800">
                {stats.charactersWithSpaces.toLocaleString()}
              </div>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="text-sm text-green-600 font-medium">字符数（不含空格）</div>
              <div className="text-2xl font-bold text-green-800">
                {stats.charactersWithoutSpaces.toLocaleString()}
              </div>
            </div>
            
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="text-sm text-purple-600 font-medium">单词数</div>
              <div className="text-2xl font-bold text-purple-800">
                {stats.words.toLocaleString()}
              </div>
            </div>
            
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="text-sm text-orange-600 font-medium">行数</div>
              <div className="text-2xl font-bold text-orange-800">
                {stats.lines.toLocaleString()}
              </div>
            </div>
            
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-sm text-red-600 font-medium">段落数</div>
              <div className="text-2xl font-bold text-red-800">
                {stats.paragraphs.toLocaleString()}
              </div>
            </div>
          </div>
          
          {/* 额外信息 */}
          {text.length > 0 && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">额外信息</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <div>平均每行字符数: {stats.lines > 0 ? Math.round(stats.charactersWithSpaces / stats.lines) : 0}</div>
                <div>平均每段字符数: {stats.paragraphs > 0 ? Math.round(stats.charactersWithSpaces / stats.paragraphs) : 0}</div>
                <div>平均每段单词数: {stats.paragraphs > 0 ? Math.round(stats.words / stats.paragraphs) : 0}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextCounter;
