import React, { useState } from 'react';

const TimeTools = () => {
  const [activeTab, setActiveTab] = useState('timestamp');
  const [timestamp, setTimestamp] = useState('');
  const [dateTime, setDateTime] = useState('');
  const [convertResult, setConvertResult] = useState('');

  const getCurrentTimestamp = () => {
    const now = Math.floor(Date.now() / 1000);
    setTimestamp(now.toString());
    setConvertResult(new Date(now * 1000).toLocaleString('zh-CN'));
  };

  const timestampToDate = () => {
    try {
      const ts = parseInt(timestamp);
      const date = new Date(ts * 1000);
      setConvertResult(date.toLocaleString('zh-CN'));
    } catch (error) {
      setConvertResult('转换失败：请输入有效的时间戳');
    }
  };

  const dateToTimestamp = () => {
    try {
      const date = new Date(dateTime);
      const ts = Math.floor(date.getTime() / 1000);
      setConvertResult(ts.toString());
    } catch (error) {
      setConvertResult('转换失败：请输入有效的日期时间');
    }
  };

  const tabs = [
    { id: 'timestamp', name: '时间戳转换' },
    { id: 'calculator', name: '时间换算工具' },
    { id: 'date-calc', name: '日期计算器' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">时间处理工具</h2>
      
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {activeTab === 'timestamp' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">时间戳转日期</h3>
                <div className="space-y-3">
                  <input
                    type="text"
                    value={timestamp}
                    onChange={(e) => setTimestamp(e.target.value)}
                    placeholder="输入时间戳（秒）"
                    className="input-field"
                  />
                  <button onClick={timestampToDate} className="btn-primary w-full">
                    转换为日期
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">日期转时间戳</h3>
                <div className="space-y-3">
                  <input
                    type="datetime-local"
                    value={dateTime}
                    onChange={(e) => setDateTime(e.target.value)}
                    className="input-field"
                  />
                  <button onClick={dateToTimestamp} className="btn-primary w-full">
                    转换为时间戳
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">当前时间戳</h3>
                <button onClick={getCurrentTimestamp} className="btn-success w-full">
                  获取当前时间戳
                </button>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">转换结果</h3>
              <div className="result-area h-32">
                {convertResult || '转换结果将在这里显示'}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'calculator' && (
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-800 mb-4">常用时间换算表</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div>1分钟 = 60秒</div>
                <div>1小时 = 60分钟 = 3,600秒</div>
                <div>1天 = 24小时 = 1,440分钟</div>
                <div>1周 = 7天 = 168小时</div>
              </div>
              <div className="space-y-2">
                <div>1月 ≈ 30天 = 720小时</div>
                <div>1年 = 365天 = 8,760小时</div>
                <div>1年 = 52周 = 12个月</div>
                <div>1闰年 = 366天 = 8,784小时</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'date-calc' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">日期加减计算</h3>
              <div className="space-y-3">
                <input
                  type="date"
                  className="input-field"
                  placeholder="起始日期"
                />
                <div className="flex gap-2">
                  <select className="select-field">
                    <option value="add">加上</option>
                    <option value="subtract">减去</option>
                  </select>
                  <input
                    type="number"
                    className="input-field"
                    placeholder="数量"
                  />
                  <select className="select-field">
                    <option value="days">天</option>
                    <option value="weeks">周</option>
                    <option value="months">月</option>
                    <option value="years">年</option>
                  </select>
                </div>
                <button className="btn-primary w-full">计算</button>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">日期间隔计算</h3>
              <div className="space-y-3">
                <input
                  type="date"
                  className="input-field"
                  placeholder="开始日期"
                />
                <input
                  type="date"
                  className="input-field"
                  placeholder="结束日期"
                />
                <button className="btn-primary w-full">计算间隔</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TimeTools;
