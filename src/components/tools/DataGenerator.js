import React, { useState } from 'react';

const DataGenerator = () => {
  const [activeTab, setActiveTab] = useState('random-content');
  const [results, setResults] = useState({});

  // 随机内容生成器
  const [contentConfig, setContentConfig] = useState({
    wordCount: 100,
    language: '中文',
    contentType: 'Lorem ipsum'
  });

  // 随机邮箱生成器
  const [emailConfig, setEmailConfig] = useState({
    count: 10,
    domain: 'gmail.com'
  });

  // 随机电话号码生成器
  const [phoneConfig, setPhoneConfig] = useState({
    count: 10,
    type: '手机号码'
  });

  // 随机地址生成器
  const [addressConfig, setAddressConfig] = useState({
    count: 10,
    type: '完整地址'
  });

  // 随机身份证生成器
  const [idCardConfig, setIdCardConfig] = useState({
    count: 10,
    gender: '随机',
    ageMin: 18,
    ageMax: 65,
    region: '随机地区'
  });

  const generateRandomContent = () => {
    const contents = {
      'Lorem ipsum': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      '文章段落': '这是一个示例文章段落，包含了丰富的内容和信息...',
      '故事内容': '从前有一个小村庄，村庄里住着一位善良的老人...',
      '技术文档': '本文档介绍了系统的架构设计和实现方案...',
      '商务文本': '尊敬的客户，感谢您对我们产品的关注和支持...',
      '日常对话': '你好，今天天气真不错，适合出去走走...'
    };
    
    const baseContent = contents[contentConfig.contentType] || contents['Lorem ipsum'];
    const words = baseContent.split(' ');
    const result = words.slice(0, Math.min(contentConfig.wordCount, words.length)).join(' ');
    
    setResults(prev => ({ ...prev, randomContent: result }));
  };

  const generateEmails = () => {
    const emails = [];
    const domains = ['gmail.com', '163.com', 'qq.com', 'hotmail.com', 'yahoo.com'];
    const selectedDomain = emailConfig.domain;
    
    for (let i = 0; i < emailConfig.count; i++) {
      const username = `user${Math.floor(Math.random() * 10000)}`;
      emails.push(`${username}@${selectedDomain}`);
    }
    
    setResults(prev => ({ ...prev, emails: emails.join('\n') }));
  };

  const generatePhones = () => {
    const phones = [];
    
    for (let i = 0; i < phoneConfig.count; i++) {
      if (phoneConfig.type === '手机号码') {
        const prefix = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139'][Math.floor(Math.random() * 10)];
        const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
        phones.push(`${prefix}${suffix}`);
      } else {
        const areaCode = ['010', '021', '022', '023', '024', '025'][Math.floor(Math.random() * 6)];
        const number = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
        phones.push(`${areaCode}-${number}`);
      }
    }
    
    setResults(prev => ({ ...prev, phones: phones.join('\n') }));
  };

  const generateAddresses = () => {
    const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市'];
    const districts = ['朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区'];
    const streets = ['中山路', '人民路', '建设路', '解放路', '和平路', '友谊路'];
    
    const addresses = [];
    
    for (let i = 0; i < addressConfig.count; i++) {
      const city = cities[Math.floor(Math.random() * cities.length)];
      const district = districts[Math.floor(Math.random() * districts.length)];
      const street = streets[Math.floor(Math.random() * streets.length)];
      const number = Math.floor(Math.random() * 999) + 1;
      
      if (addressConfig.type === '完整地址') {
        addresses.push(`${city}${district}${street}${number}号`);
      } else if (addressConfig.type === '仅城市') {
        addresses.push(city);
      } else {
        addresses.push(`${street}${number}号`);
      }
    }
    
    setResults(prev => ({ ...prev, addresses: addresses.join('\n') }));
  };

  const generateIdCards = () => {
    const idCards = [];
    const regions = ['110000', '120000', '130000', '140000', '150000'];
    
    for (let i = 0; i < idCardConfig.count; i++) {
      const region = idCardConfig.region === '随机地区' ? 
        regions[Math.floor(Math.random() * regions.length)] : '110000';
      const year = new Date().getFullYear() - Math.floor(Math.random() * (idCardConfig.ageMax - idCardConfig.ageMin)) - idCardConfig.ageMin;
      const month = Math.floor(Math.random() * 12) + 1;
      const day = Math.floor(Math.random() * 28) + 1;
      const sequence = Math.floor(Math.random() * 999).toString().padStart(3, '0');
      const genderDigit = idCardConfig.gender === '男性' ? '1' : 
                         idCardConfig.gender === '女性' ? '0' : 
                         Math.floor(Math.random() * 2).toString();
      
      const idCard = `${region}${year}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}${sequence}${genderDigit}`;
      idCards.push(idCard);
    }
    
    setResults(prev => ({ ...prev, idCards: idCards.join('\n') }));
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  const tabs = [
    { id: 'random-content', name: '随机内容生成器' },
    { id: 'email-generator', name: '随机邮箱生成器' },
    { id: 'phone-generator', name: '随机电话号码生成器' },
    { id: 'address-generator', name: '随机地址生成器' },
    { id: 'id-generator', name: '随机身份证生成器' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">数据生成工具</h2>
      
      {/* 标签页导航 */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {/* 随机内容生成器 */}
      {activeTab === 'random-content' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">生成字数：</label>
              <input
                type="number"
                value={contentConfig.wordCount}
                onChange={(e) => setContentConfig(prev => ({ ...prev, wordCount: parseInt(e.target.value) || 100 }))}
                className="input-field"
                min="1"
                max="10000"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">语言选择：</label>
              <select
                value={contentConfig.language}
                onChange={(e) => setContentConfig(prev => ({ ...prev, language: e.target.value }))}
                className="select-field"
              >
                <option value="中文">中文</option>
                <option value="英文">英文</option>
                <option value="日文">日文</option>
                <option value="韩文">韩文</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">内容类型：</label>
              <select
                value={contentConfig.contentType}
                onChange={(e) => setContentConfig(prev => ({ ...prev, contentType: e.target.value }))}
                className="select-field"
              >
                <option value="Lorem ipsum">Lorem ipsum</option>
                <option value="文章段落">文章段落</option>
                <option value="故事内容">故事内容</option>
                <option value="技术文档">技术文档</option>
                <option value="商务文本">商务文本</option>
                <option value="日常对话">日常对话</option>
              </select>
            </div>
            
            <div className="flex gap-3">
              <button onClick={generateRandomContent} className="btn-primary">
                生成内容
              </button>
              <button 
                onClick={() => copyToClipboard(results.randomContent || '')} 
                className="btn-secondary"
                disabled={!results.randomContent}
              >
                复制结果
              </button>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
            <textarea
              value={results.randomContent || ''}
              readOnly
              className="result-area h-64"
              placeholder="生成的内容将在这里显示..."
            />
          </div>
        </div>
      )}

      {/* 其他生成器的实现类似，这里简化显示 */}
      {activeTab === 'email-generator' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">邮箱数量：</label>
              <input
                type="number"
                value={emailConfig.count}
                onChange={(e) => setEmailConfig(prev => ({ ...prev, count: parseInt(e.target.value) || 10 }))}
                className="input-field"
                min="1"
                max="1000"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">域名选择：</label>
              <select
                value={emailConfig.domain}
                onChange={(e) => setEmailConfig(prev => ({ ...prev, domain: e.target.value }))}
                className="select-field"
              >
                <option value="gmail.com">gmail.com</option>
                <option value="163.com">163.com</option>
                <option value="qq.com">qq.com</option>
                <option value="hotmail.com">hotmail.com</option>
                <option value="yahoo.com">yahoo.com</option>
              </select>
            </div>
            
            <div className="flex gap-3">
              <button onClick={generateEmails} className="btn-primary">
                生成邮箱
              </button>
              <button 
                onClick={() => copyToClipboard(results.emails || '')} 
                className="btn-secondary"
                disabled={!results.emails}
              >
                复制结果
              </button>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
            <textarea
              value={results.emails || ''}
              readOnly
              className="result-area h-64"
              placeholder="生成的邮箱将在这里显示..."
            />
          </div>
        </div>
      )}

      {/* 继续添加其他标签页的内容... */}
    </div>
  );
};

export default DataGenerator;
