import React, { useState, useEffect, useMemo, useCallback } from 'react';
import AutoLoginService from '../../utils/autoLogin';
import seleniumAutoLoginService from '../../utils/seleniumAutoLogin';

const AccountManager = () => {
  const [accounts, setAccounts] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState('直销');
  const [selectedEnvironment, setSelectedEnvironment] = useState('线上');
  const [newAccount, setNewAccount] = useState({
    platform: '直销',
    environment: '线上',
    username: '',
    password: '',
    staffUsername: '',
    staffPassword: '',
    url: '',
    note: ''
  });
  const [activeTab, setActiveTab] = useState('manager');
  const [searchTerm, setSearchTerm] = useState('');

  // 预设的平台数据
  const platforms = {
    '直销': {
      environments: ['线上', 'dep', '本地'],
      urls: {
        '线上': 'http://i.flw.com/',
        'dep': 'http://i.fkw.com.faidev.cc/',
        '本地': 'http://i.fff.com/'
      }
    },
    'OEM': {
      environments: ['线上', '本地'],
      urls: {
        '线上': 'http://adm.webportal.top/',
        '本地': 'http://www.app.top/'
      }
    },
    '代理商平台': {
      environments: ['线上', '本地'],
      urls: {
        '线上': 'http://it.faisco.cn',
        '本地': 'http://tk.faisco.cn/index'
      }
    }
  };

  // 预设账号数据
  const defaultAccounts = useMemo(() => [
    {
      id: 1,
      platform: '直销',
      environment: '线上',
      username: 'dannell',
      password: 'faisco1234',
      staffUsername: 'boss',
      staffPassword: 'faisco1234',
      url: 'http://i.flw.com/',
      note: 'aid: ********'
    },
    {
      id: 2,
      platform: '直销',
      environment: '线上',
      username: 'ywinger',
      password: 'faisco1234',
      staffUsername: 'yaowj',
      staffPassword: 'faisco1234',
      url: 'http://i.flw.com/',
      note: 'aid:******** 灰度账号'
    },
    {
      id: 6,
      platform: '直销',
      environment: 'dep',
      username: 'huaedu1',
      password: 'faisco1234',
      staffUsername: 'boss',
      staffPassword: 'faisco1234',
      url: 'http://i.fkw.com.faidev.cc/',
      note: 'dep环境测试账号'
    },
    {
      id: 7,
      platform: '直销',
      environment: 'dep',
      username: 'la19281',
      password: 'faker0507',
      staffUsername: '',
      staffPassword: '',
      url: 'http://i.fkw.com.faidev.cc/',
      note: '测试账号la19281'
    },
    {
      id: 3,
      platform: 'OEM',
      environment: '线上',
      username: 'testmall6',
      password: 'faisco1234',
      staffUsername: 'fuji',
      staffPassword: 'faisco1234',
      url: 'http://adm.webportal.top/',
      note: 'aid: ********'
    },
    {
      id: 4,
      platform: '代理商平台',
      environment: '线上',
      username: 'faidev',
      password: 'fai6508',
      staffUsername: 'boss',
      staffPassword: '',
      url: 'http://it.faisco.cn',
      note: 'aid: 1591627 灰度账号'
    },

  ], []);

  // 更新账号URL
  const updateAccountUrls = useCallback((accountList) => {
    return accountList.map(account => ({
      ...account,
      url: platforms[account.platform]?.urls[account.environment] || account.url
    }));
  }, [platforms]);

  // 初始化数据
  useEffect(() => {
    const savedAccounts = localStorage.getItem('accountManager');
    if (savedAccounts) {
      const parsedAccounts = JSON.parse(savedAccounts);
      // 更新URL以确保使用最新的配置
      const updatedAccounts = updateAccountUrls(parsedAccounts);
      setAccounts(updatedAccounts);
      // 保存更新后的账号信息
      localStorage.setItem('accountManager', JSON.stringify(updatedAccounts));
    } else {
      const updatedDefaultAccounts = updateAccountUrls(defaultAccounts);
      setAccounts(updatedDefaultAccounts);
      localStorage.setItem('accountManager', JSON.stringify(updatedDefaultAccounts));
    }
  }, [defaultAccounts, updateAccountUrls]);

  // 保存到本地存储
  const saveToStorage = (accountList) => {
    localStorage.setItem('accountManager', JSON.stringify(accountList));
  };

  // 添加账号
  const addAccount = () => {
    if (!newAccount.username || !newAccount.password) {
      alert('请填写用户名和密码');
      return;
    }

    const account = {
      ...newAccount,
      id: Date.now(),
      url: platforms[newAccount.platform]?.urls[newAccount.environment] || ''
    };

    const updatedAccounts = [...accounts, account];
    setAccounts(updatedAccounts);
    saveToStorage(updatedAccounts);

    // 重置表单
    setNewAccount({
      platform: '直销',
      environment: '线上',
      username: '',
      password: '',
      staffUsername: '',
      staffPassword: '',
      url: '',
      note: ''
    });

    alert('账号添加成功');
  };

  // 删除账号
  const deleteAccount = (id) => {
    if (window.confirm('确定要删除这个账号吗？')) {
      const updatedAccounts = accounts.filter(account => account.id !== id);
      setAccounts(updatedAccounts);
      saveToStorage(updatedAccounts);
    }
  };

  // 强制Selenium自动登录
  const forceSeleniumLogin = async (account) => {
    if (!account.url) {
      alert('该账号没有配置网址');
      return;
    }

    try {
      console.log('🚀 强制执行Selenium自动登录...', account);

      // 显示加载提示
      const loadingMsg = '正在执行自动登录，请稍候...';
      const originalAlert = window.alert;

      // 创建一个临时的加载提示
      const loadingDiv = document.createElement('div');
      loadingDiv.innerHTML = `
        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                    background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10000; text-align: center;">
          <div style="margin-bottom: 10px;">🚀 正在执行自动登录...</div>
          <div style="font-size: 12px; color: #666;">账号: ${account.username}</div>
          <div style="font-size: 12px; color: #666;">环境: ${account.platform} - ${account.environment}</div>
        </div>
      `;
      document.body.appendChild(loadingDiv);

      try {
        const result = await seleniumAutoLoginService.performAutoLogin(account);

        document.body.removeChild(loadingDiv);

        if (result.success) {
          alert('✅ 强制自动登录成功！\n\n如果页面没有自动登录，请检查浏览器控制台的提示信息。');
        } else {
          alert('⚠️ 自动登录遇到问题，请查看浏览器控制台获取详细信息。');
        }
      } catch (error) {
        document.body.removeChild(loadingDiv);
        console.error('强制自动登录失败:', error);
        alert(`❌ 强制自动登录失败: ${error.message}\n\n请尝试手动登录或检查网络连接。`);
      }

    } catch (error) {
      console.error('强制自动登录出错:', error);
      alert(`❌ 执行失败: ${error.message}`);
    }
  };

  // 复制登录脚本
  const copyLoginScript = (account) => {
    const script = `// 🚀 ${account.username} 自动登录脚本
(function() {
    console.log('🚀 开始执行自动登录脚本...');

    // 账号配置
    const account = {
        username: '${account.username}',
        password: '${account.password}',
        staffUsername: '${account.staffUsername || ''}',
        staffPassword: '${account.staffPassword || ''}',
        platform: '${account.platform}'
    };

    console.log('📋 使用账号:', account.username);

    // 工具函数
    function sleep(ms) { return new Promise(resolve => setTimeout(resolve, ms)); }

    async function typeText(element, text) {
        if (!element) return false;
        console.log('📝 输入:', text);
        element.focus();
        element.click();
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
        await sleep(200);
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await sleep(100);
        }
        element.dispatchEvent(new Event('change', { bubbles: true }));
        console.log('✅ 输入完成:', element.value);
        return true;
    }

    function findElements() {
        const usernameInput = document.querySelector('input[placeholder*="用户名"]') ||
                             document.querySelector('input[placeholder*="手机号"]') ||
                             document.querySelector('input[name="username"]') ||
                             document.querySelectorAll('input[type="text"]')[0];

        const passwordInput = document.querySelector('input[type="password"]');

        const staffInput = document.querySelector('input[placeholder*="员工"]') ||
                          document.querySelectorAll('input[type="text"]')[1];

        const staffCheckbox = document.querySelector('input[type="checkbox"]');

        const loginButton = document.querySelector('button[type="submit"]') ||
                           document.querySelector('.login-btn') ||
                           document.querySelector('button');

        console.log('🔍 找到元素:', {
            username: !!usernameInput,
            password: !!passwordInput,
            staff: !!staffInput,
            checkbox: !!staffCheckbox,
            button: !!loginButton
        });

        return { usernameInput, passwordInput, staffInput, staffCheckbox, loginButton };
    }

    async function performLogin() {
        try {
            await sleep(2000);
            const elements = findElements();

            if (!elements.usernameInput || !elements.passwordInput || !elements.loginButton) {
                console.error('❌ 缺少必要的登录元素');
                return;
            }

            // 处理员工复选框
            if (elements.staffCheckbox && elements.staffCheckbox.checked) {
                elements.staffCheckbox.click();
                await sleep(300);
                console.log('✅ 已取消员工账号选择');
            }

            // 输入用户名
            await typeText(elements.usernameInput, account.username);
            await sleep(500);

            // 处理员工登录
            if (account.staffUsername && account.staffUsername !== '' && account.staffUsername !== 'boss') {
                if (elements.staffCheckbox && !elements.staffCheckbox.checked) {
                    elements.staffCheckbox.click();
                    await sleep(300);
                    console.log('✅ 已选择员工账号登录');
                }
                if (elements.staffInput) {
                    await typeText(elements.staffInput, account.staffUsername);
                    await sleep(500);
                }
                await typeText(elements.passwordInput, account.staffPassword || account.password);
            } else {
                await typeText(elements.passwordInput, account.password);
            }

            await sleep(1000);
            elements.loginButton.click();
            console.log('✅ 登录按钮已点击');

            // 等待并关闭弹窗
            await sleep(3000);
            const closeSelectors = ['.close', '.btnClose', '.modal-close', 'button:contains("关闭")'];
            for (const selector of closeSelectors) {
                const closeBtn = document.querySelector(selector);
                if (closeBtn && closeBtn.offsetParent !== null) {
                    closeBtn.click();
                    console.log('✅ 已关闭弹窗');
                    await sleep(500);
                }
            }

            console.log('🎉 自动登录完成！');
        } catch (error) {
            console.error('❌ 登录失败:', error);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', performLogin);
    } else {
        performLogin();
    }
})();`;

    navigator.clipboard.writeText(script).then(() => {
      alert(`✅ ${account.username} 的登录脚本已复制到剪贴板！

📋 使用方法：
1. 打开登录页面：${account.url}
2. 按F12打开开发者工具
3. 切换到Console标签
4. 粘贴脚本并按回车执行
5. 观察自动登录过程

🎯 这个脚本会自动输入账号密码并点击登录！`);
    }).catch(() => {
      alert('复制失败，请手动复制脚本');
    });
  };

  // 复制账号信息
  const copyAccountInfo = (account) => {
    const info = `平台: ${account.platform}
环境: ${account.environment}
用户名: ${account.username}
密码: ${account.password}
员工账号: ${account.staffUsername}
员工密码: ${account.staffPassword}
网址: ${account.url}
备注: ${account.note}`;

    navigator.clipboard.writeText(info);
    alert('账号信息已复制到剪贴板');
  };

  // 注入自动登录脚本的函数
  const injectAutoLoginScript = (targetWindow, account) => {
    try {
      console.log('🚀 开始注入自动登录脚本...', account.username);

      const script = `
        (function() {
          console.log('🚀 自动登录脚本已加载，开始执行...');

          const account = \${JSON.stringify(account)};
          console.log('📋 账号信息:', account.username);

          function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
          }

          async function fillAndSubmit() {
            try {
              console.log('🔍 开始查找页面元素...');
              await sleep(1000);

              // 查找用户名输入框
              let usernameInput = document.querySelector('input[type="text"]') ||
                                 document.querySelector('input[name="username"]') ||
                                 document.querySelector('input[placeholder*="用户名"]') ||
                                 document.querySelector('input[placeholder*="账号"]');

              // 查找密码输入框
              let passwordInput = document.querySelector('input[type="password"]') ||
                                 document.querySelector('input[name="password"]');

              console.log('🔍 元素查找结果:', {
                username: !!usernameInput,
                password: !!passwordInput
              });

              if (!usernameInput || !passwordInput) {
                console.error('❌ 未找到必要的输入框');
                return;
              }

              // 填写用户名
              console.log('📝 填写用户名...');
              usernameInput.focus();
              usernameInput.value = account.username;
              usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
              usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
              await sleep(500);

              // 填写密码
              console.log('📝 填写密码...');
              passwordInput.focus();
              passwordInput.value = account.password;
              passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
              passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
              await sleep(500);

              // 查找并点击登录按钮
              console.log('🔍 查找登录按钮...');
              let loginButton = document.querySelector('#login_button') ||
                               document.querySelector('.login_button') ||
                               document.querySelector('div[onclick*="login"]') ||
                               document.querySelector('button[type="submit"]') ||
                               document.querySelector('input[type="submit"]');

              // 如果还没找到，通过文本查找
              if (!loginButton) {
                const allElements = document.querySelectorAll('div, button, a, span');
                for (const element of allElements) {
                  const text = element.textContent.trim();
                  if (text === '登录' || text === '登 录' || text === 'Login') {
                    loginButton = element;
                    console.log('🔍 通过文本找到登录按钮:', text);
                    break;
                  }
                }
              }

              if (loginButton) {
                console.log('🔘 找到登录按钮，准备点击...');
                await sleep(500);

                // 尝试点击
                try {
                  loginButton.click();
                  console.log('✅ 登录按钮点击成功');
                } catch (e1) {
                  try {
                    if (loginButton.onclick) {
                      loginButton.onclick();
                      console.log('✅ 通过onclick点击成功');
                    }
                  } catch (e2) {
                    const event = new MouseEvent('click', { bubbles: true, cancelable: true });
                    loginButton.dispatchEvent(event);
                    console.log('✅ 通过事件分发点击成功');
                  }
                }

                console.log('🎉 自动登录流程完成！');
              } else {
                console.warn('⚠️ 未找到登录按钮，请手动点击登录');
              }

            } catch (error) {
              console.error('❌ 自动登录过程出错:', error);
            }
          }

          // 等待页面加载完成
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
              setTimeout(fillAndSubmit, 1000);
            });
          } else {
            setTimeout(fillAndSubmit, 1000);
          }

        })();
      `;

      // 尝试注入脚本
      try {
        targetWindow.eval(script);
        console.log('✅ 脚本注入成功 (eval方式)');
        return true;
      } catch (error1) {
        try {
          const scriptElement = targetWindow.document.createElement('script');
          scriptElement.textContent = script;
          targetWindow.document.head.appendChild(scriptElement);
          console.log('✅ 脚本注入成功 (script标签方式)');
          return true;
        } catch (error2) {
          console.error('❌ 脚本注入失败:', error2);
          return false;
        }
      }

    } catch (error) {
      console.error('❌ 注入过程出错:', error);
      return false;
    }
  };

  // 快速登录
  const quickLogin = async (account) => {
    if (!account.url) {
      alert('该账号没有配置网址');
      return;
    }

    try {
      console.log('🚀 开始执行自动登录...', account);

      // 显示加载提示
      const loadingDiv = document.createElement('div');
      loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px 40px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 10000;
        text-align: center;
        font-size: 16px;
      `;
      loadingDiv.innerHTML = `
        <div style="margin-bottom: 15px;">🚀 正在启动自动登录...</div>
        <div style="font-size: 14px; color: #666;">请稍候，浏览器将自动打开并完成登录</div>
      `;
      document.body.appendChild(loadingDiv);

      // 调用Python后端服务进行自动登录
      const response = await fetch('http://localhost:5000/auto-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: account.url,
          username: account.username,
          password: account.password
        })
      });

      // 移除加载提示
      document.body.removeChild(loadingDiv);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        alert('✅ 自动登录成功！浏览器已打开并完成登录。');
        console.log('✅ 自动登录成功:', result);
      } else {
        alert(`❌ 自动登录失败: ${result.message}`);
        console.error('❌ 自动登录失败:', result);
      }

    } catch (error) {
      console.error('快速登录出错:', error);

      // 移除可能存在的加载提示
      const loadingDiv = document.querySelector('div[style*="position: fixed"]');
      if (loadingDiv) {
        document.body.removeChild(loadingDiv);
      }

      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        alert('❌ 无法连接到自动登录服务！\n\n请确保：\n1. Python自动登录服务已启动\n2. 服务运行在 http://localhost:5000\n3. 已安装必要的依赖包');
      } else {
        alert('快速登录失败: ' + error.message);
      }
    }
  };

  // 筛选账号
  const filteredAccounts = accounts.filter(account => {
    const matchesPlatform = selectedPlatform === '全部' || account.platform === selectedPlatform;
    const matchesEnvironment = selectedEnvironment === '全部' || account.environment === selectedEnvironment;
    const matchesSearch = !searchTerm ||
      account.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.platform.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.note.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesPlatform && matchesEnvironment && matchesSearch;
  });

  // 导出账号数据
  const exportAccounts = () => {
    const dataStr = JSON.stringify(accounts, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `账号数据_${new Date().toISOString().slice(0, 10)}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // 导入账号数据
  const importAccounts = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedAccounts = JSON.parse(e.target.result);
          setAccounts(importedAccounts);
          saveToStorage(importedAccounts);
          alert('账号数据导入成功');
        } catch (error) {
          alert('导入失败，请检查文件格式');
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { id: 'manager', name: '账号管理' },
    { id: 'add', name: '添加账号' },
    { id: 'settings', name: '设置' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">常用账号登录管理</h2>
      
      {/* 标签页导航 */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {/* 账号管理 */}
      {activeTab === 'manager' && (
        <div className="space-y-6">
          {/* 筛选器 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">平台：</label>
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  className="select-field"
                >
                  <option value="全部">全部平台</option>
                  {Object.keys(platforms).map(platform => (
                    <option key={platform} value={platform}>{platform}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">环境：</label>
                <select
                  value={selectedEnvironment}
                  onChange={(e) => setSelectedEnvironment(e.target.value)}
                  className="select-field"
                >
                  <option value="全部">全部环境</option>
                  <option value="线上">线上</option>
                  <option value="dep">dep</option>
                  <option value="本地">本地</option>
                  <option value="开发环境">开发环境</option>
                  <option value="测试环境">测试环境</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">搜索：</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field"
                  placeholder="搜索用户名、平台或备注..."
                />
              </div>
            </div>

            <div className="text-sm text-gray-600">
              共找到 {filteredAccounts.length} 个账号
            </div>
          </div>

          {/* 账号列表 */}
          <div className="space-y-3">
            {filteredAccounts.map(account => (
              <div key={account.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                  <div>
                    <div className="font-medium text-gray-800">{account.platform}</div>
                    <div className="text-sm text-gray-500">{account.environment}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">用户名</div>
                    <div className="font-medium">{account.username}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">密码</div>
                    <div className="font-medium text-xs">{account.password}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">员工账号</div>
                    <div className="font-medium">{account.staffUsername || '-'}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">备注</div>
                    <div className="text-sm text-gray-700">{account.note || '-'}</div>
                  </div>

                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => quickLogin(account)}
                      className="btn-primary text-xs"
                      title="自动填写账号密码并登录"
                    >
                      自动登录
                    </button>
                    <button
                      onClick={() => forceSeleniumLogin(account)}
                      className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                      title="使用Selenium强制自动登录（推荐）"
                    >
                      强制登录
                    </button>
                    <button
                      onClick={() => copyLoginScript(account)}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-xs transition-colors"
                      title="复制登录脚本，在登录页面控制台执行"
                    >
                      复制脚本
                    </button>
                    <button
                      onClick={() => copyAccountInfo(account)}
                      className="btn-secondary text-xs"
                    >
                      复制信息
                    </button>
                    <button
                      onClick={() => deleteAccount(account.id)}
                      className="btn-danger text-xs"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {filteredAccounts.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                没有找到匹配的账号
              </div>
            )}
          </div>
        </div>
      )}

      {/* 添加账号 */}
      {activeTab === 'add' && (
        <div className="max-w-2xl mx-auto space-y-6">
          <h3 className="text-lg font-medium text-gray-800">添加新账号</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">平台：</label>
              <select
                value={newAccount.platform}
                onChange={(e) => setNewAccount(prev => ({ 
                  ...prev, 
                  platform: e.target.value,
                  url: platforms[e.target.value]?.urls[prev.environment] || ''
                }))}
                className="select-field"
              >
                {Object.keys(platforms).map(platform => (
                  <option key={platform} value={platform}>{platform}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">环境：</label>
              <select
                value={newAccount.environment}
                onChange={(e) => setNewAccount(prev => ({ 
                  ...prev, 
                  environment: e.target.value,
                  url: platforms[prev.platform]?.urls[e.target.value] || ''
                }))}
                className="select-field"
              >
                {platforms[newAccount.platform]?.environments.map(env => (
                  <option key={env} value={env}>{env}</option>
                ))}
              </select>
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名：*</label>
              <input
                type="text"
                value={newAccount.username}
                onChange={(e) => setNewAccount(prev => ({ ...prev, username: e.target.value }))}
                className="input-field"
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码：*</label>
              <input
                type="password"
                value={newAccount.password}
                onChange={(e) => setNewAccount(prev => ({ ...prev, password: e.target.value }))}
                className="input-field"
                placeholder="请输入密码"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">员工账号：</label>
              <input
                type="text"
                value={newAccount.staffUsername}
                onChange={(e) => setNewAccount(prev => ({ ...prev, staffUsername: e.target.value }))}
                className="input-field"
                placeholder="请输入员工账号"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">员工密码：</label>
              <input
                type="password"
                value={newAccount.staffPassword}
                onChange={(e) => setNewAccount(prev => ({ ...prev, staffPassword: e.target.value }))}
                className="input-field"
                placeholder="请输入员工密码"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">网址：</label>
              <input
                type="url"
                value={newAccount.url}
                onChange={(e) => setNewAccount(prev => ({ ...prev, url: e.target.value }))}
                className="input-field"
                placeholder="自动填充或手动输入网址"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">备注：</label>
              <textarea
                value={newAccount.note}
                onChange={(e) => setNewAccount(prev => ({ ...prev, note: e.target.value }))}
                className="textarea-field h-20"
                placeholder="请输入备注信息，如aid等"
              />
            </div>
          </div>

          <div className="flex justify-center">
            <button onClick={addAccount} className="btn-primary px-8">
              添加账号
            </button>
          </div>
        </div>
      )}

      {/* 设置 */}
      {activeTab === 'settings' && (
        <div className="max-w-2xl mx-auto space-y-6">
          <h3 className="text-lg font-medium text-gray-800">数据管理</h3>
          
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">数据导出</h4>
              <p className="text-sm text-yellow-700 mb-3">
                导出所有账号数据为JSON文件，可用于备份或迁移。
              </p>
              <button onClick={exportAccounts} className="btn-secondary">
                导出账号数据
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">数据导入</h4>
              <p className="text-sm text-blue-700 mb-3">
                从JSON文件导入账号数据，将覆盖当前所有数据。
              </p>
              <input
                type="file"
                accept=".json"
                onChange={importAccounts}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-800 mb-2">重置数据</h4>
              <p className="text-sm text-red-700 mb-3">
                清除所有账号数据并恢复默认账号，此操作不可撤销。
              </p>
              <button
                onClick={() => {
                  if (window.confirm('确定要重置所有数据吗？此操作不可撤销！')) {
                    setAccounts(defaultAccounts);
                    saveToStorage(defaultAccounts);
                    alert('数据已重置');
                  }
                }}
                className="btn-danger"
              >
                重置为默认数据
              </button>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <h4 className="font-medium text-gray-800 mb-3">使用说明</h4>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• 账号数据保存在浏览器本地存储中</p>
              <p>• 支持按平台、环境、A/B分组筛选账号</p>
              <p>• 快速登录功能会在新标签页打开对应网址</p>
              <p>• 复制功能会将账号信息复制到剪贴板</p>
              <p>• 建议定期导出数据进行备份</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountManager;
