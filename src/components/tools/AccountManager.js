import React, { useState, useEffect, useMemo } from 'react';
import AutoLogin from '../../utils/autoLogin';

const AccountManager = () => {
  const [accounts, setAccounts] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState('直销');
  const [selectedEnvironment, setSelectedEnvironment] = useState('线上');
  const [newAccount, setNewAccount] = useState({
    platform: '直销',
    environment: '线上',
    username: '',
    password: '',
    staffUsername: '',
    staffPassword: '',
    url: '',
    note: ''
  });
  const [activeTab, setActiveTab] = useState('manager');
  const [searchTerm, setSearchTerm] = useState('');

  // 预设的平台数据
  const platforms = {
    '直销': {
      environments: ['线上', 'dep', '本地'],
      urls: {
        '线上': 'http://i.flw.com/',
        'dep': 'http://i.flw.com.faidev.cc/',
        '本地': 'http://i.fff.com/'
      }
    },
    'OEM': {
      environments: ['线上', '本地'],
      urls: {
        '线上': 'http://adm.webportal.top/',
        '本地': 'http://www.app.top/'
      }
    },
    '代理商平台': {
      environments: ['线上', '本地'],
      urls: {
        '线上': 'http://it.faisco.cn',
        '本地': 'http://tk.faisco.cn/index'
      }
    },
    'OSS': {
      environments: ['线上', '本地'],
      urls: {
        '线上': 'http://o.flw.com/',
        '本地': 'http://o.aaa.cn/'
      }
    },
    '独立环境': {
      environments: ['开发环境', '测试环境'],
      urls: {
        '开发环境': 'http://www.aaa.cn.fktest.dev.cc',
        '测试环境': 'http://i.fff.com..dev.cc'
      }
    }
  };

  // 预设账号数据
  const defaultAccounts = useMemo(() => [
    {
      id: 1,
      platform: '直销',
      environment: '线上',
      username: 'dannell',
      password: 'faisco1234',
      staffUsername: 'boss',
      staffPassword: 'faisco1234',
      url: 'http://i.flw.com/',
      note: 'aid: ********'
    },
    {
      id: 2,
      platform: '直销',
      environment: '线上',
      username: 'ywinger',
      password: 'faisco1234',
      staffUsername: 'yaowj',
      staffPassword: 'faisco1234',
      url: 'http://i.flw.com/',
      note: 'aid:******** 灰度账号'
    },
    {
      id: 6,
      platform: '直销',
      environment: 'dep',
      username: 'huaedu1',
      password: 'faisco1234',
      staffUsername: 'boss',
      staffPassword: 'faisco1234',
      url: 'http://i.flw.com.faidev.cc/',
      note: 'dep环境测试账号'
    },
    {
      id: 3,
      platform: 'OEM',
      environment: '线上',
      username: 'testmall6',
      password: 'faisco1234',
      staffUsername: 'fuji',
      staffPassword: 'faisco1234',
      url: 'http://adm.webportal.top/',
      note: 'aid: 14556184'
    },
    {
      id: 4,
      platform: '代理商平台',
      environment: '线上',
      username: 'faidev',
      password: 'fai6508',
      staffUsername: 'boss',
      staffPassword: '',
      url: 'http://it.faisco.cn',
      note: 'aid: 1591627 灰度账号'
    },
    {
      id: 5,
      platform: 'OSS',
      environment: '线上',
      username: 'admin',
      password: 'faisco',
      staffUsername: '',
      staffPassword: '',
      url: 'http://o.flw.com/',
      note: ''
    }
  ], []);

  // 初始化数据
  useEffect(() => {
    const savedAccounts = localStorage.getItem('accountManager');
    if (savedAccounts) {
      setAccounts(JSON.parse(savedAccounts));
    } else {
      setAccounts(defaultAccounts);
      localStorage.setItem('accountManager', JSON.stringify(defaultAccounts));
    }
  }, [defaultAccounts]);

  // 保存到本地存储
  const saveToStorage = (accountList) => {
    localStorage.setItem('accountManager', JSON.stringify(accountList));
  };

  // 添加账号
  const addAccount = () => {
    if (!newAccount.username || !newAccount.password) {
      alert('请填写用户名和密码');
      return;
    }

    const account = {
      ...newAccount,
      id: Date.now(),
      url: platforms[newAccount.platform]?.urls[newAccount.environment] || ''
    };

    const updatedAccounts = [...accounts, account];
    setAccounts(updatedAccounts);
    saveToStorage(updatedAccounts);

    // 重置表单
    setNewAccount({
      platform: '直销',
      environment: '线上',
      username: '',
      password: '',
      staffUsername: '',
      staffPassword: '',
      url: '',
      note: ''
    });

    alert('账号添加成功');
  };

  // 删除账号
  const deleteAccount = (id) => {
    if (window.confirm('确定要删除这个账号吗？')) {
      const updatedAccounts = accounts.filter(account => account.id !== id);
      setAccounts(updatedAccounts);
      saveToStorage(updatedAccounts);
    }
  };

  // 复制账号信息
  const copyAccountInfo = (account) => {
    const info = `平台: ${account.platform}
环境: ${account.environment}
用户名: ${account.username}
密码: ${account.password}
员工账号: ${account.staffUsername}
员工密码: ${account.staffPassword}
网址: ${account.url}
备注: ${account.note}`;

    navigator.clipboard.writeText(info);
    alert('账号信息已复制到剪贴板');
  };

  // 快速登录
  const quickLogin = async (account) => {
    if (!account.url) {
      alert('该账号没有配置网址');
      return;
    }

    try {
      // 打开新窗口
      const newWindow = window.open(account.url, '_blank');

      if (!newWindow) {
        alert('无法打开新窗口，请检查浏览器弹窗设置');
        return;
      }

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 注入自动登录脚本到新窗口
      const script = `
        // 自动登录脚本
        (function() {
          const account = ${JSON.stringify(account)};

          // 等待页面完全加载
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', performAutoLogin);
          } else {
            performAutoLogin();
          }

          async function performAutoLogin() {
            try {
              console.log('开始自动登录...', account);

              // 根据平台执行不同的登录逻辑
              const result = await executeLogin(account);

              if (result.success) {
                console.log('自动登录成功:', result.message);
                showNotification('自动登录成功', 'success');
              } else {
                console.warn('自动登录失败:', result.message);
                showNotification('自动登录失败: ' + result.message, 'error');
              }
            } catch (error) {
              console.error('自动登录出错:', error);
              showNotification('自动登录出错: ' + error.message, 'error');
            }
          }

          async function executeLogin(account) {
            const { platform, username, password, staffUsername, staffPassword } = account;

            // 等待页面元素加载
            await waitForPageLoad();

            switch (platform) {
              case '直销':
                return await loginFKW(account);
              case 'OEM':
                return await loginOEM(account);
              case '代理商平台':
                return await loginAgency(account);
              case 'OSS':
                return await loginOSS(account);
              default:
                return { success: false, message: '不支持的平台: ' + platform };
            }
          }

          async function waitForPageLoad() {
            return new Promise((resolve) => {
              if (document.readyState === 'complete') {
                setTimeout(resolve, 1000); // 额外等待1秒确保页面完全渲染
              } else {
                window.addEventListener('load', () => {
                  setTimeout(resolve, 1000);
                });
              }
            });
          }

          async function waitForElement(selector, timeout = 5000) {
            return new Promise((resolve, reject) => {
              const startTime = Date.now();

              const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                  resolve(element);
                } else if (Date.now() - startTime > timeout) {
                  reject(new Error('Element not found: ' + selector));
                } else {
                  setTimeout(checkElement, 100);
                }
              };

              checkElement();
            });
          }

          async function fillInput(selector, value) {
            try {
              const element = await waitForElement(selector, 3000);
              if (element && value) {
                element.value = '';
                element.focus();
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                return true;
              }
            } catch (error) {
              console.warn('Failed to fill input:', selector, error);
              return false;
            }
          }

          async function clickElement(selector) {
            try {
              const element = await waitForElement(selector, 3000);
              if (element) {
                element.click();
                return true;
              }
            } catch (error) {
              console.warn('Failed to click element:', selector, error);
              return false;
            }
          }

          async function loginFKW(account) {
            const { username, password, staffUsername, staffPassword } = account;

            try {
              // 尝试新版登录页面
              const usernameSelectors = [
                'input[name="username"]',
                'input[placeholder*="用户名"]',
                'input[placeholder*="账号"]',
                '#username'
              ];

              const passwordSelectors = [
                'input[name="password"]',
                'input[type="password"]',
                '#password'
              ];

              const loginButtonSelectors = [
                'button[type="submit"]',
                '.login-btn',
                '.btn-login',
                '#loginBtn'
              ];

              // 填写用户名
              let filled = false;
              for (const selector of usernameSelectors) {
                if (await fillInput(selector, username)) {
                  filled = true;
                  break;
                }
              }

              if (!filled) {
                return { success: false, message: '未找到用户名输入框' };
              }

              // 处理员工登录
              if (staffUsername && staffUsername !== 'boss') {
                const staffCheckboxSelectors = [
                  'input[type="checkbox"][name*="staff"]',
                  '.staff-checkbox input',
                  'input[name="isStaff"]'
                ];

                for (const selector of staffCheckboxSelectors) {
                  if (await clickElement(selector)) break;
                }

                const staffInputSelectors = [
                  'input[name="staff"]',
                  'input[placeholder*="员工"]'
                ];

                for (const selector of staffInputSelectors) {
                  if (await fillInput(selector, staffUsername)) break;
                }

                // 使用员工密码
                for (const selector of passwordSelectors) {
                  if (await fillInput(selector, staffPassword || password)) break;
                }
              } else {
                // 普通登录
                for (const selector of passwordSelectors) {
                  if (await fillInput(selector, password)) break;
                }
              }

              // 点击登录按钮
              await new Promise(resolve => setTimeout(resolve, 500));
              for (const selector of loginButtonSelectors) {
                if (await clickElement(selector)) break;
              }

              // 等待登录完成并关闭弹窗
              setTimeout(closePopups, 3000);

              return { success: true, message: '登录请求已发送' };
            } catch (error) {
              return { success: false, message: '登录失败: ' + error.message };
            }
          }

          async function loginOEM(account) {
            // OEM登录逻辑类似，但选择器可能不同
            return await loginFKW(account); // 暂时复用直销登录逻辑
          }

          async function loginAgency(account) {
            const { username, password } = account;

            try {
              // 代理商平台特殊处理
              const agencyTypeSelectors = [
                'input[value="agency"]',
                '.agency-login'
              ];

              for (const selector of agencyTypeSelectors) {
                if (await clickElement(selector)) break;
              }

              await fillInput('input[name="username"], input[placeholder*="代理商"]', username);
              await fillInput('input[name="password"], input[type="password"]', password);
              await clickElement('button[type="submit"], .login-btn');

              return { success: true, message: '登录请求已发送' };
            } catch (error) {
              return { success: false, message: '登录失败: ' + error.message };
            }
          }

          async function loginOSS(account) {
            const { username, password } = account;

            try {
              await fillInput('input[name="username"], input[placeholder*="用户名"]', username);
              await fillInput('input[name="password"], input[type="password"]', password);
              await clickElement('button[type="submit"], .login-btn');

              return { success: true, message: '登录请求已发送' };
            } catch (error) {
              return { success: false, message: '登录失败: ' + error.message };
            }
          }

          function closePopups() {
            const popupSelectors = [
              '.close', '.btnClose', '.popup-close',
              '[class*="close"]', '[class*="modal"] .close',
              '.layui-layer-close', '.el-dialog__close'
            ];

            popupSelectors.forEach(selector => {
              const elements = document.querySelectorAll(selector);
              elements.forEach(element => {
                if (element.offsetParent !== null) {
                  element.click();
                }
              });
            });
          }

          function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = \`
              position: fixed;
              top: 20px;
              right: 20px;
              padding: 12px 20px;
              border-radius: 4px;
              color: white;
              font-size: 14px;
              z-index: 10000;
              max-width: 300px;
              word-wrap: break-word;
              background-color: \${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
            \`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
              if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
              }
            }, 3000);
          }
        })();
      `;

      // 尝试注入脚本
      try {
        newWindow.eval(script);
      } catch (error) {
        console.warn('无法注入自动登录脚本，可能是跨域限制:', error);
        alert('已打开登录页面，请手动输入账号密码登录');
      }

    } catch (error) {
      console.error('快速登录出错:', error);
      alert('快速登录失败: ' + error.message);
    }
  };

  // 筛选账号
  const filteredAccounts = accounts.filter(account => {
    const matchesPlatform = selectedPlatform === '全部' || account.platform === selectedPlatform;
    const matchesEnvironment = selectedEnvironment === '全部' || account.environment === selectedEnvironment;
    const matchesSearch = !searchTerm ||
      account.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.platform.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.note.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesPlatform && matchesEnvironment && matchesSearch;
  });

  // 导出账号数据
  const exportAccounts = () => {
    const dataStr = JSON.stringify(accounts, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `账号数据_${new Date().toISOString().slice(0, 10)}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // 导入账号数据
  const importAccounts = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedAccounts = JSON.parse(e.target.result);
          setAccounts(importedAccounts);
          saveToStorage(importedAccounts);
          alert('账号数据导入成功');
        } catch (error) {
          alert('导入失败，请检查文件格式');
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { id: 'manager', name: '账号管理' },
    { id: 'add', name: '添加账号' },
    { id: 'settings', name: '设置' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">常用账号登录管理</h2>
      
      {/* 标签页导航 */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {/* 账号管理 */}
      {activeTab === 'manager' && (
        <div className="space-y-6">
          {/* 筛选器 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">平台：</label>
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  className="select-field"
                >
                  <option value="全部">全部平台</option>
                  {Object.keys(platforms).map(platform => (
                    <option key={platform} value={platform}>{platform}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">环境：</label>
                <select
                  value={selectedEnvironment}
                  onChange={(e) => setSelectedEnvironment(e.target.value)}
                  className="select-field"
                >
                  <option value="全部">全部环境</option>
                  <option value="线上">线上</option>
                  <option value="dep">dep</option>
                  <option value="本地">本地</option>
                  <option value="开发环境">开发环境</option>
                  <option value="测试环境">测试环境</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">搜索：</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field"
                  placeholder="搜索用户名、平台或备注..."
                />
              </div>
            </div>

            <div className="text-sm text-gray-600">
              共找到 {filteredAccounts.length} 个账号
            </div>
          </div>

          {/* 账号列表 */}
          <div className="space-y-3">
            {filteredAccounts.map(account => (
              <div key={account.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                  <div>
                    <div className="font-medium text-gray-800">{account.platform}</div>
                    <div className="text-sm text-gray-500">{account.environment}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">用户名</div>
                    <div className="font-medium">{account.username}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">密码</div>
                    <div className="font-medium">{'*'.repeat(account.password.length)}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">员工账号</div>
                    <div className="font-medium">{account.staffUsername || '-'}</div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-600">备注</div>
                    <div className="text-sm text-gray-700">{account.note || '-'}</div>
                  </div>

                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => quickLogin(account)}
                      className="btn-primary text-xs"
                      title="自动填写账号密码并登录"
                    >
                      自动登录
                    </button>
                    <button
                      onClick={() => copyAccountInfo(account)}
                      className="btn-secondary text-xs"
                    >
                      复制信息
                    </button>
                    <button
                      onClick={() => deleteAccount(account.id)}
                      className="btn-danger text-xs"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {filteredAccounts.length === 0 && (
              <div className="text-center py-12 text-gray-500">
                没有找到匹配的账号
              </div>
            )}
          </div>
        </div>
      )}

      {/* 添加账号 */}
      {activeTab === 'add' && (
        <div className="max-w-2xl mx-auto space-y-6">
          <h3 className="text-lg font-medium text-gray-800">添加新账号</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">平台：</label>
              <select
                value={newAccount.platform}
                onChange={(e) => setNewAccount(prev => ({ 
                  ...prev, 
                  platform: e.target.value,
                  url: platforms[e.target.value]?.urls[prev.environment] || ''
                }))}
                className="select-field"
              >
                {Object.keys(platforms).map(platform => (
                  <option key={platform} value={platform}>{platform}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">环境：</label>
              <select
                value={newAccount.environment}
                onChange={(e) => setNewAccount(prev => ({ 
                  ...prev, 
                  environment: e.target.value,
                  url: platforms[prev.platform]?.urls[e.target.value] || ''
                }))}
                className="select-field"
              >
                {platforms[newAccount.platform]?.environments.map(env => (
                  <option key={env} value={env}>{env}</option>
                ))}
              </select>
            </div>



            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名：*</label>
              <input
                type="text"
                value={newAccount.username}
                onChange={(e) => setNewAccount(prev => ({ ...prev, username: e.target.value }))}
                className="input-field"
                placeholder="请输入用户名"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码：*</label>
              <input
                type="password"
                value={newAccount.password}
                onChange={(e) => setNewAccount(prev => ({ ...prev, password: e.target.value }))}
                className="input-field"
                placeholder="请输入密码"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">员工账号：</label>
              <input
                type="text"
                value={newAccount.staffUsername}
                onChange={(e) => setNewAccount(prev => ({ ...prev, staffUsername: e.target.value }))}
                className="input-field"
                placeholder="请输入员工账号"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">员工密码：</label>
              <input
                type="password"
                value={newAccount.staffPassword}
                onChange={(e) => setNewAccount(prev => ({ ...prev, staffPassword: e.target.value }))}
                className="input-field"
                placeholder="请输入员工密码"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">网址：</label>
              <input
                type="url"
                value={newAccount.url}
                onChange={(e) => setNewAccount(prev => ({ ...prev, url: e.target.value }))}
                className="input-field"
                placeholder="自动填充或手动输入网址"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">备注：</label>
              <textarea
                value={newAccount.note}
                onChange={(e) => setNewAccount(prev => ({ ...prev, note: e.target.value }))}
                className="textarea-field h-20"
                placeholder="请输入备注信息，如aid等"
              />
            </div>
          </div>

          <div className="flex justify-center">
            <button onClick={addAccount} className="btn-primary px-8">
              添加账号
            </button>
          </div>
        </div>
      )}

      {/* 设置 */}
      {activeTab === 'settings' && (
        <div className="max-w-2xl mx-auto space-y-6">
          <h3 className="text-lg font-medium text-gray-800">数据管理</h3>
          
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">数据导出</h4>
              <p className="text-sm text-yellow-700 mb-3">
                导出所有账号数据为JSON文件，可用于备份或迁移。
              </p>
              <button onClick={exportAccounts} className="btn-secondary">
                导出账号数据
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">数据导入</h4>
              <p className="text-sm text-blue-700 mb-3">
                从JSON文件导入账号数据，将覆盖当前所有数据。
              </p>
              <input
                type="file"
                accept=".json"
                onChange={importAccounts}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-800 mb-2">重置数据</h4>
              <p className="text-sm text-red-700 mb-3">
                清除所有账号数据并恢复默认账号，此操作不可撤销。
              </p>
              <button
                onClick={() => {
                  if (window.confirm('确定要重置所有数据吗？此操作不可撤销！')) {
                    setAccounts(defaultAccounts);
                    saveToStorage(defaultAccounts);
                    alert('数据已重置');
                  }
                }}
                className="btn-danger"
              >
                重置为默认数据
              </button>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-6">
            <h4 className="font-medium text-gray-800 mb-3">使用说明</h4>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• 账号数据保存在浏览器本地存储中</p>
              <p>• 支持按平台、环境、A/B分组筛选账号</p>
              <p>• 快速登录功能会在新标签页打开对应网址</p>
              <p>• 复制功能会将账号信息复制到剪贴板</p>
              <p>• 建议定期导出数据进行备份</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountManager;
