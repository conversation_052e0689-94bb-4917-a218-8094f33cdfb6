import React, { useState } from 'react';

const JsonCompare = () => {
  const [jsonA, setJsonA] = useState('');
  const [jsonB, setJsonB] = useState('');
  const [activeTab, setActiveTab] = useState('differences');
  const [result, setResult] = useState({
    statusA: '待验证',
    statusB: '待验证',
    compareStatus: '待对比',
    differences: [],
    structure: null,
    summary: {
      sameFields: 0,
      differentFields: 0,
      newFields: 0,
      deletedFields: 0
    }
  });

  const formatJson = (jsonString, setter) => {
    try {
      const parsed = JSON.parse(jsonString);
      const formatted = JSON.stringify(parsed, null, 2);
      setter(formatted);
    } catch (error) {
      // 如果解析失败，不做任何操作
    }
  };

  const validateJson = (jsonString) => {
    try {
      JSON.parse(jsonString);
      return { isValid: true, error: null };
    } catch (error) {
      return { isValid: false, error: error.message };
    }
  };

  const compareJsonObjects = (objA, objB, path = '') => {
    const differences = [];
    const allKeys = new Set([...Object.keys(objA || {}), ...Object.keys(objB || {})]);

    allKeys.forEach(key => {
      const currentPath = path ? `${path}.${key}` : key;
      const valueA = objA?.[key];
      const valueB = objB?.[key];

      if (valueA === undefined) {
        differences.push({
          path: currentPath,
          type: 'added',
          valueA: undefined,
          valueB: valueB
        });
      } else if (valueB === undefined) {
        differences.push({
          path: currentPath,
          type: 'deleted',
          valueA: valueA,
          valueB: undefined
        });
      } else if (typeof valueA === 'object' && typeof valueB === 'object' && 
                 valueA !== null && valueB !== null && 
                 !Array.isArray(valueA) && !Array.isArray(valueB)) {
        // 递归比较对象
        differences.push(...compareJsonObjects(valueA, valueB, currentPath));
      } else if (JSON.stringify(valueA) !== JSON.stringify(valueB)) {
        differences.push({
          path: currentPath,
          type: 'modified',
          valueA: valueA,
          valueB: valueB
        });
      } else {
        differences.push({
          path: currentPath,
          type: 'same',
          valueA: valueA,
          valueB: valueB
        });
      }
    });

    return differences;
  };

  const getJsonStructure = (obj, path = '') => {
    if (typeof obj !== 'object' || obj === null) {
      return { [path]: typeof obj };
    }

    const structure = {};
    Object.keys(obj).forEach(key => {
      const currentPath = path ? `${path}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(structure, getJsonStructure(obj[key], currentPath));
      } else {
        structure[currentPath] = Array.isArray(obj[key]) ? 'array' : typeof obj[key];
      }
    });

    return structure;
  };

  const compareJson = () => {
    const validationA = validateJson(jsonA);
    const validationB = validateJson(jsonB);

    let newResult = {
      statusA: validationA.isValid ? '有效' : `无效: ${validationA.error}`,
      statusB: validationB.isValid ? '有效' : `无效: ${validationB.error}`,
      compareStatus: '待对比',
      differences: [],
      structure: null,
      summary: {
        sameFields: 0,
        differentFields: 0,
        newFields: 0,
        deletedFields: 0
      }
    };

    if (validationA.isValid && validationB.isValid) {
      try {
        const objA = JSON.parse(jsonA);
        const objB = JSON.parse(jsonB);
        
        const differences = compareJsonObjects(objA, objB);
        
        const summary = differences.reduce((acc, diff) => {
          switch (diff.type) {
            case 'same':
              acc.sameFields++;
              break;
            case 'modified':
              acc.differentFields++;
              break;
            case 'added':
              acc.newFields++;
              break;
            case 'deleted':
              acc.deletedFields++;
              break;
          }
          return acc;
        }, { sameFields: 0, differentFields: 0, newFields: 0, deletedFields: 0 });

        const structureA = getJsonStructure(objA);
        const structureB = getJsonStructure(objB);

        newResult = {
          ...newResult,
          compareStatus: '对比完成',
          differences,
          structure: { A: structureA, B: structureB },
          summary
        };
      } catch (error) {
        newResult.compareStatus = `对比失败: ${error.message}`;
      }
    } else {
      newResult.compareStatus = '请确保两个JSON都有效';
    }

    setResult(newResult);
  };

  const clearAll = () => {
    setJsonA('');
    setJsonB('');
    setResult({
      statusA: '待验证',
      statusB: '待验证',
      compareStatus: '待对比',
      differences: [],
      structure: null,
      summary: {
        sameFields: 0,
        differentFields: 0,
        newFields: 0,
        deletedFields: 0
      }
    });
  };

  const copyResult = () => {
    let content = '';
    if (activeTab === 'differences') {
      content = result.differences.map(diff => 
        `${diff.path}: ${diff.type} - A: ${JSON.stringify(diff.valueA)} | B: ${JSON.stringify(diff.valueB)}`
      ).join('\n');
    } else if (activeTab === 'structure') {
      content = `结构A:\n${JSON.stringify(result.structure?.A, null, 2)}\n\n结构B:\n${JSON.stringify(result.structure?.B, null, 2)}`;
    } else if (activeTab === 'summary') {
      content = `对比摘要:\n相同字段: ${result.summary.sameFields}\n不同字段: ${result.summary.differentFields}\n新增字段: ${result.summary.newFields}\n删除字段: ${result.summary.deletedFields}`;
    }
    navigator.clipboard.writeText(content);
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'same': return 'text-green-600 bg-green-50 border-green-200';
      case 'modified': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'added': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'deleted': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'same': return '相同';
      case 'modified': return '修改';
      case 'added': return '新增';
      case 'deleted': return '删除';
      default: return '未知';
    }
  };

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">JSON数据对比工具</h2>
      
      <div className="space-y-6">
        {/* JSON输入区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-lg font-medium text-gray-800">JSON数据 A：</label>
              <div className="flex gap-2">
                <button 
                  onClick={() => formatJson(jsonA, setJsonA)} 
                  className="text-sm btn-secondary"
                >
                  格式化
                </button>
                <button 
                  onClick={() => setJsonA('')} 
                  className="text-sm btn-secondary"
                >
                  清空
                </button>
              </div>
            </div>
            <textarea
              value={jsonA}
              onChange={(e) => setJsonA(e.target.value)}
              className="textarea-field h-64 font-mono text-sm"
              placeholder='输入JSON数据A，例如：{"name": "张三", "age": 25}'
            />
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-lg font-medium text-gray-800">JSON数据 B：</label>
              <div className="flex gap-2">
                <button 
                  onClick={() => formatJson(jsonB, setJsonB)} 
                  className="text-sm btn-secondary"
                >
                  格式化
                </button>
                <button 
                  onClick={() => setJsonB('')} 
                  className="text-sm btn-secondary"
                >
                  清空
                </button>
              </div>
            </div>
            <textarea
              value={jsonB}
              onChange={(e) => setJsonB(e.target.value)}
              className="textarea-field h-64 font-mono text-sm"
              placeholder='输入JSON数据B，例如：{"name": "李四", "age": 30}'
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button onClick={compareJson} className="btn-primary">
            对比JSON
          </button>
          <button onClick={clearAll} className="btn-secondary">
            清空全部
          </button>
          <button onClick={copyResult} className="btn-success" disabled={!result.differences.length}>
            复制结果
          </button>
        </div>

        {/* 状态信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className={`border rounded-lg p-3 ${
            result.statusA.includes('有效') ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
          }`}>
            <div className="text-sm font-medium text-gray-600">JSON A 状态</div>
            <div className={`text-sm ${
              result.statusA.includes('有效') ? 'text-green-800' : 'text-red-800'
            }`}>
              {result.statusA}
            </div>
          </div>
          
          <div className={`border rounded-lg p-3 ${
            result.statusB.includes('有效') ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
          }`}>
            <div className="text-sm font-medium text-gray-600">JSON B 状态</div>
            <div className={`text-sm ${
              result.statusB.includes('有效') ? 'text-green-800' : 'text-red-800'
            }`}>
              {result.statusB}
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm font-medium text-blue-600">对比结果</div>
            <div className="text-sm text-blue-800">{result.compareStatus}</div>
          </div>
        </div>

        {/* 结果标签页 */}
        <div>
          <div className="flex space-x-1 border-b border-gray-200 mb-4">
            {[
              { id: 'differences', name: '差异详情' },
              { id: 'structure', name: '结构对比' },
              { id: 'summary', name: '对比摘要' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
              >
                {tab.name}
              </button>
            ))}
          </div>

          {/* 差异详情 */}
          {activeTab === 'differences' && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">差异列表：</h4>
              <div className="result-area max-h-96 overflow-y-auto">
                {result.differences.length > 0 ? (
                  result.differences.map((diff, index) => (
                    <div key={index} className={`mb-2 p-3 border rounded ${getTypeColor(diff.type)}`}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{diff.path}</span>
                        <span className="text-xs px-2 py-1 rounded bg-white border">
                          {getTypeLabel(diff.type)}
                        </span>
                      </div>
                      <div className="text-xs space-y-1">
                        <div>A: <span className="font-mono">{JSON.stringify(diff.valueA)}</span></div>
                        <div>B: <span className="font-mono">{JSON.stringify(diff.valueB)}</span></div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500">输入两个JSON数据进行对比，差异将在这里显示</div>
                )}
              </div>
            </div>
          )}

          {/* 结构对比 */}
          {activeTab === 'structure' && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">结构对比：</h4>
              <div className="result-area">
                {result.structure ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-medium text-sm mb-2">JSON A 结构：</h5>
                      <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.structure.A, null, 2)}
                      </pre>
                    </div>
                    <div>
                      <h5 className="font-medium text-sm mb-2">JSON B 结构：</h5>
                      <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.structure.B, null, 2)}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-500">JSON结构对比信息将在这里显示</div>
                )}
              </div>
            </div>
          )}

          {/* 对比摘要 */}
          {activeTab === 'summary' && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">对比摘要：</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-green-600 font-medium">相同字段</div>
                  <div className="text-xl font-bold text-green-800">{result.summary.sameFields}</div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-yellow-600 font-medium">不同字段</div>
                  <div className="text-xl font-bold text-yellow-800">{result.summary.differentFields}</div>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-blue-600 font-medium">新增字段</div>
                  <div className="text-xl font-bold text-blue-800">{result.summary.newFields}</div>
                </div>
                
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-red-600 font-medium">删除字段</div>
                  <div className="text-xl font-bold text-red-800">{result.summary.deletedFields}</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default JsonCompare;
