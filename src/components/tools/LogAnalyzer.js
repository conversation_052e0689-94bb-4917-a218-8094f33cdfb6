import React, { useState } from 'react';

const LogAnalyzer = () => {
  const [activeTab, setActiveTab] = useState('import');
  const [logContent, setLogContent] = useState('');
  const [logFormat, setLogFormat] = useState('auto');
  const [analysis, setAnalysis] = useState({
    totalLines: 0,
    errorLogs: 0,
    warningLogs: 0,
    infoLogs: 0,
    timeRange: '-',
    ipCount: 0
  });

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogContent(e.target.result);
        analyzeLog(e.target.result);
      };
      reader.readAsText(file);
    }
  };

  const analyzeLog = (content) => {
    const lines = content.split('\n').filter(line => line.trim());
    const errorCount = lines.filter(line => 
      line.toLowerCase().includes('error') || 
      line.toLowerCase().includes('err')
    ).length;
    
    const warningCount = lines.filter(line => 
      line.toLowerCase().includes('warning') || 
      line.toLowerCase().includes('warn')
    ).length;
    
    const infoCount = lines.filter(line => 
      line.toLowerCase().includes('info') || 
      line.toLowerCase().includes('information')
    ).length;

    // 简单的IP地址提取
    const ipRegex = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g;
    const ips = new Set();
    lines.forEach(line => {
      const matches = line.match(ipRegex);
      if (matches) {
        matches.forEach(ip => ips.add(ip));
      }
    });

    setAnalysis({
      totalLines: lines.length,
      errorLogs: errorCount,
      warningLogs: warningCount,
      infoLogs: infoCount,
      timeRange: lines.length > 0 ? '已分析' : '-',
      ipCount: ips.size
    });
  };

  const tabs = [
    { id: 'import', name: '日志导入' },
    { id: 'analysis', name: '统计分析' },
    { id: 'filter', name: '日志过滤' },
    { id: 'search', name: '关键词搜索' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">日志分析工具</h2>
      
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {activeTab === 'import' && (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择日志文件：
            </label>
            <input
              type="file"
              onChange={handleFileUpload}
              accept=".log,.txt"
              className="input-field"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              或直接粘贴日志内容：
            </label>
            <textarea
              value={logContent}
              onChange={(e) => {
                setLogContent(e.target.value);
                analyzeLog(e.target.value);
              }}
              className="textarea-field h-64"
              placeholder="粘贴日志内容到这里..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              日志格式：
            </label>
            <select
              value={logFormat}
              onChange={(e) => setLogFormat(e.target.value)}
              className="select-field"
            >
              <option value="auto">自动检测</option>
              <option value="apache">Apache访问日志</option>
              <option value="nginx">Nginx访问日志</option>
              <option value="iis">IIS日志</option>
              <option value="system">系统日志</option>
              <option value="application">应用程序日志</option>
              <option value="custom">自定义格式</option>
            </select>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">日志预览</h3>
            <div className="result-area h-32">
              {logContent ? 
                logContent.split('\n').slice(0, 5).join('\n') + 
                (logContent.split('\n').length > 5 ? '\n...' : '')
                : '请导入日志文件或粘贴日志内容'
              }
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analysis' && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-4">日志统计概览</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-sm text-blue-600 font-medium">总行数</div>
                <div className="text-2xl font-bold text-blue-800">{analysis.totalLines}</div>
              </div>
              
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-sm text-red-600 font-medium">错误日志</div>
                <div className="text-2xl font-bold text-red-800">{analysis.errorLogs}</div>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-sm text-yellow-600 font-medium">警告日志</div>
                <div className="text-2xl font-bold text-yellow-800">{analysis.warningLogs}</div>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-sm text-green-600 font-medium">信息日志</div>
                <div className="text-2xl font-bold text-green-800">{analysis.infoLogs}</div>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                <div className="text-sm text-purple-600 font-medium">时间范围</div>
                <div className="text-lg font-bold text-purple-800">{analysis.timeRange}</div>
              </div>
              
              <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
                <div className="text-sm text-indigo-600 font-medium">IP地址数</div>
                <div className="text-2xl font-bold text-indigo-800">{analysis.ipCount}</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-4">分析图表</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">日志级别分布</h4>
                <div className="text-sm text-gray-600">图表功能开发中...</div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">时间分布</h4>
                <div className="text-sm text-gray-600">图表功能开发中...</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'filter' && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-4">过滤条件</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">日志级别：</label>
                <div className="space-y-2">
                  {['错误', '警告', '信息', '调试'].map(level => (
                    <label key={level} className="flex items-center">
                      <input type="checkbox" className="mr-2" />
                      <span className="text-sm">{level}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">时间范围：</label>
                  <div className="flex gap-2">
                    <input type="datetime-local" className="input-field" />
                    <span className="self-center">至</span>
                    <input type="datetime-local" className="input-field" />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">IP地址：</label>
                  <input type="text" className="input-field" placeholder="输入IP地址" />
                </div>
              </div>
            </div>
            
            <div className="flex gap-3 mt-4">
              <button className="btn-primary">应用过滤</button>
              <button className="btn-secondary">清除过滤</button>
              <button className="btn-success">导出结果</button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">过滤结果 (0条)</h3>
            <div className="result-area h-64">
              请先导入日志并设置过滤条件
            </div>
          </div>
        </div>
      )}

      {activeTab === 'search' && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-4">搜索设置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">搜索关键词：</label>
                <input type="text" className="input-field" placeholder="输入搜索关键词" />
              </div>
              
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">区分大小写</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">全词匹配</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">正则表达式</span>
                </label>
              </div>
              
              <div className="flex gap-3">
                <button className="btn-primary">搜索</button>
                <button className="btn-secondary">清除</button>
                <button className="btn-success">导出搜索结果</button>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">搜索结果 (0条匹配)</h3>
            <div className="result-area h-64">
              请输入关键词进行搜索
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">搜索统计</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                <div className="text-sm text-blue-600 font-medium">匹配行数</div>
                <div className="text-xl font-bold text-blue-800">0</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
                <div className="text-sm text-green-600 font-medium">匹配次数</div>
                <div className="text-xl font-bold text-green-800">0</div>
              </div>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
                <div className="text-sm text-purple-600 font-medium">匹配率</div>
                <div className="text-xl font-bold text-purple-800">0%</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LogAnalyzer;
