import React, { useState } from 'react';

const RandomQuestions = () => {
  const [questionBank, setQuestionBank] = useState('');
  const [generatedQuestions, setGeneratedQuestions] = useState([]);
  const [settings, setSettings] = useState({
    questionCount: 10,
    questionType: 'all',
    includeAnswers: true,
    randomOrder: true
  });
  const [activeTab, setActiveTab] = useState('generator');

  // 示例题目格式
  const sampleQuestions = `# 单选题
1. JavaScript是一种什么类型的语言？
A. 编译型语言
B. 解释型语言
C. 汇编语言
D. 机器语言
答案：B

2. 以下哪个不是JavaScript的数据类型？
A. string
B. number
C. char
D. boolean
答案：C

# 多选题
3. 以下哪些是前端框架？（多选）
A. React
B. Vue
C. Angular
D. Django
答案：ABC

# 判断题
4. JavaScript是区分大小写的语言。
答案：正确

5. HTML是一种编程语言。
答案：错误

# 填空题
6. JavaScript中声明变量使用关键字____或____。
答案：var|let

7. CSS的全称是____。
答案：Cascading Style Sheets

# 简答题
8. 请简述JavaScript中闭包的概念。
答案：闭包是指有权访问另一个函数作用域中变量的函数。

# 编程题
9. 编写一个函数，计算数组中所有元素的和。
答案：
function sum(arr) {
  return arr.reduce((total, num) => total + num, 0);
}`;

  // 解析题目文本
  const parseQuestions = (text) => {
    const questions = [];
    const lines = text.split('\n').filter(line => line.trim());
    let currentType = '';
    let currentQuestion = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 识别题目类型
      if (line.startsWith('#')) {
        currentType = line.replace('#', '').trim();
        continue;
      }

      // 识别题目开始（数字开头）
      if (/^\d+\./.test(line)) {
        // 保存上一题
        if (currentQuestion) {
          questions.push(currentQuestion);
        }
        
        // 开始新题
        currentQuestion = {
          id: questions.length + 1,
          type: currentType,
          question: line.replace(/^\d+\.\s*/, ''),
          options: [],
          answer: '',
          explanation: ''
        };
      }
      // 识别选项（A. B. C. D.开头）
      else if (/^[A-Z]\./.test(line) && currentQuestion) {
        currentQuestion.options.push(line);
      }
      // 识别答案
      else if (line.startsWith('答案：') && currentQuestion) {
        currentQuestion.answer = line.replace('答案：', '');
      }
      // 识别解析
      else if (line.startsWith('解析：') && currentQuestion) {
        currentQuestion.explanation = line.replace('解析：', '');
      }
      // 其他内容作为题目内容的一部分
      else if (currentQuestion && !line.startsWith('答案：') && !line.startsWith('解析：')) {
        currentQuestion.question += '\n' + line;
      }
    }

    // 添加最后一题
    if (currentQuestion) {
      questions.push(currentQuestion);
    }

    return questions;
  };

  // 生成随机题目
  const generateRandomQuestions = () => {
    if (!questionBank.trim()) {
      alert('请先输入题目库内容');
      return;
    }

    const allQuestions = parseQuestions(questionBank);
    
    if (allQuestions.length === 0) {
      alert('未能解析到有效题目，请检查格式');
      return;
    }

    // 根据类型筛选题目
    let filteredQuestions = allQuestions;
    if (settings.questionType !== 'all') {
      filteredQuestions = allQuestions.filter(q => 
        q.type.includes(settings.questionType)
      );
    }

    if (filteredQuestions.length === 0) {
      alert('没有找到指定类型的题目');
      return;
    }

    // 随机选择题目
    let selectedQuestions = [...filteredQuestions];
    if (settings.randomOrder) {
      selectedQuestions = selectedQuestions.sort(() => Math.random() - 0.5);
    }

    // 限制题目数量
    selectedQuestions = selectedQuestions.slice(0, Math.min(settings.questionCount, selectedQuestions.length));

    setGeneratedQuestions(selectedQuestions);
  };

  // 导出题目
  const exportQuestions = () => {
    if (generatedQuestions.length === 0) {
      alert('请先生成题目');
      return;
    }

    let exportText = '随机生成的题目\n';
    exportText += '==================\n\n';

    generatedQuestions.forEach((q, index) => {
      exportText += `${index + 1}. ${q.question}\n`;
      
      if (q.options.length > 0) {
        q.options.forEach(option => {
          exportText += `${option}\n`;
        });
      }
      
      if (settings.includeAnswers) {
        exportText += `答案：${q.answer}\n`;
        if (q.explanation) {
          exportText += `解析：${q.explanation}\n`;
        }
      }
      
      exportText += '\n';
    });

    // 下载文件
    const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `随机题目_${new Date().toISOString().slice(0, 10)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // 复制题目
  const copyQuestions = () => {
    if (generatedQuestions.length === 0) {
      alert('请先生成题目');
      return;
    }

    let copyText = '';
    generatedQuestions.forEach((q, index) => {
      copyText += `${index + 1}. ${q.question}\n`;
      
      if (q.options.length > 0) {
        q.options.forEach(option => {
          copyText += `${option}\n`;
        });
      }
      
      if (settings.includeAnswers) {
        copyText += `答案：${q.answer}\n`;
        if (q.explanation) {
          copyText += `解析：${q.explanation}\n`;
        }
      }
      
      copyText += '\n';
    });

    navigator.clipboard.writeText(copyText);
    alert('题目已复制到剪贴板');
  };

  const tabs = [
    { id: 'generator', name: '题目生成' },
    { id: 'format', name: '格式说明' },
    { id: 'preview', name: '题目预览' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">随机题目生成工具</h2>
      
      {/* 标签页导航 */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {/* 题目生成 */}
      {activeTab === 'generator' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 题目库输入 */}
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-2">
                <label className="text-lg font-medium text-gray-800">题目库内容：</label>
                <button 
                  onClick={() => setQuestionBank(sampleQuestions)}
                  className="text-sm btn-secondary"
                >
                  加载示例
                </button>
              </div>
              <textarea
                value={questionBank}
                onChange={(e) => setQuestionBank(e.target.value)}
                className="textarea-field h-96"
                placeholder="请按照格式输入题目内容，或点击'加载示例'查看格式..."
              />
            </div>

            {/* 生成设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-800">生成设置</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">题目数量：</label>
                <input
                  type="number"
                  value={settings.questionCount}
                  onChange={(e) => setSettings(prev => ({ ...prev, questionCount: parseInt(e.target.value) || 10 }))}
                  className="input-field"
                  min="1"
                  max="100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">题目类型：</label>
                <select
                  value={settings.questionType}
                  onChange={(e) => setSettings(prev => ({ ...prev, questionType: e.target.value }))}
                  className="select-field"
                >
                  <option value="all">全部类型</option>
                  <option value="单选题">单选题</option>
                  <option value="多选题">多选题</option>
                  <option value="判断题">判断题</option>
                  <option value="填空题">填空题</option>
                  <option value="简答题">简答题</option>
                  <option value="编程题">编程题</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.includeAnswers}
                    onChange={(e) => setSettings(prev => ({ ...prev, includeAnswers: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm">包含答案</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.randomOrder}
                    onChange={(e) => setSettings(prev => ({ ...prev, randomOrder: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm">随机顺序</span>
                </label>
              </div>

              <div className="flex flex-col gap-3 pt-4">
                <button onClick={generateRandomQuestions} className="btn-primary w-full">
                  生成随机题目
                </button>
                <button onClick={copyQuestions} className="btn-secondary w-full" disabled={generatedQuestions.length === 0}>
                  复制题目
                </button>
                <button onClick={exportQuestions} className="btn-success w-full" disabled={generatedQuestions.length === 0}>
                  导出题目
                </button>
              </div>

              {/* 统计信息 */}
              {generatedQuestions.length > 0 && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">生成统计</h4>
                  <div className="text-xs text-blue-600 space-y-1">
                    <div>已生成题目：{generatedQuestions.length} 道</div>
                    <div>题目类型：{[...new Set(generatedQuestions.map(q => q.type))].join('、')}</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 格式说明 */}
      {activeTab === 'format' && (
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-yellow-800 mb-4">题目文本导入格式规范</h3>
            
            <div className="space-y-4 text-sm text-yellow-700">
              <div>
                <h4 className="font-medium mb-2">基本格式：</h4>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>使用 # 标记题目类型（如：# 单选题）</li>
                  <li>题目以数字开头（如：1. 题目内容）</li>
                  <li>选项以字母开头（如：A. 选项内容）</li>
                  <li>答案以"答案："开头</li>
                  <li>解析以"解析："开头（可选）</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">支持的题目类型：</h4>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li><strong>单选题</strong>：包含A、B、C、D选项，答案为单个字母</li>
                  <li><strong>多选题</strong>：包含多个选项，答案为多个字母组合</li>
                  <li><strong>判断题</strong>：答案为"正确"或"错误"</li>
                  <li><strong>填空题</strong>：答案可用|分隔多个可能答案</li>
                  <li><strong>简答题</strong>：开放性题目，答案为文本描述</li>
                  <li><strong>编程题</strong>：代码类题目，答案可包含代码块</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">注意事项：</h4>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>每个题目之间用空行分隔</li>
                  <li>题目内容可以跨多行</li>
                  <li>选项和答案必须紧跟在题目后面</li>
                  <li>支持中英文混合内容</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">示例格式：</h4>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">
{`# 单选题
1. JavaScript是什么类型的语言？
A. 编译型语言
B. 解释型语言
C. 汇编语言
D. 机器语言
答案：B
解析：JavaScript是一种解释型的脚本语言

# 多选题
2. 以下哪些是前端框架？
A. React
B. Vue
C. Angular
D. Django
答案：ABC

# 判断题
3. HTML是一种编程语言。
答案：错误`}
            </pre>
          </div>
        </div>
      )}

      {/* 题目预览 */}
      {activeTab === 'preview' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-800">生成的题目预览</h3>
          
          {generatedQuestions.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              请先在"题目生成"页面生成题目
            </div>
          ) : (
            <div className="space-y-6">
              {generatedQuestions.map((question, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                      {question.type}
                    </span>
                    <span className="text-sm text-gray-500">第 {index + 1} 题</span>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-800 mb-2">
                      {index + 1}. {question.question}
                    </h4>
                  </div>

                  {question.options.length > 0 && (
                    <div className="mb-4">
                      <div className="space-y-1">
                        {question.options.map((option, optIndex) => (
                          <div key={optIndex} className="text-sm text-gray-700">
                            {option}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {settings.includeAnswers && (
                    <div className="border-t border-gray-100 pt-3">
                      <div className="text-sm">
                        <span className="font-medium text-green-600">答案：</span>
                        <span className="text-green-700">{question.answer}</span>
                      </div>
                      {question.explanation && (
                        <div className="text-sm mt-1">
                          <span className="font-medium text-blue-600">解析：</span>
                          <span className="text-blue-700">{question.explanation}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RandomQuestions;
