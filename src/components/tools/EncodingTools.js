import React, { useState } from 'react';

const EncodingTools = () => {
  const [activeTab, setActiveTab] = useState('base64');
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [encryptionKey, setEncryptionKey] = useState('');

  const base64Encode = () => {
    try {
      const encoded = btoa(unescape(encodeURIComponent(inputText)));
      setOutputText(encoded);
    } catch (error) {
      setOutputText('编码失败：' + error.message);
    }
  };

  const base64Decode = () => {
    try {
      const decoded = decodeURIComponent(escape(atob(inputText)));
      setOutputText(decoded);
    } catch (error) {
      setOutputText('解码失败：' + error.message);
    }
  };

  const urlEncode = () => {
    try {
      const encoded = encodeURIComponent(inputText);
      setOutputText(encoded);
    } catch (error) {
      setOutputText('编码失败：' + error.message);
    }
  };

  const urlDecode = () => {
    try {
      const decoded = decodeURIComponent(inputText);
      setOutputText(decoded);
    } catch (error) {
      setOutputText('解码失败：' + error.message);
    }
  };

  const htmlEncode = () => {
    const htmlEntities = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;',
      '/': '&#x2F;'
    };
    const encoded = inputText.replace(/[&<>"'\/]/g, (s) => htmlEntities[s]);
    setOutputText(encoded);
  };

  const htmlDecode = () => {
    const htmlEntities = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&#x2F;': '/'
    };
    const decoded = inputText.replace(/&amp;|&lt;|&gt;|&quot;|&#39;|&#x2F;/g, (s) => htmlEntities[s]);
    setOutputText(decoded);
  };

  const unicodeEncode = () => {
    const encoded = inputText.split('').map(char => {
      const code = char.charCodeAt(0);
      return code > 127 ? '\\u' + code.toString(16).padStart(4, '0') : char;
    }).join('');
    setOutputText(encoded);
  };

  const unicodeDecode = () => {
    try {
      const decoded = inputText.replace(/\\u[\dA-F]{4}/gi, (match) => {
        return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
      });
      setOutputText(decoded);
    } catch (error) {
      setOutputText('解码失败：' + error.message);
    }
  };

  const hexEncode = () => {
    const encoded = inputText.split('').map(char => 
      char.charCodeAt(0).toString(16).padStart(2, '0')
    ).join(' ');
    setOutputText(encoded);
  };

  const hexDecode = () => {
    try {
      const decoded = inputText.split(' ').map(hex => 
        String.fromCharCode(parseInt(hex, 16))
      ).join('');
      setOutputText(decoded);
    } catch (error) {
      setOutputText('解码失败：' + error.message);
    }
  };

  const md5Hash = async () => {
    // 简化的MD5实现（实际项目中应使用专业的加密库）
    setOutputText('MD5加密需要专业的加密库支持');
  };

  const clearAll = () => {
    setInputText('');
    setOutputText('');
    setEncryptionKey('');
  };

  const copyResult = () => {
    navigator.clipboard.writeText(outputText);
  };

  const downloadResult = () => {
    const blob = new Blob([outputText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${activeTab}_result.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const tabs = [
    { id: 'base64', name: 'Base64编码' },
    { id: 'url', name: 'URL编码' },
    { id: 'html', name: 'HTML编码' },
    { id: 'unicode', name: 'Unicode编码' },
    { id: 'hex', name: '十六进制编码' },
    { id: 'md5', name: 'MD5加解密' },
    { id: 'des', name: 'DES加解密' },
    { id: 'aes', name: 'AES加解密' },
    { id: 'rsa', name: 'RSA加解密' }
  ];

  const renderToolContent = () => {
    switch (activeTab) {
      case 'base64':
        return (
          <div className="flex gap-3">
            <button onClick={base64Encode} className="btn-primary">Base64编码</button>
            <button onClick={base64Decode} className="btn-primary">Base64解码</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'url':
        return (
          <div className="flex gap-3">
            <button onClick={urlEncode} className="btn-primary">URL编码</button>
            <button onClick={urlDecode} className="btn-primary">URL解码</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'html':
        return (
          <div className="flex gap-3">
            <button onClick={htmlEncode} className="btn-primary">HTML编码</button>
            <button onClick={htmlDecode} className="btn-primary">HTML解码</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'unicode':
        return (
          <div className="flex gap-3">
            <button onClick={unicodeEncode} className="btn-primary">Unicode编码</button>
            <button onClick={unicodeDecode} className="btn-primary">Unicode解码</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'hex':
        return (
          <div className="flex gap-3">
            <button onClick={hexEncode} className="btn-primary">十六进制编码</button>
            <button onClick={hexDecode} className="btn-primary">十六进制解码</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'md5':
        return (
          <div className="flex gap-3">
            <button onClick={md5Hash} className="btn-primary">MD5加密</button>
            <button onClick={() => setOutputText('MD5解密需要彩虹表支持')} className="btn-primary">MD5解密</button>
            <button onClick={clearAll} className="btn-secondary">清空</button>
          </div>
        );
      case 'des':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密钥 (8字符)：</label>
              <input
                type="text"
                value={encryptionKey}
                onChange={(e) => setEncryptionKey(e.target.value)}
                className="input-field"
                placeholder="输入8位密钥"
                maxLength={8}
              />
            </div>
            <div className="flex gap-3">
              <button onClick={() => setOutputText('DES加密需要专业的加密库支持')} className="btn-primary">DES加密</button>
              <button onClick={() => setOutputText('DES解密需要专业的加密库支持')} className="btn-primary">DES解密</button>
              <button onClick={clearAll} className="btn-secondary">清空</button>
            </div>
          </div>
        );
      case 'aes':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密钥 (16/24/32字符)：</label>
              <input
                type="text"
                value={encryptionKey}
                onChange={(e) => setEncryptionKey(e.target.value)}
                className="input-field"
                placeholder="输入16、24或32位密钥"
              />
            </div>
            <div className="flex gap-3">
              <button onClick={() => setOutputText('AES加密需要专业的加密库支持')} className="btn-primary">AES加密</button>
              <button onClick={() => setOutputText('AES解密需要专业的加密库支持')} className="btn-primary">AES解密</button>
              <button onClick={clearAll} className="btn-secondary">清空</button>
            </div>
          </div>
        );
      case 'rsa':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">RSA密钥：</label>
              <textarea
                value={encryptionKey}
                onChange={(e) => setEncryptionKey(e.target.value)}
                className="textarea-field h-24"
                placeholder="输入RSA公钥或私钥"
              />
            </div>
            <div className="flex gap-3">
              <button onClick={() => setOutputText('RSA加密需要专业的加密库支持')} className="btn-primary">RSA加密</button>
              <button onClick={() => setOutputText('RSA解密需要专业的加密库支持')} className="btn-primary">RSA解密</button>
              <button onClick={() => setOutputText('RSA密钥对生成需要专业的加密库支持')} className="btn-success">生成密钥对</button>
              <button onClick={clearAll} className="btn-secondary">清空</button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">编码/加密工具</h2>
      
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      <div className="space-y-6">
        {/* 输入文本 */}
        <div>
          <h3 className="text-lg font-medium text-gray-800 mb-3">输入文本</h3>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="textarea-field h-32"
            placeholder="输入要处理的文本..."
          />
        </div>

        {/* 工具操作按钮 */}
        <div>
          {renderToolContent()}
        </div>

        {/* 输出结果 */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-medium text-gray-800">输出结果</h3>
            <div className="flex gap-2">
              <button 
                onClick={copyResult} 
                className="btn-secondary"
                disabled={!outputText}
              >
                复制结果
              </button>
              <button 
                onClick={downloadResult} 
                className="btn-success"
                disabled={!outputText}
              >
                下载结果
              </button>
            </div>
          </div>
          <textarea
            value={outputText}
            readOnly
            className="result-area h-32"
            placeholder="处理结果将在这里显示..."
          />
        </div>

        {/* 说明信息 */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">注意事项：</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Base64、URL、HTML、Unicode、十六进制编码为客户端实现</li>
            <li>• MD5、DES、AES、RSA等加密算法需要专业的加密库支持</li>
            <li>• 生产环境中请使用经过安全审计的加密库</li>
            <li>• 敏感数据请勿在客户端进行加密处理</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default EncodingTools;
