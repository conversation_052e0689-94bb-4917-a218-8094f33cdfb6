import React, { useState } from 'react';

const ImageProcessor = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageInfo, setImageInfo] = useState({
    fileName: '',
    format: '',
    size: '',
    fileSize: ''
  });
  const [convertSettings, setConvertSettings] = useState({
    outputFormat: 'JPEG',
    quality: 90,
    compressionMode: '质量优先',
    targetSize: '',
    targetUnit: 'KB',
    enableResize: false,
    width: '',
    height: '',
    keepRatio: true
  });

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedImage(file);
      
      // 获取图片信息
      const img = new Image();
      img.onload = () => {
        setImageInfo({
          fileName: file.name,
          format: file.type,
          size: `${img.width} × ${img.height}`,
          fileSize: formatFileSize(file.size)
        });
      };
      img.src = URL.createObjectURL(file);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      handleImageUpload({ target: { files: [file] } });
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const convertImage = () => {
    if (!selectedImage) return;
    
    // 这里应该实现实际的图片转换逻辑
    // 由于浏览器限制，这里只是模拟
    alert('图片转换功能需要后端支持，这里仅为演示界面');
  };

  const resetSelection = () => {
    setSelectedImage(null);
    setImageInfo({
      fileName: '',
      format: '',
      size: '',
      fileSize: ''
    });
  };

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">图片处理工具</h2>
      
      <div className="space-y-6">
        {/* 图片上传区域 */}
        {!selectedImage ? (
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-blue-400 transition-colors"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            <div className="text-6xl mb-4">📷</div>
            <div className="text-lg text-gray-600 mb-2">点击或拖拽图片到此处上传</div>
            <div className="text-sm text-gray-500 mb-4">支持 JPG、PNG、GIF、BMP、WEBP 格式</div>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              id="image-upload"
            />
            <label htmlFor="image-upload" className="btn-primary cursor-pointer">
              📁 选择图片
            </label>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 原始图片信息 */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">原始图片</h3>
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <img
                  src={URL.createObjectURL(selectedImage)}
                  alt="预览"
                  className="max-w-full h-auto max-h-64 mx-auto rounded"
                />
              </div>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">文件名：</span>{imageInfo.fileName}</div>
                <div><span className="font-medium">格式：</span>{imageInfo.format}</div>
                <div><span className="font-medium">尺寸：</span>{imageInfo.size}</div>
                <div><span className="font-medium">文件大小：</span>{imageInfo.fileSize}</div>
              </div>
            </div>

            {/* 转换设置 */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">转换设置</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">输出格式：</label>
                  <select
                    value={convertSettings.outputFormat}
                    onChange={(e) => setConvertSettings(prev => ({ ...prev, outputFormat: e.target.value }))}
                    className="select-field"
                  >
                    <option value="JPEG">JPEG</option>
                    <option value="PNG">PNG</option>
                    <option value="WEBP">WEBP</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    图片质量：{convertSettings.quality}%
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="100"
                    value={convertSettings.quality}
                    onChange={(e) => setConvertSettings(prev => ({ ...prev, quality: parseInt(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">压缩模式：</label>
                  <select
                    value={convertSettings.compressionMode}
                    onChange={(e) => setConvertSettings(prev => ({ ...prev, compressionMode: e.target.value }))}
                    className="select-field"
                  >
                    <option value="质量优先">质量优先</option>
                    <option value="体积优先">体积优先</option>
                    <option value="平衡模式">平衡模式</option>
                    <option value="自定义目标">自定义目标</option>
                  </select>
                </div>

                {convertSettings.compressionMode === '自定义目标' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">目标文件大小：</label>
                    <div className="flex gap-2">
                      <input
                        type="number"
                        value={convertSettings.targetSize}
                        onChange={(e) => setConvertSettings(prev => ({ ...prev, targetSize: e.target.value }))}
                        className="input-field flex-1"
                        placeholder="目标大小"
                      />
                      <select
                        value={convertSettings.targetUnit}
                        onChange={(e) => setConvertSettings(prev => ({ ...prev, targetUnit: e.target.value }))}
                        className="select-field w-20"
                      >
                        <option value="KB">KB</option>
                        <option value="MB">MB</option>
                      </select>
                    </div>
                  </div>
                )}

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={convertSettings.enableResize}
                      onChange={(e) => setConvertSettings(prev => ({ ...prev, enableResize: e.target.checked }))}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">启用尺寸调整</span>
                  </label>
                </div>

                {convertSettings.enableResize && (
                  <div className="space-y-3">
                    <div className="flex gap-2 items-center">
                      <input
                        type="number"
                        value={convertSettings.width}
                        onChange={(e) => setConvertSettings(prev => ({ ...prev, width: e.target.value }))}
                        className="input-field"
                        placeholder="宽度"
                      />
                      <span>×</span>
                      <input
                        type="number"
                        value={convertSettings.height}
                        onChange={(e) => setConvertSettings(prev => ({ ...prev, height: e.target.value }))}
                        className="input-field"
                        placeholder="高度"
                      />
                    </div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={convertSettings.keepRatio}
                        onChange={(e) => setConvertSettings(prev => ({ ...prev, keepRatio: e.target.checked }))}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-600">保持比例</span>
                    </label>
                  </div>
                )}

                <div className="flex gap-3 pt-4">
                  <button onClick={convertImage} className="btn-primary">
                    转换图片
                  </button>
                  <button onClick={resetSelection} className="btn-secondary">
                    重新选择
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 转换后图片（模拟） */}
        {selectedImage && (
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">转换后图片</h3>
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <div className="text-gray-500 mb-4">转换后的图片将在这里显示</div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">格式：</span>
                  <span>{convertSettings.outputFormat}</span>
                </div>
                <div>
                  <span className="font-medium">尺寸：</span>
                  <span>待转换</span>
                </div>
                <div>
                  <span className="font-medium">文件大小：</span>
                  <span>待转换</span>
                </div>
                <div>
                  <span className="font-medium">压缩率：</span>
                  <span>待转换</span>
                </div>
              </div>
              <button className="btn-success mt-4" disabled>
                下载图片
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageProcessor;
