import React, { useState, useEffect } from 'react';

const RegexTester = () => {
  const [pattern, setPattern] = useState('');
  const [flags, setFlags] = useState({
    g: true,  // global
    i: false, // ignoreCase
    m: false, // multiline
    s: false, // dotAll
    u: false, // unicode
    y: false  // sticky
  });
  const [testText, setTestText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [activeTab, setActiveTab] = useState('matches');
  const [result, setResult] = useState({
    isValid: true,
    matches: [],
    groups: [],
    replaced: '',
    error: null
  });

  useEffect(() => {
    testRegex();
  }, [pattern, flags, testText, replaceText]);

  const testRegex = () => {
    if (!pattern || !testText) {
      setResult({
        isValid: true,
        matches: [],
        groups: [],
        replaced: '',
        error: null
      });
      return;
    }

    try {
      const flagString = Object.entries(flags)
        .filter(([_, enabled]) => enabled)
        .map(([flag, _]) => flag)
        .join('');
      
      const regex = new RegExp(pattern, flagString);
      const matches = [];
      const groups = [];
      let match;

      // 获取所有匹配
      if (flags.g) {
        while ((match = regex.exec(testText)) !== null) {
          matches.push({
            match: match[0],
            index: match.index,
            groups: match.slice(1)
          });
          
          // 避免无限循环
          if (match.index === regex.lastIndex) {
            regex.lastIndex++;
          }
        }
      } else {
        match = regex.exec(testText);
        if (match) {
          matches.push({
            match: match[0],
            index: match.index,
            groups: match.slice(1)
          });
        }
      }

      // 获取分组信息
      if (matches.length > 0) {
        matches.forEach((m, i) => {
          m.groups.forEach((group, j) => {
            if (group !== undefined) {
              groups.push({
                matchIndex: i,
                groupIndex: j + 1,
                value: group
              });
            }
          });
        });
      }

      // 执行替换
      let replaced = '';
      if (replaceText !== '') {
        replaced = testText.replace(regex, replaceText);
      }

      setResult({
        isValid: true,
        matches,
        groups,
        replaced,
        error: null
      });

    } catch (error) {
      setResult({
        isValid: false,
        matches: [],
        groups: [],
        replaced: '',
        error: error.message
      });
    }
  };

  const toggleFlag = (flag) => {
    setFlags(prev => ({
      ...prev,
      [flag]: !prev[flag]
    }));
  };

  const clearAll = () => {
    setPattern('');
    setTestText('');
    setReplaceText('');
    setResult({
      isValid: true,
      matches: [],
      groups: [],
      replaced: '',
      error: null
    });
  };

  const copyResult = () => {
    let content = '';
    if (activeTab === 'matches') {
      content = result.matches.map(m => m.match).join('\n');
    } else if (activeTab === 'groups') {
      content = result.groups.map(g => `Group ${g.groupIndex}: ${g.value}`).join('\n');
    } else if (activeTab === 'replace') {
      content = result.replaced;
    }
    navigator.clipboard.writeText(content);
  };

  const highlightMatches = (text) => {
    if (!result.matches.length || !result.isValid) return text;

    let highlighted = text;
    let offset = 0;

    result.matches.forEach((match) => {
      const start = match.index + offset;
      const end = start + match.match.length;
      const before = highlighted.substring(0, start);
      const matchText = highlighted.substring(start, end);
      const after = highlighted.substring(end);
      
      highlighted = before + `<mark class="bg-yellow-200 px-1 rounded">${matchText}</mark>` + after;
      offset += 47; // 添加的HTML标签长度
    });

    return highlighted;
  };

  const commonPatterns = [
    { name: '邮箱', pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}' },
    { name: '手机号', pattern: '1[3-9]\\d{9}' },
    { name: 'IP地址', pattern: '\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b' },
    { name: 'URL', pattern: 'https?://[^\\s]+' },
    { name: '身份证', pattern: '[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]' },
    { name: '中文字符', pattern: '[\\u4e00-\\u9fa5]+' }
  ];

  return (
    <div className="tool-section">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">正则表达式测试工具</h2>
      
      <div className="space-y-6">
        {/* 正则表达式输入 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            正则表达式：
          </label>
          <div className="flex items-center space-x-2">
            <span className="text-gray-500">/</span>
            <input
              type="text"
              value={pattern}
              onChange={(e) => setPattern(e.target.value)}
              className="input-field flex-1"
              placeholder="输入正则表达式..."
            />
            <span className="text-gray-500">/</span>
            
            {/* 标志选择 */}
            <div className="flex space-x-1">
              {Object.entries(flags).map(([flag, enabled]) => (
                <button
                  key={flag}
                  onClick={() => toggleFlag(flag)}
                  className={`px-2 py-1 text-xs rounded ${
                    enabled 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                  }`}
                  title={`${flag} flag`}
                >
                  {flag}
                </button>
              ))}
            </div>
          </div>
          
          {/* 常用正则 */}
          <div className="mt-2">
            <div className="text-xs text-gray-500 mb-1">常用模式：</div>
            <div className="flex flex-wrap gap-1">
              {commonPatterns.map((item) => (
                <button
                  key={item.name}
                  onClick={() => setPattern(item.pattern)}
                  className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-600"
                >
                  {item.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 测试文本 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            测试文本：
          </label>
          <textarea
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            className="textarea-field h-32"
            placeholder="输入要测试的文本..."
          />
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button onClick={testRegex} className="btn-primary">
            测试
          </button>
          <button onClick={clearAll} className="btn-secondary">
            清空
          </button>
          <button onClick={copyResult} className="btn-success" disabled={!result.matches.length}>
            复制结果
          </button>
        </div>

        {/* 状态信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm text-blue-600 font-medium">匹配数量</div>
            <div className="text-xl font-bold text-blue-800">{result.matches.length}</div>
          </div>
          
          <div className={`border rounded-lg p-3 ${
            result.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
          }`}>
            <div className={`text-sm font-medium ${
              result.isValid ? 'text-green-600' : 'text-red-600'
            }`}>
              正则状态
            </div>
            <div className={`text-xl font-bold ${
              result.isValid ? 'text-green-800' : 'text-red-800'
            }`}>
              {result.isValid ? '有效' : '无效'}
            </div>
          </div>
          
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="text-sm text-purple-600 font-medium">分组数量</div>
            <div className="text-xl font-bold text-purple-800">{result.groups.length}</div>
          </div>
        </div>

        {/* 错误信息 */}
        {result.error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800 font-medium">错误信息：</div>
            <div className="text-red-600 text-sm mt-1">{result.error}</div>
          </div>
        )}

        {/* 结果标签页 */}
        <div>
          <div className="flex space-x-1 border-b border-gray-200 mb-4">
            {[
              { id: 'matches', name: '匹配结果' },
              { id: 'groups', name: '分组详情' },
              { id: 'replace', name: '替换测试' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
              >
                {tab.name}
              </button>
            ))}
          </div>

          {/* 匹配结果 */}
          {activeTab === 'matches' && (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">高亮显示：</h4>
                <div 
                  className="result-area"
                  dangerouslySetInnerHTML={{ __html: highlightMatches(testText) || '输入正则表达式和测试文本，匹配结果将在这里高亮显示' }}
                />
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">匹配列表：</h4>
                <div className="result-area">
                  {result.matches.length > 0 ? (
                    result.matches.map((match, index) => (
                      <div key={index} className="mb-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <div className="font-mono text-sm">{match.match}</div>
                        <div className="text-xs text-gray-500">位置: {match.index}</div>
                      </div>
                    ))
                  ) : (
                    <div className="text-gray-500">暂无匹配结果</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 分组详情 */}
          {activeTab === 'groups' && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">分组捕获：</h4>
              <div className="result-area">
                {result.groups.length > 0 ? (
                  result.groups.map((group, index) => (
                    <div key={index} className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded">
                      <div className="text-sm">
                        <span className="font-medium">分组 {group.groupIndex}:</span>
                        <span className="font-mono ml-2">{group.value}</span>
                      </div>
                      <div className="text-xs text-gray-500">匹配 {group.matchIndex + 1}</div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-500">暂无分组信息</div>
                )}
              </div>
            </div>
          )}

          {/* 替换测试 */}
          {activeTab === 'replace' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  替换文本：
                </label>
                <input
                  type="text"
                  value={replaceText}
                  onChange={(e) => setReplaceText(e.target.value)}
                  className="input-field"
                  placeholder="输入替换文本..."
                />
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">替换结果：</h4>
                <div className="result-area">
                  {replaceText ? result.replaced || '输入替换文本查看结果' : '输入替换文本查看结果'}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RegexTester;
