// 自动登录工具类
class AutoLogin {
  constructor() {
    this.platformConfigs = {
      '直销': {
        selectors: {
          // 新版登录页面
          newVersion: {
            username: 'input[name="username"], input[placeholder*="用户名"], input[placeholder*="账号"]',
            password: 'input[name="password"], input[type="password"]',
            staffUsername: 'input[name="staff"], input[placeholder*="员工"]',
            staffCheckbox: 'input[type="checkbox"][name*="staff"], .staff-checkbox',
            loginButton: 'button[type="submit"], .login-btn, .btn-login',
          },
          // 旧版登录页面
          oldVersion: {
            username: '#username, input[name="user"]',
            password: '#password, input[name="pwd"]',
            loginButton: '#loginBtn, .loginBtn',
          }
        }
      },
      'OEM': {
        selectors: {
          username: 'input[name="username"], input[placeholder*="用户名"]',
          staffUsername: 'input[name="staff"], input[placeholder*="员工"]',
          password: 'input[name="password"], input[type="password"]',
          staffCheckbox: 'input[type="checkbox"][name*="staff"]',
          loginButton: 'button[type="submit"], .login-btn'
        }
      },
      '代理商平台': {
        selectors: {
          loginType: 'input[value="agency"], .agency-login',
          username: 'input[name="username"], input[placeholder*="代理商"]',
          password: 'input[name="password"], input[type="password"]',
          loginButton: 'button[type="submit"], .login-btn'
        }
      },
      'OSS': {
        selectors: {
          username: 'input[name="username"], input[placeholder*="用户名"]',
          password: 'input[name="password"], input[type="password"]',
          loginButton: 'button[type="submit"], .login-btn'
        }
      }
    };
  }

  // 等待元素出现
  waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        } else {
          setTimeout(checkElement, 100);
        }
      };
      
      checkElement();
    });
  }

  // 安全地填充输入框
  async fillInput(selector, value, delay = 100) {
    try {
      const element = await this.waitForElement(selector, 3000);
      if (element && value) {
        // 清空现有内容
        element.value = '';
        element.focus();
        
        // 模拟人工输入
        for (let i = 0; i < value.length; i++) {
          element.value += value[i];
          element.dispatchEvent(new Event('input', { bubbles: true }));
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.blur();
        return true;
      }
    } catch (error) {
      console.warn(`Failed to fill input ${selector}:`, error);
      return false;
    }
  }

  // 安全地点击元素
  async clickElement(selector, delay = 500) {
    try {
      const element = await this.waitForElement(selector, 3000);
      if (element) {
        await new Promise(resolve => setTimeout(resolve, delay));
        element.click();
        return true;
      }
    } catch (error) {
      console.warn(`Failed to click element ${selector}:`, error);
      return false;
    }
  }

  // 检测登录页面版本（针对直销平台）
  async detectLoginPageVersion(platform) {
    if (platform !== '直销') return 'default';
    
    try {
      // 尝试检测新版登录页面特征
      await this.waitForElement(this.platformConfigs['直销'].selectors.newVersion.username, 2000);
      return 'newVersion';
    } catch {
      return 'oldVersion';
    }
  }

  // 直销平台登录
  async loginFKW(account) {
    const { username, password, staffUsername, staffPassword } = account;
    const version = await this.detectLoginPageVersion('直销');
    const selectors = this.platformConfigs['直销'].selectors[version];
    
    console.log(`检测到直销${version === 'newVersion' ? '新版' : '旧版'}登录页面`);
    
    try {
      // 填写用户名
      await this.fillInput(selectors.username, username);
      
      if (version === 'newVersion') {
        // 新版登录逻辑
        if (staffUsername && staffUsername !== 'boss') {
          // 需要员工登录
          await this.clickElement(selectors.staffCheckbox);
          await this.fillInput(selectors.staffUsername, staffUsername);
          await this.fillInput(selectors.password, staffPassword || password);
        } else {
          // 普通登录
          await this.fillInput(selectors.password, password);
        }
      } else {
        // 旧版登录逻辑
        await this.fillInput(selectors.password, password);
      }
      
      // 点击登录按钮
      await this.clickElement(selectors.loginButton, 1000);
      
      // 等待登录完成并关闭可能的弹窗
      setTimeout(() => this.closePopups(), 3000);
      
      return { success: true, message: '登录请求已发送' };
    } catch (error) {
      return { success: false, message: `登录失败: ${error.message}` };
    }
  }

  // OEM平台登录
  async loginOEM(account) {
    const { username, password, staffUsername, staffPassword } = account;
    const selectors = this.platformConfigs['OEM'].selectors;
    
    try {
      await this.fillInput(selectors.username, username);
      
      if (staffUsername && staffUsername !== 'boss') {
        await this.clickElement(selectors.staffCheckbox);
        await this.fillInput(selectors.staffUsername, staffUsername);
        await this.fillInput(selectors.password, staffPassword || password);
      } else {
        await this.fillInput(selectors.password, password);
      }
      
      await this.clickElement(selectors.loginButton, 1000);
      
      setTimeout(() => this.closePopups(), 3000);
      
      return { success: true, message: '登录请求已发送' };
    } catch (error) {
      return { success: false, message: `登录失败: ${error.message}` };
    }
  }

  // 代理商平台登录
  async loginAgency(account) {
    const { username, password } = account;
    const selectors = this.platformConfigs['代理商平台'].selectors;
    
    try {
      // 选择代理商登录类型
      await this.clickElement(selectors.loginType);
      
      await this.fillInput(selectors.username, username);
      await this.fillInput(selectors.password, password);
      await this.clickElement(selectors.loginButton, 1000);
      
      return { success: true, message: '登录请求已发送' };
    } catch (error) {
      return { success: false, message: `登录失败: ${error.message}` };
    }
  }

  // OSS平台登录
  async loginOSS(account) {
    const { username, password } = account;
    const selectors = this.platformConfigs['OSS'].selectors;
    
    try {
      await this.fillInput(selectors.username, username);
      await this.fillInput(selectors.password, password);
      await this.clickElement(selectors.loginButton, 1000);
      
      return { success: true, message: '登录请求已发送' };
    } catch (error) {
      return { success: false, message: `登录失败: ${error.message}` };
    }
  }

  // 关闭登录后的弹窗
  closePopups() {
    const popupSelectors = [
      '.close', '.btnClose', '.popup-close',
      '[class*="close"]', '[class*="modal"] .close',
      '.layui-layer-close', '.el-dialog__close'
    ];
    
    popupSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element.offsetParent !== null) { // 检查元素是否可见
          element.click();
        }
      });
    });
  }

  // 主登录方法
  async performLogin(account) {
    const { platform } = account;
    
    console.log(`开始自动登录 ${platform} 平台...`);
    
    try {
      let result;
      
      switch (platform) {
        case '直销':
          result = await this.loginFKW(account);
          break;
        case 'OEM':
          result = await this.loginOEM(account);
          break;
        case '代理商平台':
          result = await this.loginAgency(account);
          break;
        case 'OSS':
          result = await this.loginOSS(account);
          break;
        default:
          result = { success: false, message: `不支持的平台: ${platform}` };
      }
      
      return result;
    } catch (error) {
      return { success: false, message: `登录过程中发生错误: ${error.message}` };
    }
  }
}

// 创建全局实例
window.AutoLogin = new AutoLogin();

// 导出供模块使用
export default AutoLogin;
