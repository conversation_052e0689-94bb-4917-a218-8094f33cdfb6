// 基于Login.py的自动登录实现
// 使用更可靠的元素定位和登录逻辑

class AutoLoginService {
  constructor() {
    this.isRunning = false;
  }

  // 等待元素出现
  async waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        } else {
          setTimeout(checkElement, 100);
        }
      };
      
      checkElement();
    });
  }

  // 模拟人工输入
  async simulateTyping(element, text) {
    if (!element || !text) {
      console.warn('元素或文本为空，跳过输入');
      return;
    }

    console.log('开始模拟输入:', text);

    // 聚焦元素
    element.focus();
    element.click();

    // 清空现有内容
    element.value = '';
    element.dispatchEvent(new Event('input', { bubbles: true }));

    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 200));

    // 逐字符输入
    for (let i = 0; i < text.length; i++) {
      element.value += text[i];

      // 触发多种事件确保兼容性
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('keydown', { bubbles: true }));
      element.dispatchEvent(new Event('keyup', { bubbles: true }));

      await new Promise(resolve => setTimeout(resolve, 80)); // 每个字符间隔80ms
    }

    // 最终触发change和blur事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('blur', { bubbles: true }));

    console.log('输入完成，当前值:', element.value);
  }

  // 检查是否为新版登录页面
  isNewLoginPage() {
    return !!document.getElementById('loginAcct') ||
           !!document.querySelector('input[placeholder*="faisco"]') ||
           !!document.querySelector('input[type="text"]');
  }

  // 直销平台登录 - 基于Login.py的fkw_login函数和实际页面元素
  async fkwLogin(account, password, staff = '', staffPassword = '') {
    try {
      console.log('开始直销平台登录:', { account, staff });

      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 根据开发者工具中看到的实际元素进行定位
      const accountInput = document.querySelector('input[type="text"]') ||
                          document.querySelector('input[placeholder*="faisco"]') ||
                          document.querySelector('#loginAcct');

      const passwordInput = document.querySelector('input[type="password"]') ||
                           document.querySelector('#log-pwd');

      // 查找员工勾选框 - 根据页面结构查找checkbox
      const staffCheckbox = document.querySelector('input[type="checkbox"]') ||
                           document.querySelector('#staffLogin');

      // 查找登录按钮 - 蓝色的"登录"按钮
      const loginButton = document.querySelector('button[type="submit"]') ||
                         document.querySelector('.login-btn') ||
                         document.querySelector('button:contains("登录")') ||
                         document.querySelector('#login-button') ||
                         document.querySelector('button');

      console.log('找到的元素:', {
        accountInput: !!accountInput,
        passwordInput: !!passwordInput,
        staffCheckbox: !!staffCheckbox,
        loginButton: !!loginButton
      });

      if (!accountInput) {
        throw new Error('未找到账号输入框');
      }

      if (!passwordInput) {
        throw new Error('未找到密码输入框');
      }

      // 首先确保员工勾选框未选中（默认不勾选成员账号）
      if (staffCheckbox && staffCheckbox.checked) {
        staffCheckbox.click();
        console.log('已取消员工登录勾选');
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // 输入主账号
      console.log('开始输入账号:', account);
      await this.simulateTyping(accountInput, account);
      console.log('账号输入完成:', account);

      if (staff === '' || staff === 'boss' || !staff) {
        // 普通登录模式
        console.log('使用普通登录模式');
        console.log('开始输入密码:', password);
        await this.simulateTyping(passwordInput, password);
        console.log('密码输入完成');
      } else {
        // 员工登录模式
        console.log('使用员工登录模式:', staff);

        // 勾选员工登录
        if (staffCheckbox) {
          staffCheckbox.click();
          console.log('员工登录勾选成功');
          await new Promise(resolve => setTimeout(resolve, 500)); // 等待员工输入框出现

          // 查找员工账号输入框
          const staffInput = document.querySelector('#loginSacct') ||
                           document.querySelector('input[placeholder*="员工"]');

          if (staffInput) {
            await this.simulateTyping(staffInput, staff);
            console.log('员工账号输入完成:', staff);
          }
        }

        // 输入员工密码
        const usePassword = staffPassword && staffPassword.trim() !== '' ? staffPassword : password;
        console.log('开始输入员工密码:', usePassword);
        await this.simulateTyping(passwordInput, usePassword);
        console.log('员工密码输入完成');
      }

      // 等待一下再点击登录按钮
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 点击登录按钮
      if (loginButton) {
        console.log('准备点击登录按钮');
        loginButton.click();
        console.log('登录按钮点击成功');
      } else {
        // 如果找不到按钮，尝试其他方式
        const allButtons = document.querySelectorAll('button');
        console.log('找到的所有按钮:', allButtons.length);

        for (let i = 0; i < allButtons.length; i++) {
          const btn = allButtons[i];
          if (btn.textContent.includes('登录') || btn.textContent.includes('Login')) {
            btn.click();
            console.log('通过文本匹配点击登录按钮');
            break;
          }
        }

        if (allButtons.length === 0) {
          throw new Error('未找到登录按钮');
        }
      }

      // 等待登录完成
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 关闭可能的弹窗
      setTimeout(() => this.closePopups(), 3000);

      return { success: true, message: '登录请求已发送' };

    } catch (error) {
      console.error('直销登录失败:', error);
      return { success: false, message: '登录失败: ' + error.message };
    }
  }

  // 关闭登录后的弹窗 - 对应Login.py中的close_pop函数
  closePopups() {
    try {
      const popupSelectors = [
        '.close',
        '.btnClose',
        '.popup-close',
        '[class*="close"]',
        '.layui-layer-close',
        '.el-dialog__close',
        '.modal-close',
        '.dialog-close'
      ];
      
      popupSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (element.offsetParent !== null) { // 检查元素是否可见
            element.click();
            console.log('关闭弹窗:', selector);
          }
        });
      });
    } catch (error) {
      console.warn('关闭弹窗时出错:', error);
    }
  }

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      z-index: 10000;
      max-width: 300px;
      word-wrap: break-word;
      background-color: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 执行自动登录
  async performAutoLogin(account) {
    if (this.isRunning) {
      return { success: false, message: '自动登录正在进行中...' };
    }

    this.isRunning = true;

    try {
      const { platform, username, password, staffUsername, staffPassword } = account;
      
      console.log('开始自动登录:', { platform, username });
      
      // 等待页面完全加载
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }
      
      // 额外等待确保页面元素加载完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      let result;
      
      switch (platform) {
        case '直销':
          result = await this.fkwLogin(username, password, staffUsername, staffPassword);
          break;
        case 'OEM':
          result = { success: false, message: 'OEM登录功能待实现' };
          break;
        case '代理商平台':
          result = { success: false, message: '代理商登录功能待实现' };
          break;
        default:
          result = { success: false, message: '不支持的平台: ' + platform };
      }
      
      // 显示结果通知
      if (result.success) {
        this.showNotification('自动登录成功', 'success');
      } else {
        this.showNotification('自动登录失败: ' + result.message, 'error');
      }
      
      return result;
      
    } catch (error) {
      console.error('自动登录失败:', error);
      const errorMsg = '自动登录失败: ' + error.message;
      this.showNotification(errorMsg, 'error');
      return { success: false, message: errorMsg };
    } finally {
      this.isRunning = false;
    }
  }
}

export default new AutoLoginService();
