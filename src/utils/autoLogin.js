// 基于Login.py的自动登录实现
// 使用更可靠的元素定位和登录逻辑

class AutoLoginService {
  constructor() {
    this.isRunning = false;
  }

  // 等待元素出现
  async waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        } else {
          setTimeout(checkElement, 100);
        }
      };
      
      checkElement();
    });
  }

  // 模拟人工输入
  async simulateTyping(element, text) {
    element.value = '';
    element.focus();
    
    // 逐字符输入
    for (let i = 0; i < text.length; i++) {
      element.value += text[i];
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await new Promise(resolve => setTimeout(resolve, 50)); // 每个字符间隔50ms
    }
    
    // 触发change和blur事件
    element.dispatchEvent(new Event('change', { bubbles: true }));
    element.dispatchEvent(new Event('blur', { bubbles: true }));
  }

  // 检查是否为新版登录页面
  isNewLoginPage() {
    return !!document.getElementById('loginAcct');
  }

  // 直销平台登录 - 基于Login.py的fkw_login函数
  async fkwLogin(account, password, staff = '', staffPassword = '') {
    try {
      console.log('开始直销平台登录:', { account, staff });
      
      const isNewPage = this.isNewLoginPage();
      
      if (isNewPage) {
        console.log('检测到新版登录页面');
        
        // 新版登录页面元素 - 对应Login.py中的element[3], element[4], element[5]等
        const accountInput = document.getElementById('loginAcct');
        const passwordInput = document.getElementById('log-pwd');
        const loginButton = document.getElementById('login-button');
        const staffInput = document.getElementById('loginSacct');
        const staffCheckbox = document.getElementById('staffLogin');
        
        if (!accountInput) {
          throw new Error('未找到账号输入框');
        }
        
        // 首先确保员工勾选框未选中（默认不勾选成员账号）
        if (staffCheckbox && staffCheckbox.checked) {
          staffCheckbox.click();
          console.log('已取消员工登录勾选');
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        // 输入主账号
        await this.simulateTyping(accountInput, account);
        console.log('账号输入完成:', account);
        
        if (staff === '' || staff === 'boss') {
          // 普通登录模式
          console.log('使用普通登录模式');
          if (passwordInput) {
            await this.simulateTyping(passwordInput, password);
            console.log('密码输入完成');
          }
        } else {
          // 员工登录模式
          console.log('使用员工登录模式:', staff);
          
          // 勾选员工登录
          if (staffCheckbox) {
            staffCheckbox.click();
            console.log('员工登录勾选成功');
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待员工输入框出现
          }
          
          // 输入员工账号
          if (staffInput) {
            await this.simulateTyping(staffInput, staff);
            console.log('员工账号输入完成:', staff);
          }
          
          // 输入员工密码
          if (passwordInput) {
            const usePassword = staffPassword && staffPassword.trim() !== '' ? staffPassword : password;
            await this.simulateTyping(passwordInput, usePassword);
            console.log('员工密码输入完成');
          }
        }
        
        // 等待一下再点击登录按钮
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // 点击登录按钮
        if (loginButton) {
          loginButton.click();
          console.log('登录按钮点击成功');
        } else {
          throw new Error('未找到登录按钮');
        }
        
      } else {
        console.log('检测到旧版登录页面');
        
        // 旧版登录页面元素 - 对应Login.py中的element[0], element[1], element[2]
        const accountInput = document.querySelector('input[name="username"]') || 
                           document.querySelector('#username') ||
                           document.querySelector('input[placeholder*="用户名"]');
        const passwordInput = document.querySelector('input[name="password"]') ||
                            document.querySelector('#password') ||
                            document.querySelector('input[type="password"]');
        const loginButton = document.querySelector('input[type="submit"]') ||
                          document.querySelector('button[type="submit"]') ||
                          document.querySelector('.login-btn');
        
        if (accountInput) {
          await this.simulateTyping(accountInput, account);
          console.log('旧版页面账号输入完成');
        }
        
        if (passwordInput) {
          await this.simulateTyping(passwordInput, password);
          console.log('旧版页面密码输入完成');
        }
        
        if (loginButton) {
          loginButton.click();
          console.log('旧版页面登录按钮点击成功');
        }
      }
      
      // 等待登录完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 关闭可能的弹窗
      setTimeout(() => this.closePopups(), 3000);
      
      return { success: true, message: '登录请求已发送' };
      
    } catch (error) {
      console.error('直销登录失败:', error);
      return { success: false, message: '登录失败: ' + error.message };
    }
  }

  // 关闭登录后的弹窗 - 对应Login.py中的close_pop函数
  closePopups() {
    try {
      const popupSelectors = [
        '.close',
        '.btnClose',
        '.popup-close',
        '[class*="close"]',
        '.layui-layer-close',
        '.el-dialog__close',
        '.modal-close',
        '.dialog-close'
      ];
      
      popupSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (element.offsetParent !== null) { // 检查元素是否可见
            element.click();
            console.log('关闭弹窗:', selector);
          }
        });
      });
    } catch (error) {
      console.warn('关闭弹窗时出错:', error);
    }
  }

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      z-index: 10000;
      max-width: 300px;
      word-wrap: break-word;
      background-color: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // 执行自动登录
  async performAutoLogin(account) {
    if (this.isRunning) {
      return { success: false, message: '自动登录正在进行中...' };
    }

    this.isRunning = true;

    try {
      const { platform, username, password, staffUsername, staffPassword } = account;
      
      console.log('开始自动登录:', { platform, username });
      
      // 等待页面完全加载
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }
      
      // 额外等待确保页面元素加载完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      let result;
      
      switch (platform) {
        case '直销':
          result = await this.fkwLogin(username, password, staffUsername, staffPassword);
          break;
        case 'OEM':
          result = { success: false, message: 'OEM登录功能待实现' };
          break;
        case '代理商平台':
          result = { success: false, message: '代理商登录功能待实现' };
          break;
        default:
          result = { success: false, message: '不支持的平台: ' + platform };
      }
      
      // 显示结果通知
      if (result.success) {
        this.showNotification('自动登录成功', 'success');
      } else {
        this.showNotification('自动登录失败: ' + result.message, 'error');
      }
      
      return result;
      
    } catch (error) {
      console.error('自动登录失败:', error);
      const errorMsg = '自动登录失败: ' + error.message;
      this.showNotification(errorMsg, 'error');
      return { success: false, message: errorMsg };
    } finally {
      this.isRunning = false;
    }
  }
}

export default new AutoLoginService();
