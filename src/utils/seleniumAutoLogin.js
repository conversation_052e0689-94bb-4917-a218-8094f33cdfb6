// Selenium自动登录服务
// 基于Login.py的实现模式，使用JavaScript调用Python Selenium脚本

class SeleniumAutoLoginService {
  constructor() {
    this.isRunning = false;
    this.currentProcess = null;
  }

  // 检查是否支持自动登录
  isSupported() {
    // 检查是否在本地环境且支持调用Python脚本
    return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  }

  // 执行自动登录
  async performAutoLogin(account) {
    if (this.isRunning) {
      throw new Error('自动登录正在进行中，请稍候...');
    }

    if (!this.isSupported()) {
      throw new Error('自动登录功能仅在本地环境下可用');
    }

    try {
      this.isRunning = true;
      console.log('🚀 开始执行Selenium自动登录...', account);

      // 方法1: 尝试调用本地Python脚本
      const result = await this.callPythonScript(account);
      
      if (result.success) {
        console.log('✅ Selenium自动登录成功');
        return { success: true, message: '自动登录成功' };
      } else {
        // 方法2: 如果Python脚本调用失败，使用JavaScript注入方式
        console.log('⚠️ Python脚本调用失败，尝试JavaScript注入方式...');
        return await this.fallbackToJavaScriptInjection(account);
      }

    } catch (error) {
      console.error('❌ 自动登录失败:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  // 调用Python Selenium脚本
  async callPythonScript(account) {
    try {
      // 构建Python脚本参数
      const scriptArgs = {
        url: account.url,
        username: account.username,
        password: account.password,
        staffUsername: account.staffUsername || '',
        staffPassword: account.staffPassword || '',
        platform: account.platform,
        environment: account.environment
      };

      // 尝试通过fetch调用本地API（如果有的话）
      const response = await fetch('/api/selenium-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scriptArgs)
      });

      if (response.ok) {
        const result = await response.json();
        return { success: true, data: result };
      } else {
        throw new Error('API调用失败');
      }

    } catch (error) {
      console.log('Python脚本调用失败，将使用备用方案:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 备用方案：JavaScript注入自动登录
  async fallbackToJavaScriptInjection(account) {
    try {
      // 打开新窗口
      const newWindow = window.open(account.url, '_blank');
      
      if (!newWindow) {
        throw new Error('无法打开新窗口，请检查浏览器弹窗设置');
      }

      // 等待页面加载
      await this.sleep(3000);

      // 注入自动登录脚本
      const loginScript = this.generateLoginScript(account);
      
      try {
        // 尝试注入脚本
        newWindow.eval(loginScript);
        return { success: true, message: '自动登录脚本已注入' };
      } catch (error) {
        console.error('脚本注入失败:', error);
        // 如果注入失败，提供手动复制的脚本
        this.showManualScript(loginScript, account);
        return { success: true, message: '请手动执行登录脚本' };
      }

    } catch (error) {
      throw new Error(`JavaScript注入失败: ${error.message}`);
    }
  }

  // 生成登录脚本（基于Login.py的逻辑）
  generateLoginScript(account) {
    return `
(function() {
  console.log('🚀 开始执行自动登录脚本...');
  
  const account = ${JSON.stringify(account)};
  
  // 工具函数
  function sleep(ms) { 
    return new Promise(resolve => setTimeout(resolve, ms)); 
  }
  
  async function typeText(element, text) {
    if (!element) return false;
    console.log('📝 开始输入:', text);
    element.focus();
    element.click();
    element.value = '';
    element.dispatchEvent(new Event('input', { bubbles: true }));
    await sleep(200);
    
    for (let i = 0; i < text.length; i++) {
      element.value += text[i];
      element.dispatchEvent(new Event('input', { bubbles: true }));
      await sleep(100);
    }
    
    element.dispatchEvent(new Event('change', { bubbles: true }));
    console.log('✅ 输入完成:', element.value);
    return true;
  }
  
  // 检测登录页面类型（基于Login.py的is_loginPage_new逻辑）
  function detectLoginPageType() {
    // 检测新版登录页面元素
    const newPageSelectors = [
      'input[placeholder*="用户名"]',
      'input[placeholder*="账号"]',
      'input[placeholder*="手机号"]',
      '.login-form input[type="text"]'
    ];
    
    for (const selector of newPageSelectors) {
      if (document.querySelector(selector)) {
        return 'new';
      }
    }
    
    return 'old';
  }
  
  // 直销登录逻辑（基于Login.py的fkw_login）
  async function performFkwLogin() {
    const pageType = detectLoginPageType();
    console.log('🔍 检测到页面类型:', pageType);
    
    let usernameInput, passwordInput, staffInput, staffCheckbox, loginButton;
    
    if (pageType === 'new') {
      // 新版登录页面元素选择器
      usernameInput = document.querySelector('input[placeholder*="用户名"]') ||
                     document.querySelector('input[placeholder*="账号"]') ||
                     document.querySelector('input[placeholder*="手机号"]') ||
                     document.querySelector('.login-form input[type="text"]') ||
                     document.querySelectorAll('input[type="text"]')[0];
                     
      passwordInput = document.querySelector('input[type="password"]');
      
      staffInput = document.querySelector('input[placeholder*="员工"]') ||
                  document.querySelector('input[placeholder*="staff"]');
                  
      staffCheckbox = document.querySelector('input[type="checkbox"]') ||
                     document.querySelector('.checkbox input') ||
                     document.querySelector('[class*="staff"] input[type="checkbox"]');
                     
      loginButton = document.querySelector('button[type="submit"]') ||
                   document.querySelector('.login-btn') ||
                   document.querySelector('button:contains("登录")') ||
                   document.querySelector('button');
    } else {
      // 旧版登录页面元素选择器
      usernameInput = document.querySelector('#username') ||
                     document.querySelector('input[name="username"]') ||
                     document.querySelectorAll('input[type="text"]')[0];
                     
      passwordInput = document.querySelector('#password') ||
                     document.querySelector('input[name="password"]') ||
                     document.querySelector('input[type="password"]');
                     
      loginButton = document.querySelector('#loginBtn') ||
                   document.querySelector('input[type="submit"]') ||
                   document.querySelector('button[type="submit"]') ||
                   document.querySelector('button');
    }
    
    console.log('🔍 找到元素:', {
      username: !!usernameInput,
      password: !!passwordInput,
      staff: !!staffInput,
      checkbox: !!staffCheckbox,
      button: !!loginButton
    });
    
    // 执行登录步骤
    await sleep(1000);
    
    // 处理员工账号复选框（默认不选中）
    if (staffCheckbox && staffCheckbox.checked) {
      staffCheckbox.click();
      await sleep(300);
      console.log('✅ 已取消员工账号选择');
    }
    
    // 输入用户名
    if (usernameInput) {
      await typeText(usernameInput, account.username);
      await sleep(500);
    }
    
    // 处理员工登录
    if (account.staffUsername && account.staffUsername !== '' && account.staffUsername !== 'boss') {
      if (staffCheckbox && !staffCheckbox.checked) {
        staffCheckbox.click();
        await sleep(300);
        console.log('✅ 已选择员工账号登录');
      }
      
      if (staffInput) {
        await typeText(staffInput, account.staffUsername);
        await sleep(500);
      }
      
      if (passwordInput) {
        await typeText(passwordInput, account.staffPassword || account.password);
        await sleep(500);
      }
    } else {
      // 普通用户登录
      if (passwordInput) {
        await typeText(passwordInput, account.password);
        await sleep(500);
      }
    }
    
    // 点击登录按钮
    await sleep(1000);
    if (loginButton) {
      loginButton.click();
      console.log('✅ 登录按钮已点击');
    }
    
    // 等待登录完成并处理可能的弹窗
    await sleep(3000);
    await closeLoginPopups();
    
    console.log('🎉 自动登录完成！');
  }
  
  // 关闭登录后的弹窗（基于Login.py的close_pop）
  async function closeLoginPopups() {
    try {
      const closeSelectors = [
        '.close',
        '.btnClose',
        '.modal-close',
        '.popup-close',
        '[class*="close"]',
        'button:contains("关闭")',
        'button:contains("确定")'
      ];
      
      for (const selector of closeSelectors) {
        const closeBtn = document.querySelector(selector);
        if (closeBtn && closeBtn.offsetParent !== null) {
          closeBtn.click();
          console.log('✅ 已关闭弹窗');
          await sleep(500);
        }
      }
    } catch (error) {
      console.log('弹窗处理完成');
    }
  }
  
  // OEM登录逻辑（基于Login.py的oem_login）
  async function performOemLogin() {
    console.log('🔍 执行OEM登录...');
    
    const usernameInput = document.querySelector('input[placeholder*="账号"]') ||
                         document.querySelector('input[name="username"]') ||
                         document.querySelectorAll('input[type="text"]')[0];
                         
    const staffInput = document.querySelector('input[placeholder*="员工"]') ||
                      document.querySelectorAll('input[type="text"]')[1];
                      
    const passwordInput = document.querySelector('input[type="password"]');
    
    const staffCheckbox = document.querySelector('input[type="checkbox"]');
    
    const loginButton = document.querySelector('button[type="submit"]') ||
                       document.querySelector('button');
    
    await sleep(1000);
    
    // 输入账号
    if (usernameInput) {
      await typeText(usernameInput, account.username);
      await sleep(500);
    }
    
    // 处理员工登录
    if (account.staffUsername && account.staffUsername !== '' && account.staffUsername !== 'boss') {
      if (staffCheckbox && !staffCheckbox.checked) {
        staffCheckbox.click();
        await sleep(300);
      }
      
      if (staffInput) {
        await typeText(staffInput, account.staffUsername);
        await sleep(500);
      }
      
      if (passwordInput) {
        await typeText(passwordInput, account.staffPassword || account.password);
        await sleep(500);
      }
    } else {
      if (passwordInput) {
        await typeText(passwordInput, account.password);
        await sleep(500);
      }
    }
    
    // 点击登录
    await sleep(1000);
    if (loginButton) {
      loginButton.click();
      console.log('✅ OEM登录按钮已点击');
    }
    
    await sleep(3000);
    await closeLoginPopups();
    console.log('🎉 OEM自动登录完成！');
  }
  
  // 主执行函数
  async function executeLogin() {
    try {
      await sleep(2000); // 等待页面完全加载
      
      if (account.platform === 'OEM') {
        await performOemLogin();
      } else {
        await performFkwLogin();
      }
      
    } catch (error) {
      console.error('❌ 登录过程中出错:', error);
    }
  }
  
  // 开始执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', executeLogin);
  } else {
    executeLogin();
  }
})();
`;
  }

  // 显示手动脚本
  showManualScript(script, account) {
    const message = `
自动注入失败，请手动执行以下步骤：

1. 确保已打开登录页面：${account.url}
2. 按F12打开开发者工具
3. 切换到Console标签
4. 复制并粘贴以下脚本，然后按回车执行：

${script}
`;

    // 复制脚本到剪贴板
    if (navigator.clipboard) {
      navigator.clipboard.writeText(script).then(() => {
        alert(message + '\n\n脚本已复制到剪贴板！');
      });
    } else {
      alert(message);
    }
  }

  // 工具函数：延迟
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 停止当前登录过程
  stop() {
    this.isRunning = false;
    if (this.currentProcess) {
      this.currentProcess.abort();
      this.currentProcess = null;
    }
  }
}

// 导出单例
const seleniumAutoLoginService = new SeleniumAutoLoginService();
export default seleniumAutoLoginService;
