// Selenium代理服务
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// 代理Selenium服务
app.use('/api/selenium', createProxyMiddleware({
  target: 'http://localhost:5001',
  changeOrigin: true,
  pathRewrite: {
    '^/api/selenium': '', // 移除/api/selenium前缀
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).json({
      success: false,
      message: 'Selenium服务连接失败，请确保服务已启动'
    });
  }
}));

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🔗 Selenium代理服务启动在端口 ${PORT}`);
  console.log(`📋 代理路径: http://localhost:${PORT}/api/selenium/*`);
});
