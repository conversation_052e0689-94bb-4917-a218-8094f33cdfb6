// 直接在浏览器控制台运行的自动登录脚本
// 基于您提供的页面截图制作

(function() {
    console.log('🚀 开始执行自动登录脚本...');
    
    // 账号信息
    const account = {
        username: 'la19281',
        password: 'faker0507'
    };
    
    // 等待函数
    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 模拟人工输入
    async function typeText(element, text) {
        if (!element) {
            console.error('❌ 元素不存在');
            return false;
        }
        
        console.log(`📝 开始输入: ${text}`);
        
        // 聚焦元素
        element.focus();
        element.click();
        
        // 清空
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));
        
        await sleep(200);
        
        // 逐字符输入
        for (let i = 0; i < text.length; i++) {
            element.value += text[i];
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('keydown', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
            await sleep(100);
        }
        
        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        element.dispatchEvent(new Event('blur', { bubbles: true }));
        
        console.log(`✅ 输入完成: ${element.value}`);
        return true;
    }
    
    // 主要登录函数
    async function performLogin() {
        try {
            console.log('🔍 查找页面元素...');
            
            // 等待页面完全加载
            await sleep(2000);
            
            // 查找账号输入框
            let usernameInput = document.querySelector('input[type="text"]') ||
                               document.querySelector('input[placeholder*="faisco"]') ||
                               document.querySelector('#loginAcct') ||
                               document.querySelector('input[name="username"]');
            
            if (!usernameInput) {
                // 尝试查找所有input元素
                const allInputs = document.querySelectorAll('input');
                console.log('📋 找到的所有input元素:', allInputs.length);
                
                for (let i = 0; i < allInputs.length; i++) {
                    const input = allInputs[i];
                    console.log(`Input ${i}:`, {
                        type: input.type,
                        name: input.name,
                        id: input.id,
                        placeholder: input.placeholder,
                        className: input.className
                    });
                    
                    // 如果是第一个非checkbox/radio的input，可能是用户名
                    if (input.type !== 'checkbox' && input.type !== 'radio' && input.type !== 'submit' && input.type !== 'button') {
                        if (!usernameInput) {
                            usernameInput = input;
                            console.log('🎯 选择作为用户名输入框:', input);
                        }
                    }
                }
            }
            
            // 查找密码输入框
            let passwordInput = document.querySelector('input[type="password"]') ||
                               document.querySelector('#log-pwd') ||
                               document.querySelector('input[name="password"]');
            
            // 查找员工勾选框
            let staffCheckbox = document.querySelector('input[type="checkbox"]') ||
                               document.querySelector('#staffLogin');
            
            // 查找登录按钮
            let loginButton = document.querySelector('button[type="submit"]') ||
                             document.querySelector('#login-button') ||
                             document.querySelector('button') ||
                             document.querySelector('.login-btn') ||
                             document.querySelector('input[type="submit"]');
            
            console.log('🔍 元素查找结果:', {
                usernameInput: !!usernameInput,
                passwordInput: !!passwordInput,
                staffCheckbox: !!staffCheckbox,
                loginButton: !!loginButton
            });
            
            if (!usernameInput) {
                throw new Error('❌ 未找到用户名输入框');
            }
            
            if (!passwordInput) {
                throw new Error('❌ 未找到密码输入框');
            }
            
            // 确保员工勾选框未选中
            if (staffCheckbox && staffCheckbox.checked) {
                console.log('📋 取消员工登录勾选');
                staffCheckbox.click();
                await sleep(300);
            }
            
            // 输入用户名
            console.log('👤 输入用户名...');
            await typeText(usernameInput, account.username);
            
            await sleep(500);
            
            // 输入密码
            console.log('🔐 输入密码...');
            await typeText(passwordInput, account.password);
            
            await sleep(1000);
            
            // 点击登录按钮
            if (loginButton) {
                console.log('🖱️ 点击登录按钮...');
                loginButton.click();
                console.log('✅ 登录按钮已点击');
            } else {
                // 尝试查找所有按钮
                const allButtons = document.querySelectorAll('button');
                console.log('🔍 找到的所有按钮:', allButtons.length);
                
                for (let i = 0; i < allButtons.length; i++) {
                    const btn = allButtons[i];
                    console.log(`Button ${i}:`, {
                        text: btn.textContent,
                        type: btn.type,
                        className: btn.className
                    });
                    
                    if (btn.textContent.includes('登录') || btn.textContent.includes('Login')) {
                        btn.click();
                        console.log('✅ 通过文本匹配点击登录按钮');
                        break;
                    }
                }
            }
            
            // 等待登录完成
            await sleep(3000);
            
            // 关闭可能的弹窗
            const popupSelectors = [
                '.close', '.btnClose', '.popup-close',
                '[class*="close"]', '.layui-layer-close',
                '.el-dialog__close', '.modal-close'
            ];
            
            popupSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element.offsetParent !== null) {
                        element.click();
                        console.log('🗙 关闭弹窗:', selector);
                    }
                });
            });
            
            console.log('🎉 自动登录脚本执行完成！');
            
            // 显示成功通知
            showNotification('自动登录脚本执行完成！', 'success');
            
        } catch (error) {
            console.error('❌ 登录失败:', error);
            showNotification('登录失败: ' + error.message, 'error');
        }
    }
    
    // 显示通知
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            z-index: 10000;
            max-width: 400px;
            word-wrap: break-word;
            background-color: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            border: 2px solid white;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // 立即执行登录
    performLogin();
    
})();

console.log('🎯 自动登录脚本已加载，正在执行...');
