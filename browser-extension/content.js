// 自动登录内容脚本
console.log('🚀 Auto Login Helper 已加载');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'autoLogin') {
    console.log('收到自动登录请求:', request.credentials);
    performAutoLogin(request.credentials);
    sendResponse({success: true});
  }
});

// 检查URL参数中是否有自动登录标记
function checkAutoLoginParams() {
  const urlParams = new URLSearchParams(window.location.search);
  const autoLogin = urlParams.get('autoLogin');
  const username = urlParams.get('username');
  const password = urlParams.get('password');
  
  if (autoLogin === 'true' && username && password) {
    console.log('检测到自动登录参数，开始执行...');
    setTimeout(() => {
      performAutoLogin({username, password});
    }, 2000);
  }
}

// 执行自动登录
async function performAutoLogin(credentials) {
  try {
    console.log('🚀 开始自动登录...');
    
    const {username, password} = credentials;
    
    // 等待页面加载完成
    await sleep(2000);
    
    // 查找用户名输入框
    let usernameInput = findElement([
      'input[type="text"]',
      'input[name="username"]',
      'input[name="user"]',
      'input[placeholder*="用户名"]',
      'input[placeholder*="账号"]',
      'input[placeholder*="手机"]',
      'input[placeholder*="邮箱"]',
      '#username',
      '#user',
      '.username'
    ]);
    
    // 查找密码输入框
    let passwordInput = findElement([
      'input[type="password"]',
      'input[name="password"]',
      'input[name="pwd"]',
      '#password',
      '#pwd',
      '.password'
    ]);
    
    if (!usernameInput || !passwordInput) {
      console.error('❌ 未找到登录输入框');
      showNotification('未找到登录输入框，请手动登录', 'error');
      return;
    }
    
    // 填写用户名
    console.log('📝 填写用户名:', username);
    await fillInput(usernameInput, username);
    
    // 填写密码
    console.log('📝 填写密码...');
    await fillInput(passwordInput, password);
    
    // 查找登录按钮
    let loginButton = findElement([
      '#login_button',
      '.login_button',
      'button[type="submit"]',
      'input[type="submit"]',
      'div[onclick*="login"]',
      'a[onclick*="login"]'
    ]);
    
    // 通过文本查找登录按钮
    if (!loginButton) {
      const allElements = document.querySelectorAll('button, div, a, span, input');
      for (const element of allElements) {
        const text = element.textContent?.trim() || element.value?.trim() || '';
        if ((text === '登录' || text === '登 录' || text === 'Login' || text === '立即登录') 
            && element.offsetParent !== null && !element.disabled) {
          loginButton = element;
          console.log('✅ 通过文本找到登录按钮:', text);
          break;
        }
      }
    }
    
    if (loginButton) {
      console.log('🔘 准备点击登录按钮...');
      await sleep(1000);
      
      // 点击登录按钮
      loginButton.click();
      console.log('✅ 登录按钮点击成功');
      
      // 显示成功提示
      showNotification('✅ 自动登录完成！', 'success');
    } else {
      console.error('❌ 未找到登录按钮');
      showNotification('账号密码已填入，请手动点击登录按钮', 'warning');
    }
    
  } catch (error) {
    console.error('❌ 自动登录过程出错:', error);
    showNotification('自动登录出错: ' + error.message, 'error');
  }
}

// 查找元素
function findElement(selectors) {
  for (const selector of selectors) {
    try {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        if (element.offsetParent !== null && !element.disabled) {
          console.log('✅ 找到元素:', selector);
          return element;
        }
      }
    } catch (e) {
      continue;
    }
  }
  return null;
}

// 填写输入框
async function fillInput(input, value) {
  input.focus();
  input.click();
  await sleep(300);
  
  // 清空输入框
  input.value = '';
  
  // 逐字符输入（模拟真实输入）
  for (let i = 0; i < value.length; i++) {
    input.value += value[i];
    input.dispatchEvent(new Event('input', { bubbles: true }));
    await sleep(50);
  }
  
  // 触发change事件
  input.dispatchEvent(new Event('change', { bubbles: true }));
  input.dispatchEvent(new Event('blur', { bubbles: true }));
  await sleep(300);
}

// 显示通知
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    color: white;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196F3'};
  `;
  notification.textContent = message;
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 工具函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 页面加载完成后检查自动登录参数
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkAutoLoginParams);
} else {
  checkAutoLoginParams();
}
