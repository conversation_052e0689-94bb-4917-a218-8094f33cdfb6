<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
    }
    .success {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #4caf50;
    }
    .info {
      background: #e3f2fd;
      color: #1565c0;
      border: 1px solid #2196f3;
    }
    .button {
      width: 100%;
      padding: 10px;
      background: #2196f3;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .button:hover {
      background: #1976d2;
    }
    .instructions {
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>🚀 Auto Login Helper</h3>
  </div>
  
  <div class="status info">
    <div>扩展已激活</div>
    <div style="font-size: 12px; margin-top: 5px;">
      访问登录页面时会自动执行登录
    </div>
  </div>
  
  <button class="button" id="testLogin">测试自动登录</button>
  
  <div class="instructions">
    <strong>使用说明：</strong><br>
    1. 在账号管理页面点击"自动登录"<br>
    2. 扩展会自动在新页面执行登录<br>
    3. 无需手动操作，完全自动化
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
