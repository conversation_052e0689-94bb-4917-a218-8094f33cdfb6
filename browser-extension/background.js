// 后台脚本
console.log('Auto Login Helper 后台脚本已启动');

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('收到消息:', request);
  
  if (request.action === 'openLoginPage') {
    // 打开登录页面并传递参数
    const {url, username, password} = request;
    const loginUrl = `${url}?autoLogin=true&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
    
    chrome.tabs.create({
      url: loginUrl,
      active: true
    });
    
    sendResponse({success: true});
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    console.log('标签页加载完成:', tab.url);
  }
});
