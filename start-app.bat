@echo off
title km测试工具合集
echo.
echo ========================================
echo    km测试工具合集 - 启动中...
echo ========================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未检测到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

REM 检查build文件夹是否存在
if not exist "build" (
    echo 正在构建应用...
    call npm run build
    if %errorlevel% neq 0 (
        echo 构建失败，请检查错误信息
        pause
        exit /b 1
    )
)

REM 安装serve（如果没有安装）
echo 检查serve工具...
npx serve --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装serve工具...
    npm install -g serve
)

REM 启动应用
echo.
echo 正在启动km测试工具合集...
echo 应用将在浏览器中自动打开
echo 地址：http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止应用
echo ========================================
echo.

REM 启动服务器并自动打开浏览器
start http://localhost:3000
npx serve -s build -l 3000

pause
