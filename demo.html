<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>测试工具集 - 演示版</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f8fafc;
        }
        
        .tool-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 1.5rem;
            transition: box-shadow 0.2s;
        }
        
        .tool-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background-color: #2563eb;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .input-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
        }
        
        .input-field:focus {
            ring: 2px;
            ring-color: #3b82f6;
            border-color: transparent;
        }
        
        .textarea-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
            resize: vertical;
        }
        
        .result-area {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            padding: 1rem;
            min-height: 100px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .tab-button {
            padding: 0.5rem 1rem;
            font-weight: 500;
            font-size: 0.875rem;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
            border-bottom: 2px solid;
            transition: all 0.2s;
            cursor: pointer;
            background: none;
            border-left: none;
            border-right: none;
            border-top: none;
        }
        
        .tab-button.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            background-color: #eff6ff;
        }
        
        .tab-button.inactive {
            color: #6b7280;
            border-bottom-color: transparent;
        }
        
        .tab-button.inactive:hover {
            color: #374151;
            border-bottom-color: #d1d5db;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Header组件
        const Header = () => {
            return (
                <header className="bg-white shadow-sm border-b border-gray-200">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <h1 className="text-2xl font-bold text-gray-800">Raina测试工具集</h1>
                            </div>
                            <nav className="flex items-center space-x-6">
                                <a href="#manual" className="flex items-center text-gray-600 hover:text-blue-600 transition-colors duration-200">
                                    <span className="mr-2">📖</span>
                                    使用手册
                                </a>
                            </nav>
                        </div>
                    </div>
                </header>
            );
        };

        // CategoryNav组件
        const CategoryNav = ({ categories, activeCategory, onCategoryChange }) => {
            return (
                <div className="mb-8">
                    <div className="flex flex-wrap gap-3 justify-center">
                        {categories.map((category) => (
                            <button
                                key={category.id}
                                onClick={() => onCategoryChange(category.name)}
                                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                                    activeCategory === category.name
                                        ? 'bg-blue-600 text-white shadow-md transform scale-105'
                                        : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 shadow-sm border border-gray-200'
                                }`}
                            >
                                {category.name}
                            </button>
                        ))}
                    </div>
                </div>
            );
        };

        // 简化的数据生成工具组件
        const DataGenerator = () => {
            const [activeTab, setActiveTab] = useState('random-content');
            const [inputText, setInputText] = useState('');
            const [outputText, setOutputText] = useState('');
            const [wordCount, setWordCount] = useState(100);

            const generateRandomContent = () => {
                const sampleTexts = [
                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                    '这是一个示例文章段落，包含了丰富的内容和信息。',
                    '从前有一个小村庄，村庄里住着一位善良的老人。',
                    '本文档介绍了系统的架构设计和实现方案。'
                ];
                
                let result = '';
                for (let i = 0; i < wordCount / 10; i++) {
                    result += sampleTexts[Math.floor(Math.random() * sampleTexts.length)] + ' ';
                }
                setOutputText(result.trim());
            };

            const generateEmails = () => {
                const domains = ['gmail.com', '163.com', 'qq.com', 'hotmail.com'];
                const emails = [];
                for (let i = 0; i < 10; i++) {
                    const username = `user${Math.floor(Math.random() * 10000)}`;
                    const domain = domains[Math.floor(Math.random() * domains.length)];
                    emails.push(`${username}@${domain}`);
                }
                setOutputText(emails.join('\n'));
            };

            const tabs = [
                { id: 'random-content', name: '随机内容生成器' },
                { id: 'email-generator', name: '随机邮箱生成器' }
            ];

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">数据生成工具</h2>
                    
                    <div className="flex gap-2 border-b border-gray-200">
                        {tabs.map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
                            >
                                {tab.name}
                            </button>
                        ))}
                    </div>

                    {activeTab === 'random-content' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">生成字数：</label>
                                    <input
                                        type="number"
                                        value={wordCount}
                                        onChange={(e) => setWordCount(parseInt(e.target.value) || 100)}
                                        className="input-field"
                                        min="1"
                                        max="10000"
                                    />
                                </div>
                                
                                <button onClick={generateRandomContent} className="btn-primary w-full">
                                    生成内容
                                </button>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
                                <textarea
                                    value={outputText}
                                    readOnly
                                    className="result-area h-64"
                                    placeholder="生成的内容将在这里显示..."
                                />
                            </div>
                        </div>
                    )}

                    {activeTab === 'email-generator' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <button onClick={generateEmails} className="btn-primary w-full">
                                    生成邮箱
                                </button>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
                                <textarea
                                    value={outputText}
                                    readOnly
                                    className="result-area h-64"
                                    placeholder="生成的邮箱将在这里显示..."
                                />
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // 简化的字数统计工具
        const TextCounter = () => {
            const [text, setText] = useState('');
            const [stats, setStats] = useState({
                charactersWithSpaces: 0,
                charactersWithoutSpaces: 0,
                words: 0,
                lines: 0
            });

            React.useEffect(() => {
                const charactersWithSpaces = text.length;
                const charactersWithoutSpaces = text.replace(/\s/g, '').length;
                const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
                const lines = text === '' ? 0 : text.split('\n').length;

                setStats({
                    charactersWithSpaces,
                    charactersWithoutSpaces,
                    words,
                    lines
                });
            }, [text]);

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">字数统计工具</h2>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-2">输入文本：</label>
                            <textarea
                                value={text}
                                onChange={(e) => setText(e.target.value)}
                                className="textarea-field h-96"
                                placeholder="请在此输入需要统计的文本内容..."
                            />
                        </div>
                        
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-gray-800">统计结果</h3>
                            
                            <div className="space-y-3">
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="text-sm text-blue-600 font-medium">字符数（含空格）</div>
                                    <div className="text-2xl font-bold text-blue-800">{stats.charactersWithSpaces}</div>
                                </div>
                                
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div className="text-sm text-green-600 font-medium">字符数（不含空格）</div>
                                    <div className="text-2xl font-bold text-green-800">{stats.charactersWithoutSpaces}</div>
                                </div>
                                
                                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                    <div className="text-sm text-purple-600 font-medium">单词数</div>
                                    <div className="text-2xl font-bold text-purple-800">{stats.words}</div>
                                </div>
                                
                                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                    <div className="text-sm text-orange-600 font-medium">行数</div>
                                    <div className="text-2xl font-bold text-orange-800">{stats.lines}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // 简化的随机题目工具
        const RandomQuestions = () => {
            const [questionBank, setQuestionBank] = useState('');
            const [generatedQuestions, setGeneratedQuestions] = useState([]);
            const [questionCount, setQuestionCount] = useState(5);

            const sampleQuestions = `# 单选题
1. JavaScript是一种什么类型的语言？
A. 编译型语言
B. 解释型语言
C. 汇编语言
D. 机器语言
答案：B

2. 以下哪个不是JavaScript的数据类型？
A. string
B. number
C. char
D. boolean
答案：C

# 判断题
3. JavaScript是区分大小写的语言。
答案：正确

4. HTML是一种编程语言。
答案：错误

# 填空题
5. JavaScript中声明变量使用关键字____或____。
答案：var|let`;

            const parseQuestions = (text) => {
                const questions = [];
                const lines = text.split('\n').filter(line => line.trim());
                let currentType = '';
                let currentQuestion = null;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    if (line.startsWith('#')) {
                        currentType = line.replace('#', '').trim();
                        continue;
                    }

                    if (/^\d+\./.test(line)) {
                        if (currentQuestion) {
                            questions.push(currentQuestion);
                        }

                        currentQuestion = {
                            id: questions.length + 1,
                            type: currentType,
                            question: line.replace(/^\d+\.\s*/, ''),
                            options: [],
                            answer: ''
                        };
                    }
                    else if (/^[A-Z]\./.test(line) && currentQuestion) {
                        currentQuestion.options.push(line);
                    }
                    else if (line.startsWith('答案：') && currentQuestion) {
                        currentQuestion.answer = line.replace('答案：', '');
                    }
                }

                if (currentQuestion) {
                    questions.push(currentQuestion);
                }

                return questions;
            };

            const generateQuestions = () => {
                if (!questionBank.trim()) {
                    alert('请先输入题目库内容');
                    return;
                }

                const allQuestions = parseQuestions(questionBank);
                if (allQuestions.length === 0) {
                    alert('未能解析到有效题目');
                    return;
                }

                const shuffled = allQuestions.sort(() => Math.random() - 0.5);
                const selected = shuffled.slice(0, Math.min(questionCount, shuffled.length));
                setGeneratedQuestions(selected);
            };

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">随机题目生成工具</h2>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                            <div className="flex items-center justify-between mb-2">
                                <label className="text-lg font-medium text-gray-800">题目库内容：</label>
                                <button
                                    onClick={() => setQuestionBank(sampleQuestions)}
                                    className="text-sm btn-secondary"
                                >
                                    加载示例
                                </button>
                            </div>
                            <textarea
                                value={questionBank}
                                onChange={(e) => setQuestionBank(e.target.value)}
                                className="textarea-field h-64"
                                placeholder="请按照格式输入题目内容，或点击'加载示例'..."
                            />
                        </div>

                        <div className="space-y-4">
                            <h3 className="text-lg font-medium text-gray-800">生成设置</h3>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">题目数量：</label>
                                <input
                                    type="number"
                                    value={questionCount}
                                    onChange={(e) => setQuestionCount(parseInt(e.target.value) || 5)}
                                    className="input-field"
                                    min="1"
                                    max="20"
                                />
                            </div>

                            <button onClick={generateQuestions} className="btn-primary w-full">
                                生成随机题目
                            </button>
                        </div>
                    </div>

                    {generatedQuestions.length > 0 && (
                        <div className="border-t border-gray-200 pt-6">
                            <h3 className="text-lg font-medium text-gray-800 mb-4">生成的题目</h3>
                            <div className="space-y-4">
                                {generatedQuestions.map((question, index) => (
                                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                                {question.type}
                                            </span>
                                            <span className="text-sm text-gray-500">第 {index + 1} 题</span>
                                        </div>

                                        <h4 className="font-medium text-gray-800 mb-2">
                                            {index + 1}. {question.question}
                                        </h4>

                                        {question.options.length > 0 && (
                                            <div className="mb-2">
                                                {question.options.map((option, optIndex) => (
                                                    <div key={optIndex} className="text-sm text-gray-700">
                                                        {option}
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        <div className="text-sm border-t border-gray-100 pt-2">
                                            <span className="font-medium text-green-600">答案：</span>
                                            <span className="text-green-700">{question.answer}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // 简化的Base64编码工具
        const EncodingTools = () => {
            const [inputText, setInputText] = useState('');
            const [outputText, setOutputText] = useState('');

            const base64Encode = () => {
                try {
                    const encoded = btoa(unescape(encodeURIComponent(inputText)));
                    setOutputText(encoded);
                } catch (error) {
                    setOutputText('编码失败：' + error.message);
                }
            };

            const base64Decode = () => {
                try {
                    const decoded = decodeURIComponent(escape(atob(inputText)));
                    setOutputText(decoded);
                } catch (error) {
                    setOutputText('解码失败：' + error.message);
                }
            };

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">编码/加密工具</h2>
                    
                    <div>
                        <h3 className="text-lg font-medium text-gray-800 mb-3">Base64编码</h3>
                        <textarea
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            className="textarea-field h-32 mb-4"
                            placeholder="输入要处理的文本..."
                        />
                        
                        <div className="flex gap-3 mb-4">
                            <button onClick={base64Encode} className="btn-primary">Base64编码</button>
                            <button onClick={base64Decode} className="btn-primary">Base64解码</button>
                            <button onClick={() => { setInputText(''); setOutputText(''); }} className="btn-secondary">清空</button>
                        </div>
                        
                        <div>
                            <h4 className="text-md font-medium text-gray-700 mb-2">输出结果</h4>
                            <textarea
                                value={outputText}
                                readOnly
                                className="result-area h-32"
                                placeholder="处理结果将在这里显示..."
                            />
                        </div>
                    </div>
                </div>
            );
        };

        // 简化的账号管理工具
        const AccountManager = () => {
            const [accounts, setAccounts] = useState([]);
            const [selectedPlatform, setSelectedPlatform] = useState('直销');
            const [newAccount, setNewAccount] = useState({
                platform: '直销',
                environment: '线上',
                username: '',
                password: '',
                staffUsername: '',
                staffPassword: '',
                url: '',
                note: ''
            });
            const [activeTab, setActiveTab] = useState('manager');

            // 预设的平台数据
            const platforms = {
                '直销': {
                    environments: ['线上', 'dep', '本地'],
                    urls: {
                        '线上': 'http://i.flw.com/',
                        'dep': 'http://i.fkw.com.faidev.cc/',
                        '本地': 'http://i.fff.com/'
                    }
                },
                'OEM': {
                    environments: ['线上', '本地'],
                    urls: {
                        '线上': 'http://adm.webportal.top/',
                        '本地': 'http://www.app.top/'
                    }
                },
                '代理商平台': {
                    environments: ['线上', '本地'],
                    urls: {
                        '线上': 'http://it.faisco.cn',
                        '本地': 'http://tk.faisco.cn/index'
                    }
                }
            };

            // 默认账号数据
            const defaultAccounts = [
                {
                    id: 1,
                    platform: '直销',
                    environment: '线上',
                    username: 'dannell',
                    password: 'faisco1234',
                    staffUsername: 'boss',
                    staffPassword: 'faisco1234',
                    url: 'http://i.flw.com/',
                    note: 'aid: ********'
                },
                {
                    id: 2,
                    platform: 'OEM',
                    environment: '线上',
                    username: 'testmall6',
                    password: 'faisco1234',
                    staffUsername: 'fuji',
                    staffPassword: 'faisco1234',
                    url: 'http://adm.webportal.top/',
                    note: 'aid: ********'
                },
                {
                    id: 3,
                    platform: '直销',
                    environment: 'dep',
                    username: 'huaedu1',
                    password: 'faisco1234',
                    staffUsername: 'boss',
                    staffPassword: 'faisco1234',
                    url: 'http://i.flw.com.faidev.cc/',
                    note: 'dep环境测试账号'
                },
                {
                    id: 4,
                    platform: '直销',
                    environment: 'dep',
                    username: 'la19281',
                    password: 'faker0507',
                    staffUsername: '',
                    staffPassword: '',
                    url: 'http://i.flw.com.faidev.cc/',
                    note: '测试账号la19281'
                }
            ];

            // 初始化数据
            useEffect(() => {
                const savedAccounts = localStorage.getItem('accountManager');
                if (savedAccounts) {
                    setAccounts(JSON.parse(savedAccounts));
                } else {
                    setAccounts(defaultAccounts);
                    localStorage.setItem('accountManager', JSON.stringify(defaultAccounts));
                }
            }, []);

            // 添加账号
            const addAccount = () => {
                if (!newAccount.username || !newAccount.password) {
                    alert('请填写用户名和密码');
                    return;
                }

                const account = {
                    ...newAccount,
                    id: Date.now(),
                    url: platforms[newAccount.platform]?.urls?.[newAccount.environment] || ''
                };

                const updatedAccounts = [...accounts, account];
                setAccounts(updatedAccounts);
                localStorage.setItem('accountManager', JSON.stringify(updatedAccounts));

                setNewAccount({
                    platform: '直销',
                    environment: '线上',
                    username: '',
                    password: '',
                    staffUsername: '',
                    staffPassword: '',
                    url: '',
                    note: ''
                });

                alert('账号添加成功');
            };

            // 删除账号
            const deleteAccount = (id) => {
                if (window.confirm('确定要删除这个账号吗？')) {
                    const updatedAccounts = accounts.filter(account => account.id !== id);
                    setAccounts(updatedAccounts);
                    localStorage.setItem('accountManager', JSON.stringify(updatedAccounts));
                }
            };

            // 快速登录（自动登录）
            const quickLogin = async (account) => {
                if (!account.url) {
                    alert('该账号没有配置网址');
                    return;
                }

                try {
                    // 打开新窗口
                    const newWindow = window.open(account.url, '_blank');

                    if (!newWindow) {
                        alert('无法打开新窗口，请检查浏览器弹窗设置');
                        return;
                    }

                    // 等待页面加载
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 注入自动登录脚本到新窗口
                    const script = `
                        // 自动登录脚本
                        (function() {
                            const account = ${JSON.stringify(account)};

                            // 等待页面完全加载
                            if (document.readyState === 'loading') {
                                document.addEventListener('DOMContentLoaded', performAutoLogin);
                            } else {
                                performAutoLogin();
                            }

                            async function performAutoLogin() {
                                try {
                                    console.log('开始自动登录...', account);

                                    // 等待页面元素加载
                                    await waitForPageLoad();

                                    const result = await executeLogin(account);

                                    if (result.success) {
                                        console.log('自动登录成功:', result.message);
                                        showNotification('自动登录成功', 'success');
                                    } else {
                                        console.warn('自动登录失败:', result.message);
                                        showNotification('自动登录失败: ' + result.message, 'error');
                                    }
                                } catch (error) {
                                    console.error('自动登录出错:', error);
                                    showNotification('自动登录出错: ' + error.message, 'error');
                                }
                            }

                            async function executeLogin(account) {
                                const { platform, username, password, staffUsername, staffPassword } = account;

                                switch (platform) {
                                    case '直销':
                                    case 'OEM':
                                        return await loginFKW(account);
                                    case '代理商平台':
                                        return await loginAgency(account);
                                    case 'OSS':
                                        return await loginOSS(account);
                                    default:
                                        return { success: false, message: '不支持的平台: ' + platform };
                                }
                            }

                            async function waitForPageLoad() {
                                return new Promise((resolve) => {
                                    if (document.readyState === 'complete') {
                                        setTimeout(resolve, 1000);
                                    } else {
                                        window.addEventListener('load', () => {
                                            setTimeout(resolve, 1000);
                                        });
                                    }
                                });
                            }

                            async function waitForElement(selector, timeout = 5000) {
                                return new Promise((resolve, reject) => {
                                    const startTime = Date.now();

                                    const checkElement = () => {
                                        const element = document.querySelector(selector);
                                        if (element) {
                                            resolve(element);
                                        } else if (Date.now() - startTime > timeout) {
                                            reject(new Error('Element not found: ' + selector));
                                        } else {
                                            setTimeout(checkElement, 100);
                                        }
                                    };

                                    checkElement();
                                });
                            }

                            async function fillInput(selector, value) {
                                try {
                                    const element = await waitForElement(selector, 3000);
                                    if (element && value) {
                                        // 清空现有内容
                                        element.value = '';
                                        element.focus();

                                        // 模拟逐字符输入
                                        for (let i = 0; i < value.length; i++) {
                                            element.value += value[i];
                                            element.dispatchEvent(new Event('input', { bubbles: true }));
                                            await new Promise(resolve => setTimeout(resolve, 50));
                                        }

                                        // 触发change事件
                                        element.dispatchEvent(new Event('change', { bubbles: true }));
                                        element.dispatchEvent(new Event('blur', { bubbles: true }));

                                        console.log('输入完成:', selector, '值:', value);
                                        return true;
                                    }
                                } catch (error) {
                                    console.warn('Failed to fill input:', selector, error);
                                    return false;
                                }
                            }

                            async function clickElement(selector) {
                                try {
                                    const element = await waitForElement(selector, 3000);
                                    if (element) {
                                        element.click();
                                        return true;
                                    }
                                } catch (error) {
                                    console.warn('Failed to click element:', selector, error);
                                    return false;
                                }
                            }

                            async function loginFKW(account) {
                                const { username, password, staffUsername, staffPassword } = account;

                                try {
                                    console.log('开始直销平台登录，账号信息:', { username, staffUsername });

                                    // 等待页面完全加载
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // 根据开发者工具中看到的实际元素进行定位
                                    const accountInput = document.querySelector('input[type="text"]') ||
                                                        document.querySelector('input[placeholder*="faisco"]') ||
                                                        document.querySelector('#loginAcct');

                                    const passwordInput = document.querySelector('input[type="password"]') ||
                                                         document.querySelector('#log-pwd');

                                    // 查找员工勾选框 - 根据页面结构查找checkbox
                                    const staffCheckbox = document.querySelector('input[type="checkbox"]') ||
                                                         document.querySelector('#staffLogin');

                                    // 查找登录按钮 - 蓝色的"登录"按钮
                                    const loginButton = document.querySelector('button[type="submit"]') ||
                                                       document.querySelector('.login-btn') ||
                                                       document.querySelector('#login-button') ||
                                                       document.querySelector('button');

                                    console.log('找到的元素:', {
                                        accountInput: !!accountInput,
                                        passwordInput: !!passwordInput,
                                        staffCheckbox: !!staffCheckbox,
                                        loginButton: !!loginButton
                                    });

                                    if (!accountInput) {
                                        return { success: false, message: '未找到账号输入框' };
                                    }

                                    if (!passwordInput) {
                                        return { success: false, message: '未找到密码输入框' };
                                    }

                                    // 首先确保员工勾选框未选中（默认不勾选成员账号）
                                    if (staffCheckbox && staffCheckbox.checked) {
                                        staffCheckbox.click();
                                        console.log('已取消员工登录勾选');
                                        await new Promise(resolve => setTimeout(resolve, 300));
                                    }

                                    // 输入主账号
                                    console.log('开始输入账号:', username);
                                    await fillInputAdvanced(accountInput, username);
                                    console.log('账号输入完成:', username);

                                    if (staffUsername === '' || staffUsername === 'boss' || !staffUsername) {
                                        // 普通登录模式
                                        console.log('使用普通登录模式');
                                        console.log('开始输入密码:', password);
                                        await fillInputAdvanced(passwordInput, password);
                                        console.log('密码输入完成');
                                    } else {
                                        // 员工登录模式
                                        console.log('使用员工登录模式:', staffUsername);

                                        // 勾选员工登录
                                        if (staffCheckbox) {
                                            staffCheckbox.click();
                                            console.log('员工登录勾选成功');
                                            await new Promise(resolve => setTimeout(resolve, 500)); // 等待员工输入框出现

                                            // 查找员工账号输入框
                                            const staffInput = document.querySelector('#loginSacct') ||
                                                             document.querySelector('input[placeholder*="员工"]');

                                            if (staffInput) {
                                                await fillInputAdvanced(staffInput, staffUsername);
                                                console.log('员工账号输入完成:', staffUsername);
                                            }
                                        }

                                        // 输入员工密码
                                        const usePassword = staffPassword && staffPassword.trim() !== '' ? staffPassword : password;
                                        console.log('开始输入员工密码:', usePassword);
                                        await fillInputAdvanced(passwordInput, usePassword);
                                        console.log('员工密码输入完成');
                                    }

                                    // 等待一下再点击登录按钮
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // 点击登录按钮
                                    if (loginButton) {
                                        console.log('准备点击登录按钮');
                                        loginButton.click();
                                        console.log('登录按钮点击成功');
                                    } else {
                                        // 如果找不到按钮，尝试其他方式
                                        const allButtons = document.querySelectorAll('button');
                                        console.log('找到的所有按钮:', allButtons.length);

                                        for (let i = 0; i < allButtons.length; i++) {
                                            const btn = allButtons[i];
                                            if (btn.textContent.includes('登录') || btn.textContent.includes('Login')) {
                                                btn.click();
                                                console.log('通过文本匹配点击登录按钮');
                                                break;
                                            }
                                        }

                                        if (allButtons.length === 0) {
                                            return { success: false, message: '未找到登录按钮' };
                                        }
                                    }

                                    setTimeout(closePopups, 3000);

                                    return { success: true, message: '登录请求已发送' };
                                } catch (error) {
                                    console.error('登录过程出错:', error);
                                    return { success: false, message: '登录失败: ' + error.message };
                                }
                            }

                            // 增强的输入函数
                            async function fillInputAdvanced(element, text) {
                                if (!element || !text) {
                                    console.warn('元素或文本为空，跳过输入');
                                    return false;
                                }

                                console.log('开始模拟输入:', text);

                                // 聚焦元素
                                element.focus();
                                element.click();

                                // 清空现有内容
                                element.value = '';
                                element.dispatchEvent(new Event('input', { bubbles: true }));

                                // 等待一下
                                await new Promise(resolve => setTimeout(resolve, 200));

                                // 逐字符输入
                                for (let i = 0; i < text.length; i++) {
                                    element.value += text[i];

                                    // 触发多种事件确保兼容性
                                    element.dispatchEvent(new Event('input', { bubbles: true }));
                                    element.dispatchEvent(new Event('keydown', { bubbles: true }));
                                    element.dispatchEvent(new Event('keyup', { bubbles: true }));

                                    await new Promise(resolve => setTimeout(resolve, 80)); // 每个字符间隔80ms
                                }

                                // 最终触发change和blur事件
                                element.dispatchEvent(new Event('change', { bubbles: true }));
                                element.dispatchEvent(new Event('blur', { bubbles: true }));

                                console.log('输入完成，当前值:', element.value);
                                return true;
                            }

                            async function loginAgency(account) {
                                const { username, password } = account;

                                try {
                                    const agencyTypeSelectors = [
                                        'input[value="agency"]',
                                        '.agency-login'
                                    ];

                                    for (const selector of agencyTypeSelectors) {
                                        if (await clickElement(selector)) break;
                                    }

                                    await fillInput('input[name="username"], input[placeholder*="代理商"]', username);
                                    await fillInput('input[name="password"], input[type="password"]', password);
                                    await clickElement('button[type="submit"], .login-btn');

                                    return { success: true, message: '登录请求已发送' };
                                } catch (error) {
                                    return { success: false, message: '登录失败: ' + error.message };
                                }
                            }

                            async function loginOSS(account) {
                                const { username, password } = account;

                                try {
                                    await fillInput('input[name="username"], input[placeholder*="用户名"]', username);
                                    await fillInput('input[name="password"], input[type="password"]', password);
                                    await clickElement('button[type="submit"], .login-btn');

                                    return { success: true, message: '登录请求已发送' };
                                } catch (error) {
                                    return { success: false, message: '登录失败: ' + error.message };
                                }
                            }

                            function closePopups() {
                                const popupSelectors = [
                                    '.close', '.btnClose', '.popup-close',
                                    '[class*="close"]', '[class*="modal"] .close',
                                    '.layui-layer-close', '.el-dialog__close'
                                ];

                                popupSelectors.forEach(selector => {
                                    const elements = document.querySelectorAll(selector);
                                    elements.forEach(element => {
                                        if (element.offsetParent !== null) {
                                            element.click();
                                        }
                                    });
                                });
                            }

                            function showNotification(message, type = 'info') {
                                const notification = document.createElement('div');
                                notification.style.cssText = \`
                                    position: fixed;
                                    top: 20px;
                                    right: 20px;
                                    padding: 12px 20px;
                                    border-radius: 4px;
                                    color: white;
                                    font-size: 14px;
                                    z-index: 10000;
                                    max-width: 300px;
                                    word-wrap: break-word;
                                    background-color: \${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
                                \`;
                                notification.textContent = message;

                                document.body.appendChild(notification);

                                setTimeout(() => {
                                    if (notification.parentNode) {
                                        notification.parentNode.removeChild(notification);
                                    }
                                }, 3000);
                            }
                        })();
                    `;

                    // 尝试注入脚本
                    try {
                        newWindow.eval(script);
                    } catch (error) {
                        console.warn('无法注入自动登录脚本，可能是跨域限制:', error);
                        alert('已打开登录页面，请手动输入账号密码登录');
                    }

                } catch (error) {
                    console.error('快速登录出错:', error);
                    alert('快速登录失败: ' + error.message);
                }
            };

            // 复制账号信息
            const copyAccountInfo = (account) => {
                const info = `平台: ${account.platform}
环境: ${account.environment}
用户名: ${account.username}
密码: ${account.password}
员工账号: ${account.staffUsername}
员工密码: ${account.staffPassword}
网址: ${account.url}
备注: ${account.note}`;

                navigator.clipboard.writeText(info);
                alert('账号信息已复制到剪贴板');
            };

            return (
                <div className="tool-section">
                    <h2 className="text-2xl font-bold text-gray-800 mb-6">常用账号登录管理</h2>

                    {/* 标签页导航 */}
                    <div className="flex gap-2 mb-6 border-b border-gray-200">
                        <button
                            onClick={() => setActiveTab('manager')}
                            className={`tab-button ${activeTab === 'manager' ? 'active' : 'inactive'}`}
                        >
                            账号管理
                        </button>
                        <button
                            onClick={() => setActiveTab('add')}
                            className={`tab-button ${activeTab === 'add' ? 'active' : 'inactive'}`}
                        >
                            添加账号
                        </button>
                    </div>

                    {/* 账号管理 */}
                    {activeTab === 'manager' && (
                        <div className="space-y-4">
                            <div className="text-sm text-gray-600 mb-4">
                                共有 {accounts.length} 个账号
                            </div>

                            {accounts.map(account => (
                                <div key={account.id} className="bg-white border border-gray-200 rounded-lg p-4">
                                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                                        <div>
                                            <div className="font-medium text-gray-800">{account.platform}</div>
                                            <div className="text-sm text-gray-500">{account.environment}</div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600">用户名</div>
                                            <div className="font-medium">{account.username}</div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600">密码</div>
                                            <div className="font-medium text-xs">{account.password}</div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600">员工账号</div>
                                            <div className="font-medium">{account.staffUsername || '-'}</div>
                                        </div>

                                        <div>
                                            <div className="text-sm text-gray-600">备注</div>
                                            <div className="text-sm text-gray-700">{account.note || '-'}</div>
                                        </div>

                                        <div className="flex gap-2">
                                            <button
                                                onClick={() => quickLogin(account)}
                                                className="btn-primary text-xs px-3 py-1"
                                                title="自动填写账号密码并登录"
                                            >
                                                自动登录
                                            </button>
                                            <button
                                                onClick={() => copyAccountInfo(account)}
                                                className="btn-secondary text-xs px-3 py-1"
                                            >
                                                复制
                                            </button>
                                            <button
                                                onClick={() => deleteAccount(account.id)}
                                                className="btn-danger text-xs px-3 py-1"
                                            >
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {accounts.length === 0 && (
                                <div className="text-center py-12 text-gray-500">
                                    暂无账号，请添加账号
                                </div>
                            )}
                        </div>
                    )}

                    {/* 添加账号 */}
                    {activeTab === 'add' && (
                        <div className="max-w-2xl space-y-4">
                            <h3 className="text-lg font-medium text-gray-800">添加新账号</h3>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">平台：</label>
                                    <select
                                        value={newAccount.platform}
                                        onChange={(e) => setNewAccount(prev => ({
                                            ...prev,
                                            platform: e.target.value,
                                            environment: platforms[e.target.value]?.environments?.[0] || '线上',
                                            url: platforms[e.target.value]?.urls?.[platforms[e.target.value]?.environments?.[0]] || ''
                                        }))}
                                        className="select-field"
                                    >
                                        {Object.keys(platforms).map(platform => (
                                            <option key={platform} value={platform}>{platform}</option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">环境：</label>
                                    <select
                                        value={newAccount.environment}
                                        onChange={(e) => setNewAccount(prev => ({
                                            ...prev,
                                            environment: e.target.value,
                                            url: platforms[prev.platform]?.urls?.[e.target.value] || ''
                                        }))}
                                        className="select-field"
                                    >
                                        {platforms[newAccount.platform]?.environments?.map(env => (
                                            <option key={env} value={env}>{env}</option>
                                        ))}
                                    </select>
                                </div>



                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">用户名：*</label>
                                    <input
                                        type="text"
                                        value={newAccount.username}
                                        onChange={(e) => setNewAccount(prev => ({ ...prev, username: e.target.value }))}
                                        className="input-field"
                                        placeholder="请输入用户名"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">密码：*</label>
                                    <input
                                        type="password"
                                        value={newAccount.password}
                                        onChange={(e) => setNewAccount(prev => ({ ...prev, password: e.target.value }))}
                                        className="input-field"
                                        placeholder="请输入密码"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">员工账号：</label>
                                    <input
                                        type="text"
                                        value={newAccount.staffUsername}
                                        onChange={(e) => setNewAccount(prev => ({ ...prev, staffUsername: e.target.value }))}
                                        className="input-field"
                                        placeholder="请输入员工账号"
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">员工密码：</label>
                                    <input
                                        type="password"
                                        value={newAccount.staffPassword}
                                        onChange={(e) => setNewAccount(prev => ({ ...prev, staffPassword: e.target.value }))}
                                        className="input-field"
                                        placeholder="请输入员工密码"
                                    />
                                </div>

                                <div className="md:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">备注：</label>
                                    <textarea
                                        value={newAccount.note}
                                        onChange={(e) => setNewAccount(prev => ({ ...prev, note: e.target.value }))}
                                        className="textarea-field h-20"
                                        placeholder="请输入备注信息，如aid等"
                                    />
                                </div>
                            </div>

                            <div className="flex justify-center">
                                <button onClick={addAccount} className="btn-primary px-8">
                                    添加账号
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // 主应用组件
        const App = () => {
            const [activeCategory, setActiveCategory] = useState('数据生成工具');

            const categories = [
                { id: 'data-generator', name: '数据生成工具', component: DataGenerator },
                { id: 'text-counter', name: '字数统计', component: TextCounter },
                { id: 'random-questions', name: '随机题目', component: RandomQuestions },
                { id: 'account-manager', name: '账号管理', component: AccountManager },
                { id: 'encoding-tools', name: '编码/加密工具', component: EncodingTools }
            ];

            const activeComponent = categories.find(cat => cat.name === activeCategory)?.component;

            return (
                <div className="min-h-screen bg-gray-50">
                    <Header />
                    <main className="container mx-auto px-4 py-8">
                        <div className="text-center mb-8">
                            <h1 className="text-4xl font-bold text-gray-800 mb-2">测试工程师常用工具</h1>
                            <p className="text-xl text-gray-600">一站式数据生成和处理工具集</p>
                        </div>
                        
                        <CategoryNav 
                            categories={categories}
                            activeCategory={activeCategory}
                            onCategoryChange={setActiveCategory}
                        />
                        
                        <div className="max-w-6xl mx-auto">
                            <div className="tool-card">
                                {activeComponent && React.createElement(activeComponent)}
                            </div>
                        </div>
                    </main>
                </div>
            );
        };

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
