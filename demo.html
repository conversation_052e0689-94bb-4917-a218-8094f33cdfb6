<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>测试工具集 - 演示版</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f8fafc;
        }
        
        .tool-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            padding: 1.5rem;
            transition: box-shadow 0.2s;
        }
        
        .tool-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background-color: #2563eb;
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .input-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
        }
        
        .input-field:focus {
            ring: 2px;
            ring-color: #3b82f6;
            border-color: transparent;
        }
        
        .textarea-field {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            outline: none;
            resize: vertical;
        }
        
        .result-area {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            padding: 1rem;
            min-height: 100px;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .tab-button {
            padding: 0.5rem 1rem;
            font-weight: 500;
            font-size: 0.875rem;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
            border-bottom: 2px solid;
            transition: all 0.2s;
            cursor: pointer;
            background: none;
            border-left: none;
            border-right: none;
            border-top: none;
        }
        
        .tab-button.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            background-color: #eff6ff;
        }
        
        .tab-button.inactive {
            color: #6b7280;
            border-bottom-color: transparent;
        }
        
        .tab-button.inactive:hover {
            color: #374151;
            border-bottom-color: #d1d5db;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Header组件
        const Header = () => {
            return (
                <header className="bg-white shadow-sm border-b border-gray-200">
                    <div className="container mx-auto px-4 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <h1 className="text-2xl font-bold text-gray-800">Raina测试工具集</h1>
                            </div>
                            <nav className="flex items-center space-x-6">
                                <a href="#manual" className="flex items-center text-gray-600 hover:text-blue-600 transition-colors duration-200">
                                    <span className="mr-2">📖</span>
                                    使用手册
                                </a>
                            </nav>
                        </div>
                    </div>
                </header>
            );
        };

        // CategoryNav组件
        const CategoryNav = ({ categories, activeCategory, onCategoryChange }) => {
            return (
                <div className="mb-8">
                    <div className="flex flex-wrap gap-3 justify-center">
                        {categories.map((category) => (
                            <button
                                key={category.id}
                                onClick={() => onCategoryChange(category.name)}
                                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                                    activeCategory === category.name
                                        ? 'bg-blue-600 text-white shadow-md transform scale-105'
                                        : 'bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 shadow-sm border border-gray-200'
                                }`}
                            >
                                {category.name}
                            </button>
                        ))}
                    </div>
                </div>
            );
        };

        // 简化的数据生成工具组件
        const DataGenerator = () => {
            const [activeTab, setActiveTab] = useState('random-content');
            const [inputText, setInputText] = useState('');
            const [outputText, setOutputText] = useState('');
            const [wordCount, setWordCount] = useState(100);

            const generateRandomContent = () => {
                const sampleTexts = [
                    'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                    '这是一个示例文章段落，包含了丰富的内容和信息。',
                    '从前有一个小村庄，村庄里住着一位善良的老人。',
                    '本文档介绍了系统的架构设计和实现方案。'
                ];
                
                let result = '';
                for (let i = 0; i < wordCount / 10; i++) {
                    result += sampleTexts[Math.floor(Math.random() * sampleTexts.length)] + ' ';
                }
                setOutputText(result.trim());
            };

            const generateEmails = () => {
                const domains = ['gmail.com', '163.com', 'qq.com', 'hotmail.com'];
                const emails = [];
                for (let i = 0; i < 10; i++) {
                    const username = `user${Math.floor(Math.random() * 10000)}`;
                    const domain = domains[Math.floor(Math.random() * domains.length)];
                    emails.push(`${username}@${domain}`);
                }
                setOutputText(emails.join('\n'));
            };

            const tabs = [
                { id: 'random-content', name: '随机内容生成器' },
                { id: 'email-generator', name: '随机邮箱生成器' }
            ];

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">数据生成工具</h2>
                    
                    <div className="flex gap-2 border-b border-gray-200">
                        {tabs.map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`tab-button ${activeTab === tab.id ? 'active' : 'inactive'}`}
                            >
                                {tab.name}
                            </button>
                        ))}
                    </div>

                    {activeTab === 'random-content' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">生成字数：</label>
                                    <input
                                        type="number"
                                        value={wordCount}
                                        onChange={(e) => setWordCount(parseInt(e.target.value) || 100)}
                                        className="input-field"
                                        min="1"
                                        max="10000"
                                    />
                                </div>
                                
                                <button onClick={generateRandomContent} className="btn-primary w-full">
                                    生成内容
                                </button>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
                                <textarea
                                    value={outputText}
                                    readOnly
                                    className="result-area h-64"
                                    placeholder="生成的内容将在这里显示..."
                                />
                            </div>
                        </div>
                    )}

                    {activeTab === 'email-generator' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="space-y-4">
                                <button onClick={generateEmails} className="btn-primary w-full">
                                    生成邮箱
                                </button>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">生成结果：</label>
                                <textarea
                                    value={outputText}
                                    readOnly
                                    className="result-area h-64"
                                    placeholder="生成的邮箱将在这里显示..."
                                />
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // 简化的字数统计工具
        const TextCounter = () => {
            const [text, setText] = useState('');
            const [stats, setStats] = useState({
                charactersWithSpaces: 0,
                charactersWithoutSpaces: 0,
                words: 0,
                lines: 0
            });

            React.useEffect(() => {
                const charactersWithSpaces = text.length;
                const charactersWithoutSpaces = text.replace(/\s/g, '').length;
                const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
                const lines = text === '' ? 0 : text.split('\n').length;

                setStats({
                    charactersWithSpaces,
                    charactersWithoutSpaces,
                    words,
                    lines
                });
            }, [text]);

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">字数统计工具</h2>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-2">输入文本：</label>
                            <textarea
                                value={text}
                                onChange={(e) => setText(e.target.value)}
                                className="textarea-field h-96"
                                placeholder="请在此输入需要统计的文本内容..."
                            />
                        </div>
                        
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-gray-800">统计结果</h3>
                            
                            <div className="space-y-3">
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="text-sm text-blue-600 font-medium">字符数（含空格）</div>
                                    <div className="text-2xl font-bold text-blue-800">{stats.charactersWithSpaces}</div>
                                </div>
                                
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div className="text-sm text-green-600 font-medium">字符数（不含空格）</div>
                                    <div className="text-2xl font-bold text-green-800">{stats.charactersWithoutSpaces}</div>
                                </div>
                                
                                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                    <div className="text-sm text-purple-600 font-medium">单词数</div>
                                    <div className="text-2xl font-bold text-purple-800">{stats.words}</div>
                                </div>
                                
                                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                    <div className="text-sm text-orange-600 font-medium">行数</div>
                                    <div className="text-2xl font-bold text-orange-800">{stats.lines}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // 简化的随机题目工具
        const RandomQuestions = () => {
            const [questionBank, setQuestionBank] = useState('');
            const [generatedQuestions, setGeneratedQuestions] = useState([]);
            const [questionCount, setQuestionCount] = useState(5);

            const sampleQuestions = `# 单选题
1. JavaScript是一种什么类型的语言？
A. 编译型语言
B. 解释型语言
C. 汇编语言
D. 机器语言
答案：B

2. 以下哪个不是JavaScript的数据类型？
A. string
B. number
C. char
D. boolean
答案：C

# 判断题
3. JavaScript是区分大小写的语言。
答案：正确

4. HTML是一种编程语言。
答案：错误

# 填空题
5. JavaScript中声明变量使用关键字____或____。
答案：var|let`;

            const parseQuestions = (text) => {
                const questions = [];
                const lines = text.split('\n').filter(line => line.trim());
                let currentType = '';
                let currentQuestion = null;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();

                    if (line.startsWith('#')) {
                        currentType = line.replace('#', '').trim();
                        continue;
                    }

                    if (/^\d+\./.test(line)) {
                        if (currentQuestion) {
                            questions.push(currentQuestion);
                        }

                        currentQuestion = {
                            id: questions.length + 1,
                            type: currentType,
                            question: line.replace(/^\d+\.\s*/, ''),
                            options: [],
                            answer: ''
                        };
                    }
                    else if (/^[A-Z]\./.test(line) && currentQuestion) {
                        currentQuestion.options.push(line);
                    }
                    else if (line.startsWith('答案：') && currentQuestion) {
                        currentQuestion.answer = line.replace('答案：', '');
                    }
                }

                if (currentQuestion) {
                    questions.push(currentQuestion);
                }

                return questions;
            };

            const generateQuestions = () => {
                if (!questionBank.trim()) {
                    alert('请先输入题目库内容');
                    return;
                }

                const allQuestions = parseQuestions(questionBank);
                if (allQuestions.length === 0) {
                    alert('未能解析到有效题目');
                    return;
                }

                const shuffled = allQuestions.sort(() => Math.random() - 0.5);
                const selected = shuffled.slice(0, Math.min(questionCount, shuffled.length));
                setGeneratedQuestions(selected);
            };

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">随机题目生成工具</h2>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                            <div className="flex items-center justify-between mb-2">
                                <label className="text-lg font-medium text-gray-800">题目库内容：</label>
                                <button
                                    onClick={() => setQuestionBank(sampleQuestions)}
                                    className="text-sm btn-secondary"
                                >
                                    加载示例
                                </button>
                            </div>
                            <textarea
                                value={questionBank}
                                onChange={(e) => setQuestionBank(e.target.value)}
                                className="textarea-field h-64"
                                placeholder="请按照格式输入题目内容，或点击'加载示例'..."
                            />
                        </div>

                        <div className="space-y-4">
                            <h3 className="text-lg font-medium text-gray-800">生成设置</h3>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">题目数量：</label>
                                <input
                                    type="number"
                                    value={questionCount}
                                    onChange={(e) => setQuestionCount(parseInt(e.target.value) || 5)}
                                    className="input-field"
                                    min="1"
                                    max="20"
                                />
                            </div>

                            <button onClick={generateQuestions} className="btn-primary w-full">
                                生成随机题目
                            </button>
                        </div>
                    </div>

                    {generatedQuestions.length > 0 && (
                        <div className="border-t border-gray-200 pt-6">
                            <h3 className="text-lg font-medium text-gray-800 mb-4">生成的题目</h3>
                            <div className="space-y-4">
                                {generatedQuestions.map((question, index) => (
                                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <span className="text-sm font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                                {question.type}
                                            </span>
                                            <span className="text-sm text-gray-500">第 {index + 1} 题</span>
                                        </div>

                                        <h4 className="font-medium text-gray-800 mb-2">
                                            {index + 1}. {question.question}
                                        </h4>

                                        {question.options.length > 0 && (
                                            <div className="mb-2">
                                                {question.options.map((option, optIndex) => (
                                                    <div key={optIndex} className="text-sm text-gray-700">
                                                        {option}
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        <div className="text-sm border-t border-gray-100 pt-2">
                                            <span className="font-medium text-green-600">答案：</span>
                                            <span className="text-green-700">{question.answer}</span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // 简化的Base64编码工具
        const EncodingTools = () => {
            const [inputText, setInputText] = useState('');
            const [outputText, setOutputText] = useState('');

            const base64Encode = () => {
                try {
                    const encoded = btoa(unescape(encodeURIComponent(inputText)));
                    setOutputText(encoded);
                } catch (error) {
                    setOutputText('编码失败：' + error.message);
                }
            };

            const base64Decode = () => {
                try {
                    const decoded = decodeURIComponent(escape(atob(inputText)));
                    setOutputText(decoded);
                } catch (error) {
                    setOutputText('解码失败：' + error.message);
                }
            };

            return (
                <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-gray-800">编码/加密工具</h2>
                    
                    <div>
                        <h3 className="text-lg font-medium text-gray-800 mb-3">Base64编码</h3>
                        <textarea
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                            className="textarea-field h-32 mb-4"
                            placeholder="输入要处理的文本..."
                        />
                        
                        <div className="flex gap-3 mb-4">
                            <button onClick={base64Encode} className="btn-primary">Base64编码</button>
                            <button onClick={base64Decode} className="btn-primary">Base64解码</button>
                            <button onClick={() => { setInputText(''); setOutputText(''); }} className="btn-secondary">清空</button>
                        </div>
                        
                        <div>
                            <h4 className="text-md font-medium text-gray-700 mb-2">输出结果</h4>
                            <textarea
                                value={outputText}
                                readOnly
                                className="result-area h-32"
                                placeholder="处理结果将在这里显示..."
                            />
                        </div>
                    </div>
                </div>
            );
        };

        // 主应用组件
        const App = () => {
            const [activeCategory, setActiveCategory] = useState('数据生成工具');

            const categories = [
                { id: 'data-generator', name: '数据生成工具', component: DataGenerator },
                { id: 'text-counter', name: '字数统计', component: TextCounter },
                { id: 'random-questions', name: '随机题目', component: RandomQuestions },
                { id: 'encoding-tools', name: '编码/加密工具', component: EncodingTools }
            ];

            const activeComponent = categories.find(cat => cat.name === activeCategory)?.component;

            return (
                <div className="min-h-screen bg-gray-50">
                    <Header />
                    <main className="container mx-auto px-4 py-8">
                        <div className="text-center mb-8">
                            <h1 className="text-4xl font-bold text-gray-800 mb-2">测试工程师常用工具</h1>
                            <p className="text-xl text-gray-600">一站式数据生成和处理工具集</p>
                        </div>
                        
                        <CategoryNav 
                            categories={categories}
                            activeCategory={activeCategory}
                            onCategoryChange={setActiveCategory}
                        />
                        
                        <div className="max-w-6xl mx-auto">
                            <div className="tool-card">
                                {activeComponent && React.createElement(activeComponent)}
                            </div>
                        </div>
                    </main>
                </div>
            );
        };

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
